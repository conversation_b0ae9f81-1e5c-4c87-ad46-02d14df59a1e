<?php

use Authentication\User\UserOrders;
use Buxus\Util\PageIds;
use Buxus\Page\PageInterface;
use App\DeliveryAddress;
use Buxus\Util\Url;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Collection;

class UserController extends \Authentication\Controllers\UserController
{
    public function returnsAction(PageInterface $page, \Illuminate\Http\Request $request)
    {
        if (\WebUserAuthentication::isAuthenticated()) {
            $form = new \App\Form\ReturnsForm();

            $form->actionHandler($request);
            $this->view->page = $page;
            $elements = $form->getElements();
            $this->view->elements = $elements;
            $this->view->form = $form;

            $userId = \WebUserAuthentication::getUser()->getUserId();

            $forms = \DB::table('tblSubmitedForms')
                ->where('received_properties', 'like', "%<webuser_id>{$userId}</webuser_id>%")
                ->where('form_type_tag', \App\Form\ReturnsForm::FORM_TYPE_TAG)
                ->orderBy('form_submit_time', 'DESC')
                ->get();

            foreach ($forms as $form) {
                $this->parseData($form);
            }

            $this->view->submitted = $forms;
        } else {
            return redirect(\Buxus\Util\Url::page(PageIds::getAuthLogin()));
        }
    }

    public function complaintsAction(PageInterface $page, \Illuminate\Http\Request $request)
    {
        if (\WebUserAuthentication::isAuthenticated()) {
            $form = new \App\Form\ComplaintsForm();

            $form->actionHandler($request);
            $this->view->page = $page;
            $elements = $form->getElements();
            $this->view->elements = $elements;
            $this->view->form = $form;

            $userId = \WebUserAuthentication::getUser()->getUserId();

            $forms = \DB::table('tblSubmitedForms')
                ->where('received_properties', 'like', "%<webuser_id>{$userId}</webuser_id>%")
                ->where('form_type_tag', \App\Form\ComplaintsForm::FORM_TYPE_TAG)
                ->orderBy('form_submit_time', 'DESC')
                ->get();

            foreach ($forms as $form) {
                $this->parseData($form);
            }

            $this->view->submitted = $forms;
        } else {
            return redirect(\Buxus\Util\Url::page(PageIds::getAuthLogin()));
        }
    }

    protected function parseData($form)
    {
        $xml = '<?xml version="1.0"?><submitted_form>';
        $xml .= trim(str_replace("\n", "", $form->received_properties));
        $xml .= '</submitted_form>';
        $form->parsed_properties = simplexml_load_string($xml);
    }

    public function addressesAction(PageInterface $page)
    {
        if (\WebUserAuthentication::isAuthenticated()) {
            $userId = \WebUserAuthentication::getUserId();
            $addresses = DeliveryAddress::where('webuser_id', $userId)->get();

            $this->view->addresses = $addresses;
            $this->view->page = $page;
        } else {
            return redirect(\Buxus\Util\Url::page(PageIds::getAuthLogin()));
        }
    }

    public function ordersAction(PageInterface $page, Request $request)
    {
        $order_id = $request->get('order');

        if (empty($order_id)) {
            $this->view->list = new UserOrders(\WebUserAuthentication::getUserId());
            $this->view->list = $this->paginate($this->view->list->getItems(), 16, null, [
                'path' => Url::page(\BuxusMVC::pageId()),
            ]);


        } else {
            try {
                $order = \OrderFactory::getById($order_id);

                if ($order->getUserId() != \WebUserAuthentication::getUserId()) {
                    throw new \Exception('No this users order');
                }

                $this->view->detail = $order;
            } catch (\Exception $e) {
                $this->redirect(Url::page(\BuxusMVC::pageId()));
            }
        }

        $this->view->page = $page;
    }

    protected function paginate($items, $perPage = 15, $page = null, $options = [])
    {
        $page = $page ?: (Paginator::resolveCurrentPage() ?: 1);
        $items = $items instanceof Collection ? $items : Collection::make($items);
        return new LengthAwarePaginator($items->forPage($page, $perPage), $items->count(), $perPage, $page, $options);
    }
}
