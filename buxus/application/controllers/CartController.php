<?php

class CartController extends \Eshop\ShoppingCart\Controller\CartController
{
    public function shoppingCartAction()
    {
        if (\WebUserAuthentication::isAuthenticated()) {
            return parent::shoppingCartAction();
        }
        return redirect(\Buxus\Util\Url::page(\Buxus\Util\PageIds::getHomepage()));
    }

    public function transportPaymentAction(\Buxus\Eshop\Contracts\ShoppingCart $shoppingCart)
    {
        if (\WebUserAuthentication::isAuthenticated()) {
            return parent::transportPaymentAction($shoppingCart);
        }
        return redirect(\Buxus\Util\Url::page(\Buxus\Util\PageIds::getHomepage()));
    }

    public function deliveryDataAction()
    {
        if (\WebUserAuthentication::isAuthenticated()) {
            return parent::deliveryDataAction();
        }
        return redirect(\Buxus\Util\Url::page(\Buxus\Util\PageIds::getHomepage()));
    }

    public function summaryInfoAction()
    {
        if (\WebUserAuthentication::isAuthenticated()) {
            return parent::summaryInfoAction();
        }
        return redirect(\Buxus\Util\Url::page(\Buxus\Util\PageIds::getHomepage()));
    }
}
