<?php
$slider_items = $this->slider_items;
?>

<?php if (isset($slider_items) && is_array($slider_items) && count($slider_items)): ?>
    <section class="cover js-cover homepage-banner-list">
        <div class="cover__container" style="width:98vw;">
            <div class="cover__inner swiper-widget">
                <div class="cover-carousel swiper js-cover-carousel">
                    <div class="cover-carousel__inner swiper-wrapper">
                        <?php foreach ($slider_items as $slider): ?>
                            <div class="cover-carousel__item swiper-slide js-cover-carousel-item <?= !empty($cssClass) ? ' ' . $cssClass : '' ?>">
                                <?= $slider ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <nav class="cover__navigation swiper-navigation swiper-navigation--paginator">

                    <div class="container">
                        <button type="button"
                                class="swiper-button-prev"
                                aria-label="<?= $this->str('common', 'Predchádzajúce') ?>"><
                        </button>

                        <div class="swiper-pagination"></div>

                        <div class="cover__navigation-button-wrapper">
                            <span class="cover__navigation-button-label"></span>
                            <button type="button"
                                    class="swiper-button-next"
                                    aria-label="<?= $this->str('common', 'Nasledujúce') ?>">>
                            </button>
                        </div>
                    </div>
                </nav>
                <nav class="cover__navigation swiper-navigation swiper-navigation--paginator">
                </nav>
            </div>
        </div>
    </section>
    <?php js_begin(); ?>
    <script type="text/javascript">
        require(['carousel'])
    </script>
    <?php js_end(); ?>
<?php endif; ?>
