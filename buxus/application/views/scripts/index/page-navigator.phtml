<?php

use Buxus\Substrate\Menu\MenuItem;
use Buxus\Substrate\Menu\Breadcrumbs;

if (config('krabica.modules.pagenavigator')): ?>
    <?php if (\BuxusMVC::pageId() != Buxus\Util\PageIds::getPageId('homepage')): ?>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item">
                    <a href="/">
                        <svg class="icon">
                            <use xlink:href="#sprite-home"></use>
                        </svg>
                    </a>
                </li>

                <?php foreach ($this->menu as $item): /* @var $item MenuItem */ ?>
                    <?php
                    if (Breadcrumbs::isExcludedPageType($item->getProperties()->getPageTypeId())):
                        continue;
                    endif;
                    ?>
                    <?php if (\BuxusMVC::pageId() == $item->getProperties()->getPageId()): ?>
                        <li class="breadcrumb-item active"
                            aria-current="page"><?= $this->renderProperty($item->getProperties(), \Buxus\Util\PropertyTag::TITLE_TAG) ?></li>
                    <?php else: ?>
                        <li class="breadcrumb-item"><a href="<?= $item->getProperties()->getUrl() ?>"
                                                       title="<?= $this->escape($item->getProperties()->getValue(\Buxus\Util\PropertyTag::TITLE_TAG)) ?>"><?= $this->renderProperty($item->getProperties(), \Buxus\Util\PropertyTag::TITLE_TAG) ?></a>
                        </li>
                    <?php endif; ?>
                <?php endforeach; ?>
            </ol>
        </nav>

    <?php endif; ?>
<?php endif; ?>
