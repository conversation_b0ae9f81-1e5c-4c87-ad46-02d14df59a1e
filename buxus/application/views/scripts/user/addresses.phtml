<?php
/**
 * @var \Buxus\WebUser\WebUser $user
 */
$user = $this->user;
$addresses = $this->addresses;
?>
<div class="container">
    <?= $this->clientZoneTitle() ?>
    <div class="row">
        <?= $this->action('user-menu', $this->controller_name) ?>
        <div class="col-sm-9 col-xs-12">
            <div class="container">
                <div>
                    <div class="delivery-address__heading">
                        <h1>
                            <?= $this->renderEditableProperty($this->page, \Buxus\Util\PropertyTag::TITLE_TAG()) ?>
                        </h1>
                        <div>
                            <button class="btn btn-primary" data-toggle="modal"
                                    data-target="#deliveryAddressModalCreate">
                                <?= \Trans::str('common', 'Nová adresa') ?>
                            </button>
                        </div>
                    </div>
                    <div>
                        <div>
                            <?= $this->page->getValue(\Buxus\Util\PropertyTag::TEXT_TAG()) ?>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <?php foreach ($addresses as $address): ?>
                        <div class="col-xs-12 col-sm-12 col-md-12 col-lg-6">
                            <div class="delivery-address">
                                <table>
                                    <tr>
                                        <td><strong><?= \Trans::str('user', 'Meno') ?></strong></td>
                                        <td><?= $address->fullname; ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong><?= \Trans::str('user', 'Firma') ?></strong></td>
                                        <td><?= $address->company_name; ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong><?= \Trans::str('user', 'Telefón') ?></strong></td>
                                        <td><?= $address->delivery_phone; ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong><?= \Trans::str('user', 'Adresa') ?></strong></td>
                                        <td><?= $address->street; ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong><?= \Trans::str('user', 'Mesto') ?></strong></td>
                                        <td><?= $address->city; ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong><?= \Trans::str('user', 'PSČ') ?></strong></td>
                                        <td><?= $address->zip; ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong><?= \Trans::str('user', 'Krajina') ?></strong></td>
                                        <td><?= $address->country; ?></td>
                                    </tr>
                                </table>

                                <?= app('buxus:widget')
                                    ->run('DeliveryAddressChangeWidget', [
                                        'address' => $address
                                    ]) ?>

                                <div class="delivery-address__actions">
                                    <button class="btn btn-primary" data-toggle="modal"
                                            data-target="#deliveryAddressModal-<?= $address->id ?>">
                                        <?= \Trans::str('common', 'Upraviť') ?>
                                    </button>
                                    <a href="<?= route('delivery-address.delete', $address->id) ?>"
                                       class="btn btn-primary"><img
                                            src="<?= \Buxus\Util\Url::asset('images/bin.svg') ?>"></a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
            <?= app('buxus:widget')->run('DeliveryAddressCreateWidget') ?>
        </div>
    </div>
</div>
