<?php
/** @var Buxus\Eshop\ShoppingCart $shopping_cart */

use App\Country;

$shopping_cart = $this->shopping_cart;

/** @var \Buxus\Eshop\CartExtension\CartExtensionInterface[] $checkout_extensions */
$checkout_extensions = $this->checkout_extensions;

/** @var \Buxus\Eshop\Checkout\Checkout $checkout */
$checkout = $this->checkout;
?>

<div class="container">
    <div class="row">
        <div class="col-xs-12">
            <?= $this->action('cart-navigator', 'cart') ?>
        </div>
    </div>

    <div class="row">
        <div class="col-xs-12">
            <?= $this->formErrorsList($this->process->getForm()->getMessages()); ?>
            <?php if (isset($this->show_error) && !empty($this->show_error)): ?>
                <div class="alert alert-danger" role="alert">
                    <?= $this->escape($this->show_error) ?>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <div class="row">
        <div class="col-xs-12">
            <h2 class="cart_header"><?= $this->str('cart', 'Objednaný tovar') ?></h2>

            <table class="basket_table">
                <thead>
                <tr>
                    <th>
                    </th>
                    <th>
                        <div class="col-md-2 basket_table_product">

                        </div>
                        <div class="col-md-2 ">
                            <?= $this->str('cart', 'Množstvo') ?>
                        </div>
                        <div class="col-md-2 basket_table_variant">
                            <?= $this->str('cart', 'Výrobca') ?>
                        </div>
                        <div class="col-md-2">
                            <?= $this->str('cart', 'Cena za kus') ?>
                        </div>
                        <div class="col-md-2">
                            <?= $this->str('cart', 'Cena celkom bez DPH') ?>
                        </div>
                    </th>
                </tr>
                </thead>
                <tbody>
                <?php foreach ($shopping_cart as $key => $product): /* @var $item App\Eshop\Product */

                    if ($product instanceof \Buxus\Eshop\Item\RenderableShopItemInterface && !$product instanceof Buxus\Vouchers\VoucherCartItem): ?>
                        <tr data-cart-item="<?= $product->getTag() ?>">
                            <td class="col-xs-2 col-sm-1 basket_item_image">
                                <?php if (!empty($product->getImage('eshop.eshop_product_basket'))): ?>
                                    <a href="<?= $product->getUrl() ?>" title="<?= $product->getName() ?>">
                                        <img
                                                src="<?= $product->getImage('eshop.eshop_product_basket') ?>"
                                                alt="<?= $product->getName() ?>"
                                                title="<?= $product->getName() ?>"/>
                                    </a>
                                <?php endif; ?>
                            </td>

                            <td class="col-xs-10 col-sm-11 basket_item_details">
                                <div class="col-xs-12 col-sm-4 col-md-2 product-name-wrapper">
                                    <h3 class="cart-product-name">
                                        <a href="<?= $product->getUrl() ?>" title="<?= $product->getName() ?>">
                                            <?= $product->getName(); ?>
                                        </a>
                                    </h3>
                                </div>
                                <div class="col-xs-12 col-sm-4 col-md-2 bt-cart-item-amount text-center">
                                    <?= $product->getAmount() ?>
                                </div>
                                <div class="col-xs-12 col-sm-4 col-md-2 bt-cart-item-amount text-center">
                                    <?= $product->getProducer() ?>
                                </div>
                                <div class="col-md-2 visible-md visible-lg bt-cart-item-price">
                                    <?php if ($product->isPriceDefined(\Buxus\Eshop\Price\ItemPriceDecorator\ItemDiscount::NON_DISCOUNT_PRICE_INCLUDING_VAT)): ?>
                                        <div class="price-old"><?= $this->formatPrice($product->getPriceObject(\Buxus\Eshop\Price\ItemPriceDecorator\ItemDiscount::NON_DISCOUNT_PRICE_INCLUDING_VAT)) ?></div>
                                    <?php endif; ?>
                                    <?= $this->formatPrice($product->getPriceObject(\Buxus\Eshop\Price\PriceType::ITEM_PRICE_WITHOUT_VAT)) ?>
                                </div>

                                <div class="col-xs-12 col-sm-4 col-md-2 bt-cart-item-total-price item-total-price-formated">
                                    <?= $this->formatPrice($product->getFinalPriceObjectWithoutVat()) ?>
                                </div>
                                <div class="col-md-2">
                                    <?= $product->renderAvailability() ?>
                                </div>
                            </td>
                        </tr>
                    <?php
                    elseif ($product instanceof Buxus\Vouchers\VoucherCartItem):?>
                        <tr data-cart-item="<?= $product->getTag() ?>">
                            <td class="col-xs-2 col-sm-1 basket_item_image">
                                <img
                                        src="<?= $product->getImage('eshop.eshop_product_basket') ?>"
                                        alt="<?= $product->getName() ?>"
                                        title="<?= $product->getName() ?>"/>
                            </td>

                            <td class="col-xs-10 col-sm-11 basket_item_details">

                                <div class="col-xs-12 col-sm-4 col-md-3 product-name-wrapper">

                                    <h3 class="cart-product-name">
                                        <?= $product->getName(); ?>
                                    </h3>

                                </div>
                                <div class="col-xs-12 col-sm-4 col-md-2 bt-cart-item-amount text-center">

                                </div>
                                <div class="col-xs-12 col-md-2 hidden-sm visible-xs visible-md visible-lg">

                                </div>
                                <div class="col-md-2 visible-md visible-lg bt-cart-item-price">
                                </div>

                                <div class="col-xs-12 col-sm-4 col-md-2 bt-cart-item-total-price item-total-price-formated price-small">

                                    <?= $this->formatPrice($product->getFinalPriceObject()) ?>
                                </div>
                            </td>
                        </tr>
                    <?php endif;
                endforeach; ?>
                <?php foreach ($checkout_extensions as $key => $product): /* @var $item App\Eshop\Product */
                    if ($product instanceof \Buxus\Eshop\CartExtension\CartExtensionInterface):
                        ?>
                        <tr data-cart-item="<?= $product->getTag() ?>">
                            <td class="col-xs-2 col-sm-1 basket_item_image">
                                <img
                                        src="<?= $product->getImage('eshop.eshop_product_basket') ?>"
                                        alt="<?= $product->getName() ?>"
                                        title="<?= $product->getName() ?>"/>
                            </td>

                            <td class="col-xs-10 col-sm-11 basket_item_details">

                                <div class="col-xs-12 col-sm-4 col-md-3 product-name-wrapper">

                                    <h3 class="cart-product-name">
                                        <?= $product->getName(); ?>
                                    </h3>

                                </div>
                                <div class="col-xs-12 col-sm-4 col-md-2 bt-cart-item-amount text-center">

                                </div>
                                <div class="col-xs-12 col-md-2 hidden-sm visible-xs visible-md visible-lg">

                                </div>
                                <div class="col-md-2 visible-md visible-lg bt-cart-item-price">
                                </div>

                                <div class="col-xs-12 col-sm-4 col-md-2 bt-cart-item-total-price item-total-price-formated">

                                    <?= $this->formatPrice($product->getFinalPriceObject()) ?>
                                </div>
                            </td>
                        </tr>
                    <?php
                    endif;
                endforeach; ?>

                </tbody>
            </table>

            <h2 class="cart_header cart_header_delivery_data"><?= $this->str('cart', 'Dodacie údaje') ?></h2>
            <div class="row cart_summary_details">
                <div class="col-xs-12 col-lg-6">
                    <div class="row">
                        <div class="col-sm-6">
                            <h3 class="cart_subheader"><?= $this->str('cart', 'Fakturačná adresa:') ?></h3>
                            <ul>
                                <?php if (!empty($checkout->getCompanyName())): ?>
                                    <li><?= $checkout->getCompanyName() ?></li>
                                <?php endif; ?>

                                <li><?= $checkout->getInvoiceStreet() ?></li>
                                <li><?= $checkout->getInvoiceZip() ?> <?= $checkout->getInvoiceCity() ?></li>
                                <li><?= Country::getCountryName($checkout->getInvoiceCountry()) ?></li>

                                <?php if (!empty($checkout->getIco())): ?>
                                    <li>
                                        <span class="font-weight-bold">
                                            <?= \Trans::str('cart', 'IČO') ?>:
                                        </span>
                                        <?= $checkout->getIco() ?></li>
                                <?php endif; ?>
                                <?php if (!empty($checkout->getDic())): ?>
                                    <li>
                                        <span class="font-weight-bold">
                                            <?= \Trans::str('cart', 'DIČ') ?>:
                                        </span>
                                        <?= $checkout->getDic() ?>
                                    </li>
                                <?php endif; ?>
                                <?php if (!empty($checkout->getIcdph())): ?>
                                    <li>
                                        <span class="font-weight-bold">
                                            <?= \Trans::str('cart', 'IČ DPH') ?>:
                                        </span>
                                        <?= $checkout->getIcdph() ?>
                                    </li>
                                <?php endif; ?>

                                <li>
                                    <span class="font-weight-bold">
                                        <?= \Trans::str('cart', 'Telefón') ?>:
                                    </span>
                                    <?= $checkout->getPhone() ?>
                                </li>
                                <li>
                                    <span class="font-weight-bold">
                                        <?= \Trans::str('cart', 'Email') ?>:
                                    </span>
                                    <?= $checkout->getEmail() ?>
                                </li>
                            </ul>

                        </div>
                        <div class="col-sm-6">
                            <h3 class="cart_subheader cart_subheader_delivery_address"><?= $this->str('cart', 'Dodacia adresa:') ?></h3>

                            <ul>
                                <?php if (!empty($checkout->getDeliveryCompanyName())): ?>
                                    <li><?= $checkout->getDeliveryCompanyName() ?></li>
                                <?php endif; ?>

                                <li><?= $checkout->getDeliveryStreet() ?></li>
                                <li><?= $checkout->getDeliveryZip() ?> <?= $checkout->getDeliveryCity() ?></li>
                                <li><?= Country::getCountryName($checkout->getDeliveryCountry()) ?></li>

                                <?php if (!empty($checkout->getIco())): ?>
                                    <li>
                                        <span class="font-weight-bold">
                                            <?= \Trans::str('cart', 'IČO') ?>:
                                        </span>
                                        <?= $checkout->getIco() ?>
                                    </li>
                                <?php endif; ?>
                                <?php if (!empty($checkout->getDic())): ?>
                                    <li>
                                        <span class="font-weight-bold">
                                            <?= \Trans::str('cart', 'DIČ') ?>:
                                        </span>
                                        <?= $checkout->getDic() ?>
                                    </li>
                                <?php endif; ?>
                                <?php if (!empty($checkout->getIcdph())): ?>
                                    <li>
                                        <span class="font-weight-bold">
                                            <?= \Trans::str('cart', 'IČ DPH') ?>:
                                        </span>
                                        <?= $checkout->getIcdph() ?>
                                    </li>
                                <?php endif; ?>

                                <li>
                                    <span class="font-weight-bold">
                                        <?= \Trans::str('cart', 'Telefón') ?>:
                                    </span>
                                    <?= $checkout->getDeliveryPhone() ?: $checkout->getPhone() ?>
                                </li>
                                <li>
                                    <span class="font-weight-bold">
                                        <?= \Trans::str('cart', 'Email') ?>:
                                    </span>
                                    <?= $checkout->getEmail() ?>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <?php if(!empty($checkout->getNote())): ?>
                        <div class="row">
                            <div class="col-md-12">
                                <h2 class="cart_header cart_header_note"><?= $this->str('cart', 'Poznámka') ?></h2>
                                <div class="text_block">
                                    <p><?= $checkout->getNote() ?></p>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="col-xs-12 col-lg-6">
                    <?= $this->process->getForm() ?>
                </div>
            </div>
            <div class="row">
                <?= $this->process->getForm()->getElements()['back'] ?>
            </div>
        </div>
    </div>

</div>

<?php js_begin() ?>
<script type="text/javascript">
    require(['jquery'], function ($) {
        $(function () {
            $('form').submit(function () {
                $('input[type="submit"]', this)
                    .attr('disabled', 'disabled');
                return true;
            });
        });
    });
</script>
<?php js_end() ?>
