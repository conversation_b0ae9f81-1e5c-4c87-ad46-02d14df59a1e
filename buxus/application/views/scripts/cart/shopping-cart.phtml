<?php
$checkout = $this->checkout;
?>
<div class="container">
    <div class="row">
        <div class="col-xs-12">
            <?php

            if ($this->shopping_cart->isEmpty()) : ?>
                <p class="bg-warning"><?= $this->str('cart', '<PERSON><PERSON><PERSON> je momentálne prázdny.') ?></p>
            <?= $checkout->getCart()->renderPriceOffers() ?>
            <?php else : ?>
            <?= $this->action('cart-navigator', 'cart') ?>
                <form action="<?= \Buxus\Util\Url::page(Buxus\Util\PageIds::getPageId('cart1_contents')) ?>"
                      method="post" class="eshop-cart-form">
                    <!-- #basket_table:BEGIN //-->
                    <table class="basket_table">
                        <thead>
                        <tr>
                            <th>
                            </th>
                            <th>
                                <div class="col-md-3 basket_table_product">

                                </div>
                                <div class="col-md-2">
                                    <?= $this->str('cart', 'Množstvo') ?>
                                </div>
                                <div class="col-md-2">
                                    <?= $this->str('cart', 'Cena za kus') ?>
                                </div>
                                <div class="col-md-2">
                                    <?= $this->str('cart', 'Cena celkom bez DPH') ?>
                                </div>
                            </th>
                        </tr>
                        </thead>


                        <?php foreach ($this->shopping_cart as $item): ?>
                            <?php if ($item instanceof \Buxus\Eshop\Item\RenderableShopItemInterface): ?>
                                <?= $item->renderCheckoutContents() ?>
                            <?php endif; ?>
                        <?php endforeach; ?>


                        <?php foreach ($this->checkout_extensions as $item): ?>
                            <?php if ($item instanceof \Buxus\Eshop\CartExtension\CartExtensionInterface): ?>
                                <?= $item->renderCheckoutContents() ?>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </table>

                    <?= $this->process->getForm()->render($this) ?>

                </form>
            <?php
            js_begin() ?>
                <script type="text/javascript">
                    require(['jquery', 'requirejs-domready', 'ui42-block'], function ($, domReady, block) {

                        domReady(function () {
                            $('.bt-cart-item-amount input, .js-cart-item-amount').on('change', function () {
                                var caller = this;
                                var item_id = $(this).attr('data-item-id');
                                if (typeof item_id != 'undefined' && item_id != '') {
                                    require(['eshop'], function (eshop) {
                                        var url = '<?= $this->cartCommand('change-amount') ?>';
                                        eshop.command(caller, url, {
                                            product_id: item_id,
                                            amount: $(caller).val()
                                        });
                                    });
                                }
                            });

                            $(document).on('eshop.command', function (e, command, data) {
                                if (command == 'change-amount') {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    require(['eshop'], function (eshop) {
                                        eshop.updateInfo(data);
                                        block.block();
                                        window.location.reload();
                                        return;
                                    });
                                }
                            });
                        });
                    });
                </script>
                <?php js_end() ?>
            <?php endif; ?>
        </div>
    </div>
</div>
