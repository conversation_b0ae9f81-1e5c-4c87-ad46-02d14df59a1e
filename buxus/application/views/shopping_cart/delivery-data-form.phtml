<?php

use Eshop\ShoppingCart\Checkout\Process\Form\Plugin\DeliveryDataFormElementPluginInterface;

$shopping_cart = \ShoppingCart::get();
$checkout = $shopping_cart->getCheckout();

$showVat = new \App\Eshop\Price\ShowVAT();
?>
<div class="container">
    <form action="" method="POST" id="delivery_data_form_form" class="form-horizontal">
        <div class="row">
            <div class="col-md-6 delivery-data">
                <div class="col-xs-12 col-md-10">
                    <?php if (!\WebUserAuthentication::isAuthenticated()): ?>
                        <h2 class="cart_header"><?= $this->str('cart', 'Nákup bez registrácie') ?></h2>
                    <?php endif; ?>

                    <div class="bt-cart-header">
                        <h3><?= $this->str('cart', 'FAKTURAČNÉ ÚDAJE') ?></h3>
                    </div>
                </div>

                <div class="col-xs-12 col-md-10 justify-content-center">
                    <?= $this->elements['customer_type'] ?>
                </div>
                <div class="row">
                    <div class="col-xs-12 col-md-10">
                        <?= $this->elements['company_name'] ?>
                        <?= $this->elements['street'] ?>

                        <div class="one-row-elements">
                            <?= $this->elements['zip'] ?>&nbsp;
                            <?= $this->elements['city'] ?>
                        </div>

                        <?php if (isset($this->elements['country'])): ?>
                            <?= $this->elements['country'] ?>
                        <?php endif; ?>

                        <?= $this->elements['ico'] ?>
                        <?= $this->elements['dic'] ?>
                        <?= $this->elements['drc'] ?>
                        <?= $this->elements['phone'] ?>
                        <?= $this->elements['email'] ?>

                        <?php foreach ($this->form->getPlugins() as $plugin): /** @var DeliveryDataFormElementPluginInterface $plugin */ ?>
                            <?php foreach ($plugin->getPersonElementNames() as $pluginElementName): ?>
                                <?= $this->elements[$pluginElementName] ?>
                            <?php endforeach; ?>

                            <?php foreach ($plugin->getCorporateElementNames() as $pluginElementName): ?>
                                <?= $this->elements[$pluginElementName] ?>
                            <?php endforeach; ?>
                        <?php endforeach; ?>
                    </div>
                </div>
                <div class="row">
                    <div class="col-xs-12  col-md-10">
                        <?php foreach ($this->form->getPlugins() as $plugin): /** @var DeliveryDataFormElementPluginInterface $plugin */ ?>
                            <?php foreach ($plugin->getBasicElementNames() as $pluginElementName): ?>
                                <?= $this->elements[$pluginElementName] ?>
                            <?php endforeach; ?>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="col-xs-12  col-md-10">
                    <div class="bt-cart-header bt-cart-header-next d-flex justify-content-between">
                        <h3><?= $this->str('cart', 'ADRESA DORUČENIA') ?></h3>
                        <a target="_blank"
                           href="<?= \Buxus\Util\Url::page(\Buxus\Util\PageIds::getDeliveryAddresses()) ?>"><?= $this->str('cart', 'Upraviť adresy') ?></a>
                    </div>
                    <?= $this->elements['delivery_address_choice'] ?>
                    <?= $this->elements['delivery_company_name'] ?>
                    <?= $this->elements['delivery_name'] ?>
                    <?= $this->elements['delivery_phone'] ?>
                    <?= $this->elements['delivery_street'] ?>
                    <?= $this->elements['delivery_city'] ?>
                    <?= $this->elements['delivery_zip'] ?>
                    <?php if (isset($this->elements['delivery_country'])): ?>
                        <?= $this->elements['delivery_country'] ?>
                    <?php endif; ?>

                    <?php foreach ($this->form->getPlugins() as $plugin): /** @var DeliveryDataFormElementPluginInterface $plugin */ ?>
                        <?php foreach ($plugin->getRequiredDeliveryAddressElementNames() as $pluginElementName): ?>
                            <?= $this->elements[$pluginElementName] ?>
                        <?php endforeach; ?>

                        <?php foreach ($plugin->getOptionalDeliveryAddressElementNames() as $pluginElementName): ?>
                            <?= $this->elements[$pluginElementName] ?>
                        <?php endforeach; ?>
                    <?php endforeach; ?>
                    <div id="delivery_data_note">
                        <?= $this->elements['note'] ?>
                    </div>
                </div>
            </div>
            <?php
            /**
             * @var \Buxus\Eshop\Contracts\ShoppingCart $cart
             */
            $cart = app(\Buxus\Eshop\Contracts\ShoppingCart::class);
            ?>
            <div class="col-xs-12">
                <div class="row cart-form">
                    <?= $this->elements['back'] ?>
                    <div class="col-xs-12 col-sm-6 col-sm-offset-6 cart-form-submit">
                        <div class="row cart-transport">
                            <div class="col-xs-12">
                                <table class="table cart-transport-table">
                                    <?php
                                    /**
                                     * @var \Buxus\Eshop\Item\Delivery\GenericDeliveryType $transport
                                     */
                                    $transport = $checkout->getTransportType();
                                    ?>
                                    <?php if (is_object($transport) && $transport->getTag() != ''): ?>

                                        <tr>
                                            <th><?= $transport->getName() ?></th>
                                            <td>
                                                <?php if ($shopping_cart->shouldShowDeliveryPrice()): ?>
                                                    <?= $this->formatPrice($transport->getFinalPriceObjectWithoutVat()) ?>
                                                <?php else: ?>
                                                    <small><?= $this->str('cart', 'Cena za dopravu bude vypočítaná osobitne') ?></small>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                        <?php
                                        /**
                                         * @var \Buxus\Eshop\Item\Payment\GenericPaymentType $payment
                                         */
                                        $payment = $checkout->getPaymentType();
                                        ?>
                                        <tr>
                                            <th>
                                                <?= $payment->getName() ?>
                                            </th>
                                            <td>      <?= $this->formatPrice($payment->getFinalPriceObjectWithoutVat()) ?>

                                            </td>
                                        </tr>
                                    <?php endif; ?>
                                </table>
                            </div>
                        </div>
                        <div class="row cart-total-price">
                            <div class="cart-price-row col-xs-12">
                                <div class="row">
                                    <div class="col-xs-8">
                                        <span><?= $this->str('cart', 'Cena celkom bez DPH') ?></span>
                                    </div>
                                    <div class="col-xs-4 price cart-total-price-formated"
                                         id="cart-total-price-formated">
                                        <h3 class="price-without-vat">
                                            <?= str_replace(' ', '&nbsp;', $this->formatPrice($shopping_cart->getFinalPriceObjectWithoutVat())) ?>
                                        </h3>
                                    </div>
                                </div>
                            </div>
                            <?php if ($showVat->shouldShowVAT()): ?>
                                <div class="cart-price-row col-xs-12">
                                    <div class="row">
                                        <div class="col-xs-8 with-vat">
                                            <span><?= $this->str('cart', 'Cena celkom vrátane DPH') ?></span>
                                        </div>
                                        <div class="col-xs-4 price cart-total-price-formated price-small"
                                             id="cart-total-price-formated">
                                            <span><?= str_replace(' ', '&nbsp;', $this->formatPrice($shopping_cart->getFinalPriceObject())) ?></span>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                            <div class="col-xs-12"> <?= $this->elements[$this->form->getFormTag()] ?></div>
                        </div>


                    </div>
                </div>
            </div>
            <?php js_begin() ?>
            <script type="text/javascript">
                require(['jquery'], function ($) {
                    $(function () {
                        $('.info-form input').keydown(function (event) {
                            if (event.which == 13) {
                                event.preventDefault();
                                $(this).parents('form').submit();
                            }
                        });

                        <?php if (config('buxus_shopping_cart.check_for_prior_registration') && !\WebUserAuthentication::isAuthenticated()): ?>
                        require(['wizard/shopping-cart-login'], function (cart_wizard) {
                            cart_wizard.setup('#email', '#mini-login-form');
                        });
                        <?php endif; ?>
                    });
                });

            </script>
            <?php foreach ($this->form->getPlugins() as $plugin): ?>
                <?= $plugin->renderInlineJavascripts($this->form, $this->elements) ?>
            <?php endforeach; ?>

            <?php js_end() ?>
            <?php if (config('buxus_shopping_cart.check_for_prior_registration') && !\WebUserAuthentication::isAuthenticated()): ?>
                <div id="mini-login-form" class="mini-login-form" style="display: none">
                    <div class="form" style="display: none"></div>
                    <img class="wait" src="/buxus/assets/images/wait.gif"/>
                </div>
            <?php endif; ?>

            <?= csrf_field() ?>
        </div>
    </form>
</div>
