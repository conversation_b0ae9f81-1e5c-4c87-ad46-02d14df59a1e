<form class="form-horizontal" action="<?= $this->escape($this->form->getAction()) ?>"
      method="<?= $this->escape($this->form->getMethod()) ?>" enctype="application/x-www-form-urlencoded"
      id="<?= $this->form->getId() ?>">
    <?= $this->formErrorsList($this->form->getMessages()); ?>
    <input type="hidden" name="login_form" value="1">

    <?= $this->elements['user_login'] ?>
    <?= $this->elements['user_password'] ?>
    <?= $this->elements['redirect'] ?>
    <?= $this->elements['user_full_name'] ?? '' ?>
    <?= $this->elements['captcha'] ?>
    <?= $this->elements[$this->form->getFormTag()] ?>

    <?= csrf_field() ?>
</form>

<?= (new \App\View\Components\InvalidLoginModalComponent())->render() ?>

<?php js_begin(); ?>
<script type="text/javascript">
    require(['jquery'], function ($) {
        $('#<?= $this->form->getId() ?>').on('submit', function (e) {
            e.preventDefault();
            e.stopPropagation();

            let userLogin = $('input[name="user_login"]').val();
            let loginForm = this;

            $.ajax('<?= route('user.validate-login') ?>', {
                type: 'POST',
                data: {
                    user_login: userLogin
                },
                dataType: 'json',
                success: function (result) {
                    if (result.user_exists && !result.can_user_login) {
                        $('#invalidLoginModal').modal();
                    } else {
                        $(loginForm).unbind('submit').submit();
                    }
                },
                error: function () {
                    $(loginForm).unbind('submit').submit();
                }
            });
        });
    });
</script>
<?php js_end(); ?>
