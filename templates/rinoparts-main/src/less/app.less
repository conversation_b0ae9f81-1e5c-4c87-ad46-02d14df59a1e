// Bootstrap variables & mixins
@import 'bootstrap/_config';

// Rinoparts's variables & mixins used to overwrite Bootstrap variables
@import '_config';
@import '_mixins';

// actual Bootstrap styles
@import 'bootstrap/_styles';

// Slick carousel
//@import '~slick-carousel/slick/slick';
//@import '~slick-carousel/slick/slick-theme';

// LightGallery
//@import '~lightgallery/dist/css/lightgallery.min.css';

// RinoParts Modules
@import 'modules/_grid';
@import 'modules/_dropdown';
@import 'modules/_button';
@import 'modules/_general';
@import 'modules/_search';
@import 'modules/_header';
@import 'modules/_footer';
@import 'modules/_form';
@import 'modules/_mainNavigation';
@import 'modules/_cover';
@import 'modules/_partnerLogo';
@import 'modules/_categoryBanner';
@import 'modules/_card';
@import 'modules/_table';
@import 'modules/_suggestions';
@import 'modules/_breadcrumb';
@import 'modules/_productList';
@import 'modules/_productDetail';


