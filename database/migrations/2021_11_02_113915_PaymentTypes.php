<?php

use Buxus\Migration\AbstractMigration;

/**
 * Automatic generation from (rinoparts) at 2021-11-02 11:39:14
 * Page generator: page_id=371614
 */
class PaymentTypes extends AbstractMigration
{
    public function up()
    {
        // page: Faktúra(ID: 371614 TAG: invoice)
        $pageId = $this->getPageIdByTag('invoice');
        if ($pageId === null) {
            $pageType = $this->pageTypesManager()->getPageTypeByTag('eshop_payment_type');
            $page371614 = \PageFactory::create($this->getPageIdByTag('eshop_payment_types'), $pageType->getId());
        } else {
            $page371614 = \PageFactory::get($pageId);
        }
        $page371614->setPageName('Faktúra');
        $page371614->setPageTag('invoice');
        $page371614->setPageStateId('1');
        $page371614->setPageClassId(1);
        $page371614->setValue('exchange_rate_cz', '');
        $page371614->setValue('title', 'Faktúra');
        $page371614->setValue('title_en', '');
        $page371614->setValue('title_cz', '');
        $page371614->setValue('eshop_tag', 'invoice');
        $page371614->setValue('eshop_transaction_description', '');
        $page371614->setValue('eshop_description', 'Faktúru obdržíte pri prevzatí tovaru');
        $page371614->setValue('eshop_eur_price_including_vat', '0');
        $page371614->setValue('testing_active', 'F');
        $page371614->save();
    }

    public function down()
    {
        // remove page: Faktúra (invoice)
        $pageId = $this->getPageIdByTag('invoice');
        if ($pageId !== null) {
            \PageFactory::get($pageId)->delete();
        }
    }
}
