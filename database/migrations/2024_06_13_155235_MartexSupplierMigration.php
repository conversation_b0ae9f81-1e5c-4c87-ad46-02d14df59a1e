<?php

use Buxus\Migration\AbstractMigration;

/**
 * Automatic generation from (rinoparts) at 2024-01-25 19:38:35
 */
class MartexSupplierMigration extends AbstractMigration
{
    public function dependencies()
    {
        return [
            MartexPagesMigration::class,
        ];
    }

    public function up()
    {
        \App\Supplier::create([
            'name' => 'MARTEX-SUPPLIER',
            'producer_ciselnik_id' => \Buxus\Util\PageIds::getMartexSupplier(),
            'price_levels_on' => 1,
        ]);
    }

    public function down()
    {
    }
}
