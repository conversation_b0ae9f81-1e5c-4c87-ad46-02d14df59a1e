<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;

/**
 * Automatic generation from (rinoparts) at 2024-08-21 13:06:26
 * Property generator: property=remante_supplier_code,remante_price_without_vat,remante_stock_balance,remante_oe_number,remante_oe_numbers,remante_latest_import
 */
class RemantePropertiesMigration extends AbstractMigration
{
    public function up()
    {
        // property: Remante - Dodávateľský kód(remante_supplier_code)
        $propertyRemanteSupplierCode = $this->propertyManager()->propertyExistsByTag('remante_supplier_code');
        if ($propertyRemanteSupplierCode === false) {
            $propertyRemanteSupplierCode = new Property();
            $propertyRemanteSupplierCode->setTag('remante_supplier_code');
            $propertyRemanteSupplierCode->setDescription('');
            $propertyRemanteSupplierCode->setExtendedDescription('');
            $propertyRemanteSupplierCode->setName('Remante - Dodávateľský kód');
            $propertyRemanteSupplierCode->setClassId(4);
            $propertyRemanteSupplierCode->setShowType(null);
            $propertyRemanteSupplierCode->setShowTypeTag('text');
            $propertyRemanteSupplierCode->setValueType('oneline_text');
            $propertyRemanteSupplierCode->setDefaultValue('');
            $propertyRemanteSupplierCode->setMultiOperations(false);
            $propertyRemanteSupplierCode->setInputString('');
            $propertyRemanteSupplierCode->setAttribute('tab', 'Remante');
            $propertyRemanteSupplierCode->setAttribute('size', '60');
            $propertyRemanteSupplierCode->setAttribute('maxlength', '');
            $propertyRemanteSupplierCode->setAttribute('readonly', 'F');
            $propertyRemanteSupplierCode->setAttribute('pattern', '');
            $propertyRemanteSupplierCode->setAttribute('inherit_value', 'F');
            $propertyRemanteSupplierCode->setAttribute('onchange-js', '');
            $propertyRemanteSupplierCode->setAttribute('onkeyup-js', '');
            $propertyRemanteSupplierCode->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyRemanteSupplierCode);
        } else {
            $this->writeLine('Property with tag remante_supplier_code already exists');
            $this->setDataKey('property_remante_supplier_code_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('remante_supplier_code', 'eshop_product', false);
        }

        // property: Remante - Cena bez DPH(remante_price_without_vat)
        $propertyRemantePriceWithoutVat = $this->propertyManager()->propertyExistsByTag('remante_price_without_vat');
        if ($propertyRemantePriceWithoutVat === false) {
            $propertyRemantePriceWithoutVat = new Property();
            $propertyRemantePriceWithoutVat->setTag('remante_price_without_vat');
            $propertyRemantePriceWithoutVat->setDescription('');
            $propertyRemantePriceWithoutVat->setExtendedDescription('');
            $propertyRemantePriceWithoutVat->setName('Remante - Cena bez DPH');
            $propertyRemantePriceWithoutVat->setClassId(4);
            $propertyRemantePriceWithoutVat->setShowType(null);
            $propertyRemantePriceWithoutVat->setShowTypeTag('text');
            $propertyRemantePriceWithoutVat->setValueType('oneline_text');
            $propertyRemantePriceWithoutVat->setDefaultValue('');
            $propertyRemantePriceWithoutVat->setMultiOperations(false);
            $propertyRemantePriceWithoutVat->setInputString('');
            $propertyRemantePriceWithoutVat->setAttribute('tab', 'Remante');
            $propertyRemantePriceWithoutVat->setAttribute('size', '60');
            $propertyRemantePriceWithoutVat->setAttribute('maxlength', '');
            $propertyRemantePriceWithoutVat->setAttribute('readonly', 'F');
            $propertyRemantePriceWithoutVat->setAttribute('pattern', '');
            $propertyRemantePriceWithoutVat->setAttribute('inherit_value', 'F');
            $propertyRemantePriceWithoutVat->setAttribute('onchange-js', '');
            $propertyRemantePriceWithoutVat->setAttribute('onkeyup-js', '');
            $propertyRemantePriceWithoutVat->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyRemantePriceWithoutVat);
        } else {
            $this->writeLine('Property with tag remante_price_without_vat already exists');
            $this->setDataKey('property_remante_price_without_vat_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('remante_price_without_vat', 'eshop_product', false);
        }

        // property: Remante - Skladová zásoba(remante_stock_balance)
        $propertyRemanteStockBalance = $this->propertyManager()->propertyExistsByTag('remante_stock_balance');
        if ($propertyRemanteStockBalance === false) {
            $propertyRemanteStockBalance = new Property();
            $propertyRemanteStockBalance->setTag('remante_stock_balance');
            $propertyRemanteStockBalance->setDescription('');
            $propertyRemanteStockBalance->setExtendedDescription('');
            $propertyRemanteStockBalance->setName('Remante - Skladová zásoba');
            $propertyRemanteStockBalance->setClassId(4);
            $propertyRemanteStockBalance->setShowType(null);
            $propertyRemanteStockBalance->setShowTypeTag('text');
            $propertyRemanteStockBalance->setValueType('oneline_text');
            $propertyRemanteStockBalance->setDefaultValue('');
            $propertyRemanteStockBalance->setMultiOperations(false);
            $propertyRemanteStockBalance->setInputString('');
            $propertyRemanteStockBalance->setAttribute('tab', 'Remante');
            $propertyRemanteStockBalance->setAttribute('size', '60');
            $propertyRemanteStockBalance->setAttribute('maxlength', '');
            $propertyRemanteStockBalance->setAttribute('readonly', 'F');
            $propertyRemanteStockBalance->setAttribute('pattern', '');
            $propertyRemanteStockBalance->setAttribute('inherit_value', 'F');
            $propertyRemanteStockBalance->setAttribute('onchange-js', '');
            $propertyRemanteStockBalance->setAttribute('onkeyup-js', '');
            $propertyRemanteStockBalance->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyRemanteStockBalance);
        } else {
            $this->writeLine('Property with tag remante_stock_balance already exists');
            $this->setDataKey('property_remante_stock_balance_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('remante_stock_balance', 'eshop_product', false);
        }

        // property: Remante - OE number(remante_oe_number)
        $propertyRemanteOeNumber = $this->propertyManager()->propertyExistsByTag('remante_oe_number');
        if ($propertyRemanteOeNumber === false) {
            $propertyRemanteOeNumber = new Property();
            $propertyRemanteOeNumber->setTag('remante_oe_number');
            $propertyRemanteOeNumber->setDescription('');
            $propertyRemanteOeNumber->setExtendedDescription('');
            $propertyRemanteOeNumber->setName('Remante - OE number');
            $propertyRemanteOeNumber->setClassId(4);
            $propertyRemanteOeNumber->setShowType(null);
            $propertyRemanteOeNumber->setShowTypeTag('text');
            $propertyRemanteOeNumber->setValueType('oneline_text');
            $propertyRemanteOeNumber->setDefaultValue('');
            $propertyRemanteOeNumber->setMultiOperations(false);
            $propertyRemanteOeNumber->setInputString('');
            $propertyRemanteOeNumber->setAttribute('tab', 'Remante');
            $propertyRemanteOeNumber->setAttribute('size', '60');
            $propertyRemanteOeNumber->setAttribute('maxlength', '');
            $propertyRemanteOeNumber->setAttribute('readonly', 'F');
            $propertyRemanteOeNumber->setAttribute('pattern', '');
            $propertyRemanteOeNumber->setAttribute('inherit_value', 'F');
            $propertyRemanteOeNumber->setAttribute('onchange-js', '');
            $propertyRemanteOeNumber->setAttribute('onkeyup-js', '');
            $propertyRemanteOeNumber->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyRemanteOeNumber);
        } else {
            $this->writeLine('Property with tag remante_oe_number already exists');
            $this->setDataKey('property_remante_oe_number_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('remante_oe_number', 'eshop_product', false);
        }

        // property: Remante - OE numbers(remante_oe_numbers)
        $propertyRemanteOeNumbers = $this->propertyManager()->propertyExistsByTag('remante_oe_numbers');
        if ($propertyRemanteOeNumbers === false) {
            $propertyRemanteOeNumbers = new Property();
            $propertyRemanteOeNumbers->setTag('remante_oe_numbers');
            $propertyRemanteOeNumbers->setDescription('');
            $propertyRemanteOeNumbers->setExtendedDescription('');
            $propertyRemanteOeNumbers->setName('Remante - OE numbers');
            $propertyRemanteOeNumbers->setClassId(4);
            $propertyRemanteOeNumbers->setShowType(null);
            $propertyRemanteOeNumbers->setShowTypeTag('text');
            $propertyRemanteOeNumbers->setValueType('oneline_text');
            $propertyRemanteOeNumbers->setDefaultValue('');
            $propertyRemanteOeNumbers->setMultiOperations(false);
            $propertyRemanteOeNumbers->setInputString('');
            $propertyRemanteOeNumbers->setAttribute('tab', 'Remante');
            $propertyRemanteOeNumbers->setAttribute('size', '60');
            $propertyRemanteOeNumbers->setAttribute('maxlength', '');
            $propertyRemanteOeNumbers->setAttribute('readonly', 'F');
            $propertyRemanteOeNumbers->setAttribute('pattern', '');
            $propertyRemanteOeNumbers->setAttribute('inherit_value', 'F');
            $propertyRemanteOeNumbers->setAttribute('onchange-js', '');
            $propertyRemanteOeNumbers->setAttribute('onkeyup-js', '');
            $propertyRemanteOeNumbers->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyRemanteOeNumbers);
        } else {
            $this->writeLine('Property with tag remante_oe_numbers already exists');
            $this->setDataKey('property_remante_oe_numbers_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('remante_oe_numbers', 'eshop_product', false);
        }

        // property: Remante - Posledný import(remante_latest_import)
        $propertyRemanteLatestImport = $this->propertyManager()->propertyExistsByTag('remante_latest_import');
        if ($propertyRemanteLatestImport === false) {
            $propertyRemanteLatestImport = new Property();
            $propertyRemanteLatestImport->setTag('remante_latest_import');
            $propertyRemanteLatestImport->setDescription('');
            $propertyRemanteLatestImport->setExtendedDescription('');
            $propertyRemanteLatestImport->setName('Remante - Posledný import');
            $propertyRemanteLatestImport->setClassId(4);
            $propertyRemanteLatestImport->setShowType(null);
            $propertyRemanteLatestImport->setShowTypeTag('text');
            $propertyRemanteLatestImport->setValueType('oneline_text');
            $propertyRemanteLatestImport->setDefaultValue('');
            $propertyRemanteLatestImport->setMultiOperations(false);
            $propertyRemanteLatestImport->setInputString('');
            $propertyRemanteLatestImport->setAttribute('tab', 'Remante');
            $propertyRemanteLatestImport->setAttribute('size', '60');
            $propertyRemanteLatestImport->setAttribute('maxlength', '');
            $propertyRemanteLatestImport->setAttribute('readonly', 'F');
            $propertyRemanteLatestImport->setAttribute('pattern', '');
            $propertyRemanteLatestImport->setAttribute('inherit_value', 'F');
            $propertyRemanteLatestImport->setAttribute('onchange-js', '');
            $propertyRemanteLatestImport->setAttribute('onkeyup-js', '');
            $propertyRemanteLatestImport->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyRemanteLatestImport);
        } else {
            $this->writeLine('Property with tag remante_latest_import already exists');
            $this->setDataKey('property_remante_latest_import_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('remante_latest_import', 'eshop_product', false);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }

    public function down()
    {
        // remove property: Remante - Posledný import(remante_latest_import)
        $propertyRemanteLatestImport = $this->propertyManager()->propertyExistsByTag('remante_latest_import');
        if (($propertyRemanteLatestImport !== false) && ($this->getDataKey('property_remante_latest_import_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyRemanteLatestImport);
        }

        // remove property: Remante - OE numbers(remante_oe_numbers)
        $propertyRemanteOeNumbers = $this->propertyManager()->propertyExistsByTag('remante_oe_numbers');
        if (($propertyRemanteOeNumbers !== false) && ($this->getDataKey('property_remante_oe_numbers_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyRemanteOeNumbers);
        }

        // remove property: Remante - OE number(remante_oe_number)
        $propertyRemanteOeNumber = $this->propertyManager()->propertyExistsByTag('remante_oe_number');
        if (($propertyRemanteOeNumber !== false) && ($this->getDataKey('property_remante_oe_number_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyRemanteOeNumber);
        }

        // remove property: Remante - Skladová zásoba(remante_stock_balance)
        $propertyRemanteStockBalance = $this->propertyManager()->propertyExistsByTag('remante_stock_balance');
        if (($propertyRemanteStockBalance !== false) && ($this->getDataKey('property_remante_stock_balance_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyRemanteStockBalance);
        }

        // remove property: Remante - Cena bez DPH(remante_price_without_vat)
        $propertyRemantePriceWithoutVat = $this->propertyManager()->propertyExistsByTag('remante_price_without_vat');
        if (($propertyRemantePriceWithoutVat !== false) && ($this->getDataKey('property_remante_price_without_vat_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyRemantePriceWithoutVat);
        }

        // remove property: Remante - Dodávateľský kód(remante_supplier_code)
        $propertyRemanteSupplierCode = $this->propertyManager()->propertyExistsByTag('remante_supplier_code');
        if (($propertyRemanteSupplierCode !== false) && ($this->getDataKey('property_remante_supplier_code_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyRemanteSupplierCode);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }
}
