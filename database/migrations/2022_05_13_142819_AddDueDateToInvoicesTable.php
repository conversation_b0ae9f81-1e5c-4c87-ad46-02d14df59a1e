<?php

use Buxus\Migration\AbstractMigration;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;

/**
 * Automatic generation from (rinoparts) at 2022-05-13 14:28:19
 */
class AddDueDateToInvoicesTable extends AbstractMigration
{
    public function up()
    {
        // update table onix_enclosures
        Schema::table('onix_enclosures', function (Blueprint $table) {
            $table->string('due_date');
            $table->string('payment_remaining');
            $table->string('payment_status');
        });
    }

    public function down()
    {
        // revert changes to table onix_enclosures
        Schema::table('onix_enclosures', function (Blueprint $table) {
            $table->dropColumn('due_date');
            $table->dropColumn('payment_remaining');
            $table->dropColumn('payment_status');
        });
    }
}
