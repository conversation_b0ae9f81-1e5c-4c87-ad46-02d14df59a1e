<?php

namespace Buxus\SeoUrlLegacy\Migrations;

use Buxus\Migration\AbstractMigration;
use Buxus\Util\DB as DBUtil;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class SeoUrlTables extends AbstractMigration
{
    public function up()
    {
        if (!Schema::hasTable('tblSeoUrl')) {
            DB::statement("
            CREATE TABLE `tblSeoUrl` (
              `page_id` int(11) NOT NULL,
              `protocol` enum('http://','https://') COLLATE utf8_unicode_ci NOT NULL DEFAULT 'http://',
              `domain` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT 'NULL',
              `path` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT 'NULL',
              `parameters` varchar(255) COLLATE utf8_unicode_ci DEFAULT 'NULL',
              `type` enum('automatic','manual') COLLATE utf8_unicode_ci NOT NULL DEFAULT 'automatic',
              `creation_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
              PRIMARY KEY (`page_id`),
              UNIQUE KEY `seo_url` (`protocol`,`domain`,`path`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci ROW_FORMAT=DYNAMIC;
          ");
        }

        if (!Schema::hasTable('tblSeoUrlArchive')) {
            DB::statement("
                CREATE TABLE `tblSeoUrlArchive` (
                  `page_id` int(11) NOT NULL,
                  `protocol` enum('http://','https://') COLLATE utf8_unicode_ci NOT NULL DEFAULT 'http://',
                  `domain` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT 'NULL',
                  `path` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT 'NULL',
                  `parameters` varchar(255) COLLATE utf8_unicode_ci DEFAULT 'NULL',
                  `archive_type` enum('archived','static','static_not_redirect') COLLATE utf8_unicode_ci NOT NULL DEFAULT 'archived',
                  `archive_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                  PRIMARY KEY (`protocol`,`domain`,`path`),
                  UNIQUE KEY `seo_url` (`protocol`,`domain`,`path`,`archive_type`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci ROW_FORMAT=DYNAMIC;
                ");
        }

        if (!Schema::hasColumn('tblSeoUrl', 'no_seo_url')) {
            DB::statement("ALTER TABLE `tblSeoUrl` ADD COLUMN `no_seo_url` INT(1) UNSIGNED NOT NULL DEFAULT '0' AFTER `domain`");
            DB::statement("ALTER TABLE `tblSeoUrl` DROP PRIMARY KEY, ADD PRIMARY KEY (`page_id`, `domain`)");

            // update old entries
            DB::statement("SET @disable_triggers := 1");
            DB::statement("UPDATE `tblSeoUrl` SET no_seo_url = 1 WHERE domain = '-NO-SEO-URL-'");
        }

        DB::connection()->getPdo()->exec("DROP TRIGGER IF EXISTS `seo_url_before_delete`");
        DB::connection()->getPdo()->exec("DROP TRIGGER IF EXISTS `seo_url_before_update`");

        $source_dir = __DIR__ . '/sql/';
        DBUtil::runSQLDump($source_dir . 'buxus_seo_url_trigger_update.sql');
        DBUtil::runSQLDump($source_dir . 'buxus_seo_url_trigger_delete.sql');
    }

    public function down()
    {
        if (Schema::hasTable('tblSeoUrlArchive')) {
            Schema::drop('tblSeoUrlArchive');
        }

        if (Schema::hasTable('tblSeoUrl')) {
            Schema::drop('tblSeoUrl');
        }
    }
}
