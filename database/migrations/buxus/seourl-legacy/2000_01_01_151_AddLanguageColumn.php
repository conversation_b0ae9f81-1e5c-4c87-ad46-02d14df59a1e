<?php
namespace Buxus\SeoUrlLegacy\Migrations;

use Buxus\Migration\AbstractMigration;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Buxus\Util\DB as DBUtil;

class AddLanguageColumn extends AbstractMigration {
    public function up() {
        if (!Schema::hasColumn('tblSeoUrl', 'lang')) {
            Schema::table('tblSeoUrl', function($table) {
                $table->string('lang', 6)->default('sk')->after('domain');
                $table->dropPrimary();
                $table->primary(['page_id','domain','lang'], 'primary_key');
            });
        }

        if (!Schema::hasColumn('tblSeoUrlArchive', 'lang')) {
            Schema::table('tblSeoUrlArchive', function($table) {
                $table->string('lang', 6)->default('sk')->after('domain');
                $table->dropPrimary();
                $table->primary(['protocol','domain','path', 'lang'], 'primary_key');
            });
        }

        DB::connection()->getPdo()->exec("DROP TRIGGER IF EXISTS `seo_url_before_delete`");
        DB::connection()->getPdo()->exec("DROP TRIGGER IF EXISTS `seo_url_before_update`");

        $source_dir = __DIR__ . '/sql/';
        DBUtil::runSQLDump($source_dir . 'buxus_seo_url_trigger_update_language.sql');
        DBUtil::runSQLDump($source_dir . 'buxus_seo_url_trigger_delete_language.sql');
    }

    public function down() {
    }

}