<?php

use Buxus\Migration\AbstractMigration;

/**
 * Automatic generation from (buxus_dev) at 2018-05-14 15:49:14
 * Page generator: page_id=259,260,261,262,263,264
 */

class Gdpr_service_pages extends AbstractMigration
{
	public function up()
	{

		// page: GDPR(ID: 259 TAG: gdpr)
		$page_id = $this->getPageIdByTag('gdpr');
		if ($page_id === NULL) {
			$page_type = $this->pageTypesManager()->getPageTypeByTag('folder');
			$page_259 = \PageFactory::create($this->getPageIdByTag('admin_settings'), $page_type->getId());
		} else {
			$page_259 = \PageFactory::get($page_id);
		}
		$page_259->setPageName('GDPR');
		$page_259->setPageTag('gdpr');
		$page_259->setPageStateId(1);
		$page_259->setPageClassId(1);
		// set template on MAIN PAGE index::error404
		$this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('folder'), 'index', 'error404');
		$page_259->save();

		// page: GDPR pravidlá a podmienky(ID: 260 TAG: gdpr_terms_and_conditions)
		$page_id = $this->getPageIdByTag('gdpr_terms_and_conditions');
		if ($page_id === NULL) {
			$page_type = $this->pageTypesManager()->getPageTypeByTag('article');
			$page_260 = \PageFactory::create($this->getPageIdByTag('gdpr'), $page_type->getId());
		} else {
			$page_260 = \PageFactory::get($page_id);
		}
		$page_260->setPageName('GDPR pravidlá a podmienky');
		$page_260->setPageTag('gdpr_terms_and_conditions');
		$page_260->setPageStateId(1);
		$page_260->setPageClassId(1);
		$page_260->setValue('title', 'Pravidlá a podmienky');
		$page_260->setValue('seo_url_name', '/pravidla-a-podmienky');
		$page_260->setValue('text', '<p><span>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</span></p>');
		$page_260->setValue('hide_on_domain', 'F');
		// set template buxus-gdpr::terms-and-conditions
		$page_260->getPageTemplate()->setController('buxus-gdpr');
		$page_260->getPageTemplate()->setAction('terms-and-conditions');
		$page_260->save();

		// page: Odvolanie súhlasu so spracovním osobných údajov(ID: 261 TAG: gdpr_withdraw_personal_data_access)
		$page_id = $this->getPageIdByTag('gdpr_withdraw_personal_data_access');
		if ($page_id === NULL) {
			$page_type = $this->pageTypesManager()->getPageTypeByTag('article');
			$page_261 = \PageFactory::create($this->getPageIdByTag('gdpr'), $page_type->getId());
		} else {
			$page_261 = \PageFactory::get($page_id);
		}
		$page_261->setPageName('Odvolanie súhlasu so spracovním osobných údajov');
		$page_261->setPageTag('gdpr_withdraw_personal_data_access');
		$page_261->setPageStateId(1);
		$page_261->setPageClassId(1);
		$page_261->setValue('title', 'Odvolanie súhlasu so spracovním osobných údajov');
		$page_261->setValue('seo_url_name', '/odvolanie-suhlasu-so-spracovnim-osobnych-udajov');
		$page_261->setValue('hide_on_domain', 'F');
		// set template buxus-gdpr::withdraw-personal-data-access
		$page_261->getPageTemplate()->setController('buxus-gdpr');
		$page_261->getPageTemplate()->setAction('withdraw-personal-data-access');
		$page_261->save();

		// page: Zmazanie digitálnej stopy(ID: 262 TAG: gdpr_remove_digital_footprint)
		$page_id = $this->getPageIdByTag('gdpr_remove_digital_footprint');
		if ($page_id === NULL) {
			$page_type = $this->pageTypesManager()->getPageTypeByTag('article');
			$page_262 = \PageFactory::create($this->getPageIdByTag('gdpr'), $page_type->getId());
		} else {
			$page_262 = \PageFactory::get($page_id);
		}
		$page_262->setPageName('Zmazanie digitálnej stopy');
		$page_262->setPageTag('gdpr_remove_digital_footprint');
		$page_262->setPageStateId(1);
		$page_262->setPageClassId(1);
		$page_262->setValue('title', 'Zmazanie digitálnej stopy');
		$page_262->setValue('seo_url_name', '/zmazanie-digitalnej-stopy');
		$page_262->setValue('hide_on_domain', 'F');
		// set template buxus-gdpr::remove-digital-footprint
		$page_262->getPageTemplate()->setController('buxus-gdpr');
		$page_262->getPageTemplate()->setAction('remove-digital-footprint');
		$page_262->save();

		// page: Odvolanie súhlasu so spracovním osobných údajov - odoslaný formulár(ID: 263 TAG: gdpr_withdraw_personal_data_access_sent)
		$page_id = $this->getPageIdByTag('gdpr_withdraw_personal_data_access_sent');
		if ($page_id === NULL) {
			$page_type = $this->pageTypesManager()->getPageTypeByTag('article');
			$page_263 = \PageFactory::create($this->getPageIdByTag('gdpr'), $page_type->getId());
		} else {
			$page_263 = \PageFactory::get($page_id);
		}
		$page_263->setPageName('Odvolanie súhlasu so spracovním osobných údajov - odoslaný formulár');
		$page_263->setPageTag('gdpr_withdraw_personal_data_access_sent');
		$page_263->setPageStateId(1);
		$page_263->setPageClassId(1);
		$page_263->setValue('title', 'Odvolanie súhlasu so spracovním osobných údajov');
		$page_263->setValue('seo_url_name', '-NO-SEO-URL-');
		$page_263->setValue('hide_on_domain', 'F');
		// set template on MAIN PAGE index::index
		$this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('article'), 'index', 'index');
		$page_263->save();

		// page: Zmazanie digitálnej stopy - odoslaný formulár(ID: 264 TAG: gdpr_remove_digital_footprint_sent)
		$page_id = $this->getPageIdByTag('gdpr_remove_digital_footprint_sent');
		if ($page_id === NULL) {
			$page_type = $this->pageTypesManager()->getPageTypeByTag('article');
			$page_264 = \PageFactory::create($this->getPageIdByTag('gdpr'), $page_type->getId());
		} else {
			$page_264 = \PageFactory::get($page_id);
		}
		$page_264->setPageName('Zmazanie digitálnej stopy - odoslaný formulár');
		$page_264->setPageTag('gdpr_remove_digital_footprint_sent');
		$page_264->setPageStateId(1);
		$page_264->setPageClassId(1);
		$page_264->setValue('title', 'Zmazanie digitálnej stopy');
		$page_264->setValue('seo_url_name', '/zmazanie-digitalnej-stopy-1');
		$page_264->setValue('hide_on_domain', 'F');
		// set template on MAIN PAGE index::index
		$this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('article'), 'index', 'index');
		$page_264->save();

        BuxusDB::get()->query("create table bx_gdpr_permissions (
                id int not null auto_increment
                    primary key,
                user_identificator varchar(255) not null,
                permission_value varchar(255) null,
                note text null,
                created_at datetime default null,
                permission_tag varchar(255) not null,
                source varchar(255) null
            );
        ");

        BuxusDB::get()->query("create table bx_gdpr_requests (
                id int not null auto_increment
                    primary key,
                state varchar(255) null,
                created_at datetime not null,
                updated_at datetime not null,
                request_type varchar(255) not null,
                note text null,
                user_identificator text not null
            );
        ");

	}

	public function down()
	{
		// remove page: Zmazanie digitálnej stopy - odoslaný formulár (gdpr_remove_digital_footprint_sent)
		$page_id = $this->getPageIdByTag('gdpr_remove_digital_footprint_sent');
		if ($page_id !== NULL) {
			\PageFactory::get($page_id)->delete();
		}

		// remove page: Odvolanie súhlasu so spracovním osobných údajov - odoslaný formulár (gdpr_withdraw_personal_data_access_sent)
		$page_id = $this->getPageIdByTag('gdpr_withdraw_personal_data_access_sent');
		if ($page_id !== NULL) {
			\PageFactory::get($page_id)->delete();
		}

		// remove page: Zmazanie digitálnej stopy (gdpr_remove_digital_footprint)
		$page_id = $this->getPageIdByTag('gdpr_remove_digital_footprint');
		if ($page_id !== NULL) {
			\PageFactory::get($page_id)->delete();
		}

		// remove page: Odvolanie súhlasu so spracovním osobných údajov (gdpr_withdraw_personal_data_access)
		$page_id = $this->getPageIdByTag('gdpr_withdraw_personal_data_access');
		if ($page_id !== NULL) {
			\PageFactory::get($page_id)->delete();
		}

		// remove page: GDPR pravidlá a podmienky (gdpr_terms_and_conditions)
		$page_id = $this->getPageIdByTag('gdpr_terms_and_conditions');
		if ($page_id !== NULL) {
			\PageFactory::get($page_id)->delete();
		}

		// remove page: GDPR (gdpr)
		$page_id = $this->getPageIdByTag('gdpr');
		if ($page_id !== NULL) {
			\PageFactory::get($page_id)->delete();
		}

        BuxusDB::get()->query("drop table bx_gdpr_permissions");
        BuxusDB::get()->query("drop table bx_gdpr_requests");
	}

}
