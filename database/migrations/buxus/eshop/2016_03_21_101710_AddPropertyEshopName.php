<?php

use Buxus\Migration\AbstractMigration;

/**
 * Automatic generation from (tshop-krabica) at 2016-03-21 10:17:10
 * Property generator: property=eshop_name
 */

class AddPropertyEshopName extends AbstractMigration {
	public function up() {

		// property: Názov eshopu(eshop_name)
		$property_eshop_name = $this->propertyManager()->propertyExistsByTag('eshop_name');
		if ($property_eshop_name === false) {
			$property_eshop_name = new \Buxus\Property\Types\Input();
			$property_eshop_name->setTag('eshop_name');
			$property_eshop_name->setDescription('Názov eshopu - použitie napr. na faktúru.');
			$property_eshop_name->setExtendedDescription('');
			$property_eshop_name->setName('Názov eshopu');
			$property_eshop_name->setClassId('4');
			$property_eshop_name->setShowType(NULL);
			$property_eshop_name->setShowTypeTag('text');
			$property_eshop_name->setValueType('oneline_text');
			$property_eshop_name->setDefaultValue('');
			$property_eshop_name->setMultiOperations(false);
			$property_eshop_name->setInputString('');
			$property_eshop_name->setAttribute('tab', '');
			$property_eshop_name->setAttribute('size', '60');
			$property_eshop_name->setAttribute('maxlength', '');
			$property_eshop_name->setAttribute('readonly', 'F');
			$property_eshop_name->setAttribute('pattern', '');
			$property_eshop_name->setAttribute('inherit_value', 'F');
			$property_eshop_name->setAttribute('onchange-js', '');
			$property_eshop_name->setAttribute('onkeyup-js', '');
			$property_eshop_name->setAttribute('onkeydown-js', '');
			$this->propertyManager()->saveProperty($property_eshop_name);
		} else {
			$this->writeLine('Property with tag eshop_name already exists');
			$this->setDataKey('property_eshop_name_existed', true);
		}

		// regenerate property constants
		$this->propertyManager()->generateConstants();

	}

	public function down() {
		// remove property: Názov eshopu(eshop_name)
		$property_eshop_name = $this->propertyManager()->propertyExistsByTag('eshop_name');
		if ($property_eshop_name != false) {
			$supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_eshop_name);
			if ((is_null($this->getDataKey('property_eshop_name_existed'))) && (count($supported_page_types) == 0)) {
				$this->propertyManager()->removeProperty($property_eshop_name);
			}
		}

		// regenerate property constants
		$this->propertyManager()->generateConstants();

	}

}
