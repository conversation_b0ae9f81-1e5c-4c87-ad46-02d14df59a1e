<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;

/**
 * Automatic generation from (buxus7_clean_install) at 2019-05-02 16:16:37
 * Property generator: property=eshop_discount_percent
 */
class AddDiscountPercentInheritedProperty extends AbstractMigration
{
    public function up()
    {
        // property: Percentuálna zľava(eshop_discount_percent)
        $propertyEshopDiscountPercent = $this->propertyManager()->propertyExistsByTag('eshop_discount_percent');
        if ($propertyEshopDiscountPercent === false) {
            $propertyEshopDiscountPercent = new Property();
            $propertyEshopDiscountPercent->setTag('eshop_discount_percent');
            $propertyEshopDiscountPercent->setDescription('Zľava uvedená v percentách');
            $propertyEshopDiscountPercent->setExtendedDescription('');
            $propertyEshopDiscountPercent->setName('Percentuálna zľava');
            $propertyEshopDiscountPercent->setClassId(4);
            $propertyEshopDiscountPercent->setShowType(null);
            $propertyEshopDiscountPercent->setShowTypeTag('number');
            $propertyEshopDiscountPercent->setValueType('number');
            $propertyEshopDiscountPercent->setDefaultValue('');
            $propertyEshopDiscountPercent->setMultiOperations(false);
            $propertyEshopDiscountPercent->setInputString('');
            $propertyEshopDiscountPercent->setAttribute('tab', '');
            $propertyEshopDiscountPercent->setAttribute('size', '10');
            $propertyEshopDiscountPercent->setAttribute('min', '0');
            $propertyEshopDiscountPercent->setAttribute('max', '100');
            $propertyEshopDiscountPercent->setAttribute('step', '0.1');
            $propertyEshopDiscountPercent->setAttribute('inherit_value', 'T');
            $propertyEshopDiscountPercent->setAttribute('onchange-js', '');
            $propertyEshopDiscountPercent->setAttribute('onkeyup-js', '');
            $propertyEshopDiscountPercent->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyEshopDiscountPercent);
        } else {
            $this->writeLine('Property with tag eshop_discount_percent already exists');
            $this->setDataKey('property_eshop_discount_percent_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('eshop_discount_percent', 'eshop_product', false);
        }
        if ($this->pageTypeExists('eshop_product_variant')) {
            $this->addPropertyToPageType('eshop_discount_percent', 'eshop_product_variant', false);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }

    public function down()
    {
        // remove property: Percentuálna zľava(eshop_discount_percent)
        $propertyEshopDiscountPercent = $this->propertyManager()->propertyExistsByTag('eshop_discount_percent');
        if ($propertyEshopDiscountPercent !== false) {
            $supportedPageTypes = $this->propertyManager()->getSupportedPageTypes($propertyEshopDiscountPercent);
            if (($this->getDataKey('property_eshop_discount_percent_existed') === null) && (count($supportedPageTypes) === 0)) {
                $this->propertyManager()->removeProperty($propertyEshopDiscountPercent);
            }
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }
}
