<?php

use Buxus\Migration\AbstractMigration;

/**
 * Automatic generation from (cyklo_local) at 2019-11-18 09:26:52
 */
class IncreaseLengthForIcoDicDrcEshopDatabaseFields extends AbstractMigration
{
    public function up()
    {
        \DB::statement("ALTER TABLE `tblShopOrders` MODIFY COLUMN `ico` VARCHAR(255);");
        \DB::statement("ALTER TABLE `tblShopOrders` MODIFY COLUMN `dic` VARCHAR(255);");
        \DB::statement("ALTER TABLE `tblShopOrders` MODIFY COLUMN `drc` VARCHAR(255);");
    }

    public function down()
    {
        \DB::statement("ALTER TABLE `tblShopOrders` MODIFY COLUMN `ico` CHAR(15);");
        \DB::statement("ALTER TABLE `tblShopOrders` MODIFY COLUMN `dic` CHAR(15);");
        \DB::statement("ALTER TABLE `tblShopOrders` MODIFY COLUMN `drc` CHAR(15);");
    }
}
