<?php

namespace Buxus\Translate\Migrations;

use Buxus\Migration\AbstractMigration;
use Buxus\Translate\StringManipulations;

class DefaultZendFormTranslationsMigration extends AbstractMigration
{
    protected static $data = [
        "notAlnum" => 'Hodnota "%value%" neobsahuje len abecedné a číselné znaky.',
        "stringEmpty" => 'Hodnota "%value%" je prázdny reťazec.',
        "notAlpha" => 'Hodnota "%value%" neobsahuje len abecedné znaky',
        "invalid" => 'Hodnota "%value%" nie je platný UPC-A čiarový kód.',
        "invalidLength" => 'Hodnota "%value%" nie je platný UPC-A čiarový kód.',
        "notBetween" => 'Hodnota "%value%" nie je medzi "%min%" a "%max%", vrátane.',
        "notBetweenStrict" => 'Hodnota "%value%" nie je medzi "%min%" a "%max%", nie vrátane.',
        "ccnumLength" => 'Hodnota "%value%" musí obsahovať 13 až 19 čísel.',
        "ccnumChecksum" => 'Luhnov algoritmus (mod-10 checksum) zlyhal na "%value%".',
        "dateNotYYYY" => 'MM-DD;Dátum "%value%" nemá formát YYYY-MM-DD.',
        "dateInvalid" => 'Hodnota "%value%" sa nejaví ako platný dátum.',
        "dateFalseFormat" => 'Dátum "%value%" nevyhovuje vyžadovanému formátu.',
        "notDigits" => 'Hodnota "%value%" neobsahuje len čísla.',
        "emailAddressInvalid" => 'Hodnota "%value%" nemá formát emailovej adresy.',
        "emailAddressInvalidHostname" => 'hostname%" nie je platný hostname pre emailovú adresu "%value%".',
        "emailAddressInvalidMxRecord" => 'Hostname "%hostname%" sa nejaví, že má platný MX záznam pre emailovú adresu "%value%".',
        "emailAddressDotAtom" => 'Hodnota "%localPart%" nemá "dot-atom" formát.',
        "emailAddressQuotedString" => 'Hodnota "%localPart%" nemá "quoted-string" formát.',
        "emailAddressInvalidLocalPart" => 'Hodnota "%localPart%" nie je platná časť emailovej adresy "%value%".',
        "notFloat" => 'Hodnota "%value%" sa nejaví ako reálne číslo.',
        "notGreaterThan" => 'Hodnota "%value%" nie je väčšia ako "%min%".',
        "notHex" => 'Hodnota "%value%" nemá iba hexadecimálne číslicové znaky',
        "hostnameIpAddressNotAllowed" => 'Hodnota "%value%" sa javí byť IP adresa, ale IP adresy nie sú povolené.',
        "hostnameUnknownTld" => 'Hodnota "%value%" sa javí byť DNS hostname, ale TLD sa nezhoduje so známym zoznamom.',
        "hostnameDashCharacter" => 'Hodnota "%value%" sa javí byť DNS hostname, ale obsahuje pomlčku (-) na nesprávnej pozícii.',
        "hostnameInvalidHostnameSchema" => 'Hodnota "%value%" sa javí byť DNS hostname, ale sa nezhoduje s hostname schémou pre TLD "%tld%".',
        "hostnameUndecipherableTld" => 'Hodnota "%value%" sa javí byť DNS hostname, ale nedá sa extrahovať TLD časť.',
        "hostnameInvalidHostname" => 'Hodnota "%value%" nezodpovedá štruktúre pre DNS hostname.',
        "hostnameInvalidLocalName" => 'Hodnota "%value%" sa nejaví ako platné meno lokálnej siete.',
        "hostnameLocalNameNotAllowed" => 'Hodnota "%value%" sa javí byť meno lokálnej siete, ale meno lokálnej siete nie je povolené.',
        "notSame" => 'Reťazce sa nezhodujú',
        "missingToken" => 'Nebol vložený reťazec pre kontrolu',
        "notInArray" => 'Hodnota "%value%" nebola nájdená medzi povolenými hodnotami.',
        "notInt" => 'Hodnota "%value%" sa nejaví ako celé číslo.',
        "notIpAddress" => 'Hodnota "%value%" nevyzerá ako platná IP adresa',
        "notLessThan" => 'Hodnota "%value%" nie je menšia ako "%max%"',
        "isEmpty" => 'Nevyplnili ste povinný údaj.',
        "regexNotMatch" => 'Hodnota "%value%" sa nezhoduje so vzorom "%pattern%"',
        "stringLengthTooShort" => 'Dĺžka reťazca "%value%" je menšia ako %min%.',
        "stringLengthTooLong" => 'Dĺžka reťazca %value%" is väčšia ako %max%.',
        "emailAddressInvalidFormat" => 'Hodnota "%value%" nemá formát emailovej adresy.',
        "missingValue" => 'Prázdna hodnota kontrolného reťazca.',
        "missingID" => 'Nie je definovaný ID pole pre kontrolný reťazec.',
        "badCaptcha" => 'Nesprávna hodnota kontrolného reťazca.',
    ];

    public function dependencies()
    {
        return [
            TranslationTableMigration::class,
        ];
    }

    public function up()
    {
        foreach (static::$data as $key => $value) {
            foreach (config('translate.languages') as $lang) {
                \DB::table('tblTranslations')->insert([
                    'lang' => $lang,
                    'collection' => 'zend',
                    'tag' => StringManipulations::transformTag($key),
                    'value' => $value,
                    'translated' => $lang === 'sk' ? 1 : 0,
                ]);
            }
        }
    }

    public function down()
    {
        $sql = '
            DELETE FROM tblTranslations
            WHERE `collection` = :collection
               AND `tag` = :tag
        ';

        $db = \BuxusDB::get();
        foreach (static::$data as $key => $value) {
            $db->query($sql, [
                ':collection' => 'zend',
                ':tag' => StringManipulations::transformTag($key),
            ]);
        }
    }

}
