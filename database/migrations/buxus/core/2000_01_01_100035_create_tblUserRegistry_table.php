<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateTblUserRegistryTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('tblUserRegistry', function(Blueprint $table)
		{
			$table->integer('user_id')->default(0);
			$table->string('key', 50)->default('');
			$table->binary('value', 65535)->nullable();
			$table->primary(['user_id','key']);
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('tblUserRegistry');
	}

}
