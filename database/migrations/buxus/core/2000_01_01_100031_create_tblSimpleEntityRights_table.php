<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateTblSimpleEntityRightsTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('tblSimpleEntityRights', function(Blueprint $table)
		{
			$table->integer('user_id')->default(0);
			$table->integer('entity_id')->default(0);
			$table->char('read_right', 1)->nullable()->default('T');
			$table->primary(['user_id','entity_id']);
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('tblSimpleEntityRights');
	}

}
