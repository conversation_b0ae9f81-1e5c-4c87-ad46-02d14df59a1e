<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateTblSubmitedFormsAttachmentsTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('tblSubmitedFormsAttachments', function(Blueprint $table)
		{
			$table->integer('file_id', true);
			$table->integer('form_submit_id')->nullable()->index('form_submit_id');
			$table->string('file_tag')->nullable();
			$table->string('file_name')->nullable();
			$table->binary('content')->nullable();
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('tblSubmitedFormsAttachments');
	}

}
