<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateTblPoolAnswersTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('tblPoolAnswers', function(Blueprint $table)
		{
			$table->integer('pool_answer_id', true);
			$table->integer('pool_id')->nullable()->index('pool_id');
			$table->text('the_answer', 65535)->nullable();
			$table->integer('vote_number')->nullable();
			$table->dateTime('first_vote_time')->nullable();
			$table->dateTime('last_vote_time')->nullable();
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('tblPoolAnswers');
	}

}
