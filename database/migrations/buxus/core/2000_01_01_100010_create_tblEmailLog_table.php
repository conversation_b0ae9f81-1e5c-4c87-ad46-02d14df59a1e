<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateTblEmailLogTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('tblEmailLog', function(Blueprint $table)
		{
			$table->integer('email_log_id', true);
			$table->string('tag', 40)->nullable()->default('');
			$table->integer('custom_id')->nullable();
			$table->string('email_from')->nullable()->default('');
			$table->string('email_subject')->nullable()->default('');
			$table->text('email_body', 16777215)->nullable();
			$table->string('email_recipients')->nullable()->default('');
			$table->timestamp('created')->nullable()->default(DB::raw('CURRENT_TIMESTAMP'));
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('tblEmailLog');
	}

}
