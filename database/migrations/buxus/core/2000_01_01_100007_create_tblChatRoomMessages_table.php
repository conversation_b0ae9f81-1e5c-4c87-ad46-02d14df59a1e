<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateTblChatRoomMessagesTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('tblChatRoomMessages', function(Blueprint $table)
		{
			$table->integer('chatroom_message_id', true);
			$table->string('chatroom_tag', 25)->default('')->index('chatroom_tag');
			$table->string('nickname', 25)->default('');
			$table->integer('timestamp')->default(0);
			$table->string('message');
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('tblChatRoomMessages');
	}

}
