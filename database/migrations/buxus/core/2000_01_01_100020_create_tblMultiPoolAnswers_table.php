<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateTblMultiPoolAnswersTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('tblMultiPoolAnswers', function(Blueprint $table)
		{
			$table->integer('pool_id')->default(0);
			$table->integer('pool_answer_id')->default(0);
			$table->integer('page_id')->default(0);
			$table->integer('vote_number')->nullable()->default(0);
			$table->primary(['pool_id','pool_answer_id','page_id']);
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('tblMultiPoolAnswers');
	}

}
