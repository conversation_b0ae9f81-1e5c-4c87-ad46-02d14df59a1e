<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateTblMessagesTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('tblMessages', function(Blueprint $table)
		{
			$table->integer('message_id', true);
			$table->string('forum_tag', 64)->nullable()->index('forum_tag');
			$table->string('subject', 128);
			$table->text('message', 65535)->nullable();
			$table->dateTime('creation_date');
			$table->string('author_name', 128);
			$table->text('author_e_mail', 65535);
			$table->integer('parent_message_id')->nullable()->index('parent_message_id');
			$table->char('send_answer_to_author', 1)->nullable();
			$table->char('send_this_to_parent_author', 1)->nullable();
			$table->char('show_e_mail_to_public', 1)->nullable();
			$table->char('active', 1)->nullable()->default('T');
			$table->string('author_ip_address', 64)->nullable();
			$table->integer('page_id');
			$table->integer('tree_cardinality')->nullable();
			$table->integer('web_user_id')->nullable();
			$table->string('reserved1')->nullable();
			$table->integer('reserved2')->nullable();
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('tblMessages');
	}

}
