<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateTblBuxusSearchTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tblBuxusSearch', function (Blueprint $table) {
            $table->integer('entity_id')->unsigned();
            $table->integer('entity_type')->unsigned()->index('entity_type');
            $table->string('property_name', 128);
            $table->text('property_value', 65535)->nullable();
            $table->primary(['entity_id', 'entity_type', 'property_name']);
            $table->engine = 'MyISAM';
        });
        if (env('DB_CONNECTION') == 'mysql') {

            DB::statement('ALTER TABLE tblBuxusSearch ADD FULLTEXT full(property_value)');
        }
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('tblBuxusSearch');
    }

}
