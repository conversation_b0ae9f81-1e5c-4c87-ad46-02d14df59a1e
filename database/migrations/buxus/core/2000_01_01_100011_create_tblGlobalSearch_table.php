<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateTblGlobalSearchTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tblGlobalSearch', function (Blueprint $table) {
            $table->integer('page_id')->default(0)->primary();
            $table->text('body_text', 65535)->nullable();
            $table->timestamp('time_stamp')->default(DB::raw('CURRENT_TIMESTAMP'));
        });
        if (env('DB_CONNECTION') == 'mysql') {
            DB::statement('ALTER TABLE tblGlobalSearch ADD FULLTEXT full(body_text)');
        }
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('tblGlobalSearch');
    }

}
