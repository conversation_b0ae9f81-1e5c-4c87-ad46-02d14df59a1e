<?php

use Buxus\Migration\AbstractMigration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTblPageTypesHierarchyTable extends AbstractMigration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
        if (!Schema::hasTable('tblPageTypesHierarchy')) {
            Schema::create('tblPageTypesHierarchy', function (Blueprint $table) {
                $table->integer('parent_page_type_id')->default(0);
                $table->integer('child_page_type_id')->default(0)->index('FK_tblPageTypes');
                $table->primary(['parent_page_type_id', 'child_page_type_id'], 'parent_child');
            });
        }
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('tblPageTypesHierarchy');
	}

}
