<?php

use Buxus\Migration\AbstractMigration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTblPageDraftsTable extends AbstractMigration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
        if (!Schema::hasTable('tblPageDrafts')) {
            Schema::create('tblPageDrafts', function (Blueprint $table) {
                $table->integer('page_id')->primary();
                $table->integer('parent_page_id')->nullable();
                $table->integer('user_id');
                $table->dateTime('creation_date');
                $table->text('properties')->nullable();
                $table->dateTime('last_updated')->nullable();
                $table->integer('last_updated_by_user_id')->nullable();
            });
        }
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('tblPageDrafts');
	}

}
