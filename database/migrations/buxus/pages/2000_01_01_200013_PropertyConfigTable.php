<?php
use Buxus\Migration\AbstractMigration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class PropertyConfigTable extends AbstractMigration
{
    public function dependencies()
    {
        return [CreateTblPropertiesTable::class];
    }

    public function supportedBuxusVersions()
    {
        return [7];
    }

    public function up()
    {
        if (config('database.default') === 'mysql') {
            \DB::unprepared('ALTER TABLE `tblProperties` ENGINE = InnoDB;');
            \DB::unprepared('ALTER TABLE `tblProperties` CHANGE `property_id` `property_id` INT(11)  UNSIGNED  NOT NULL  DEFAULT \'0\';');
        }
        Schema::create('bx_property_attributes', function (Blueprint $table) {
            $table->increments('id');
            $table->string('tag');
            $table->string('value')->nullable();
            $table->integer('property_id')->unsigned();
            $table->timestamps();
            $table->foreign('property_id')->references('property_id')->on('tblProperties')->onDelete('cascade');
            $table->unique(['tag', 'property_id']);
        });

        $rows = \DB::table('tblProperties')->get();
        foreach ($rows as $row) {
            $dom = simplexml_load_string('<root>' . html_entity_decode($row->property_properties) . '</root>');

            foreach ($dom as $element) {
                $attribute = new \Buxus\Property\Attributes\PropertyAttribute();
                $attribute->tag = $element->getName();
                $attribute->value = ((string)$element);
                $attribute->property_id = $row->property_id;
                $attribute->save();
            }
        }
    }

    public function down()
    {
        Schema::drop('bx_property_attributes');
    }

}
