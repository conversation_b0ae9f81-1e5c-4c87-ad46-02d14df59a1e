<?php

use Buxus\Migration\AbstractMigration;

class MakePropertyValuesMediumText extends AbstractMigration
{
    public function up()
    {
        DB::statement("ALTER TABLE `tblPagePropertyValues` MODIFY `property_value` MEDIUMTEXT NULL");
        DB::statement("ALTER TABLE `tblArchivePagePropertyValues` MODIFY `property_value` MEDIUMTEXT NULL");
        DB::statement("ALTER TABLE `tblPageDrafts` MODIFY `properties` LONGTEXT NULL");
    }

    public function down()
    {
        DB::statement("ALTER TABLE `tblPagePropertyValues` MODIFY `property_value` TEXT NULL");
        DB::statement("ALTER TABLE `tblArchivePagePropertyValues` MODIFY `property_value` TEXT NULL");
        DB::statement("ALTER TABLE `tblPageDrafts` MODIFY `properties` TEXT NULL");
    }
}
