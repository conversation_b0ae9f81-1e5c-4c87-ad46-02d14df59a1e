<?php

use Buxus\Migration\AbstractMigration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTblPagesTable extends AbstractMigration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
        if (!Schema::hasTable('tblPages')) {
            Schema::create('tblPages', function (Blueprint $table) {
                $table->integer('page_id', true);
                $table->string('page_name')->index('page_name')->nullable();
                $table->string('page_tag', 128)->nullable()->index('page_tag');
                $table->integer('author_id')->nullable();
                $table->dateTime('creation_date')->nullable();
                $table->integer('page_type_id')->nullable();
                $table->integer('page_state_id')->nullable();
                $table->integer('parent_page_id')->nullable()->index('parent_page_id');
                $table->integer('page_class_id')->nullable();
                $table->dateTime('last_updated')->nullable();
                $table->dateTime('sort_date_time')->nullable();
                $table->text('properties', 65535)->nullable();
                $table->integer('last_updated_by_user_id')->nullable();
            });
        }
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('tblPages');
	}

}
