<?php

use Buxus\Migration\AbstractMigration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTblPageTypesInsertRightTable extends AbstractMigration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
        if (!Schema::hasTable('tblPageTypesInsertRight')) {
            Schema::create('tblPageTypesInsertRight', function (Blueprint $table) {
                $table->integer('page_id')->unsigned();
                $table->integer('page_type_id')->unsigned();
                $table->enum('insert_right', array('T', 'F'));
                $table->primary(['page_id', 'page_type_id']);
            });
        }
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('tblPageTypesInsertRight');
	}

}
