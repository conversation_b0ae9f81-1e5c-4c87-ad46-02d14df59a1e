<?php

use <PERSON>uxus\Migration\AbstractMigration;
use Buxus\PageType\Facade\PageTypesManager;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTblPageTypesTable extends AbstractMigration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('tblPageTypes')) {
            Schema::create('tblPageTypes', function (Blueprint $table) {
                $table->integer('page_type_id')->default(0)->primary();
                $table->string('page_type_name')->nullable();
                $table->integer('parent_page_type_id')->nullable();
                $table->integer('page_class_id')->nullable();
                $table->string('page_type_tag', 128)->unique('page_type_tag');
                $table->string('page_type_description')->nullable();
                $table->string('page_details_layout', 100)->nullable();
                $table->char('auto_expand', 1)->nullable()->default('F');
                $table->string('page_sort_type_tag', 30)->nullable();
                $table->string('presubmit_trigger', 100)->nullable();
                $table->string('delete_trigger', 100)->nullable();
                $table->string('postsubmit_trigger', 100)->nullable();
                $table->string('premove_trigger', 100)->nullable();
                $table->string('postmove_trigger', 100)->nullable();
                $table->integer('default_template_id')->default(2);
                $table->integer('include_in_sync')->nullable()->default(0);
                $table->integer('page_type_order')->nullable();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('tblPageTypes');

        PageTypesManager::invalidateCache();
    }
}
