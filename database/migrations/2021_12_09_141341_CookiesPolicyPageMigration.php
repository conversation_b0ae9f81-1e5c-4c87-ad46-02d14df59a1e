<?php

use Buxus\Migration\AbstractMigration;

/**
 * Automatic generation from (rinoparts) at 2021-12-09 14:13:40
 * Page generator: page_id=376199,18
 */
class CookiesPolicyPageMigration extends AbstractMigration
{
    public function up()
    {
        // page: Pravidlá spracovávania cookies(ID: 376199 TAG: cookies_policy)
        $pageId = $this->getPageIdByTag('cookies_policy');
        if ($pageId === null) {
            $pageType = $this->pageTypesManager()->getPageTypeByTag('article');
            $page376199 = \PageFactory::create($this->getPageIdByTag('info_pages'), $pageType->getId());
        } else {
            $page376199 = \PageFactory::get($pageId);
        }
        $page376199->setPageName('Pravidlá spracovávania cookies');
        $page376199->setPageTag('cookies_policy');
        $page376199->setPageStateId('1');
        $page376199->setPageClassId(1);
        $page376199->setValue('seo_url_name_en', '/cookies-policy');
        $page376199->setValue('meta_title_en', '');
        $page376199->setValue('meta_description_en', '');
        $page376199->setValue('seo_url_name_cz', '/podminky-zpracovani-cookies');
        $page376199->setValue('meta_title_cz', '');
        $page376199->setValue('meta_description_cz', '');
        $page376199->setValue('meta_title', '');
        $page376199->setValue('meta_description', '');
        $page376199->setValue('title', 'Podmienky spracovávania cookies');
        $page376199->setValue('title_en', 'Cookies policy');
        $page376199->setValue('title_cz', 'Podmínky zpracování cookies');
        $page376199->setValue('seo_url_name', '/podmienky-spracovavania-cookies');
        $page376199->setValue('annotation', '');
        $page376199->setValue('annotation_en', '');
        $page376199->setValue('annotation_cz', '');
        $page376199->setValue('text', '');
        $page376199->setValue('text_en', '');
        $page376199->setValue('text_cz', '');
        $page376199->setValue('hide_on_domain', 'F');
        // set template on MAIN PAGE index::index
        $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('article'), 'index', 'index');
        $page376199->save();

        // page: Informácie(ID: 18 TAG: footer_information)
        $pageId = $this->getPageIdByTag('footer_information');
        if ($pageId === null) {
            $pageType = $this->pageTypesManager()->getPageTypeByTag('footer_column_links_text');
            $page18 = \PageFactory::create($this->getPageIdByTag('layout_footer'), $pageType->getId());
        } else {
            $page18 = \PageFactory::get($pageId);
        }
        $page18->setPageName('Informácie');
        $page18->setPageTag('footer_information');
        $page18->setPageStateId('1');
        $page18->setPageClassId(1);
        $page18->setValue('html5_semantic_element', '');
        $page18->setValue('css_class', 'footer-information col-lg-3');
        $page18->setValue('title', 'Informácie');
        $page18->setValue('title_en', '');
        $page18->setValue('title_cz', '');
        $page18->setValue('content_before', '');
        $page18->setValue('content_after', '');
        $page18->setValue('link_list', [
    [
        'id' => null,
        'property_id' => null,
        'from_page_id' => null,
        'to_page_id' => '22',
        'url' => '',
        'order_index' => '3',
        'text' => 'O nás',
        'default_text' => 'O nás',
    ],
    [
        'id' => null,
        'property_id' => null,
        'from_page_id' => null,
        'to_page_id' => '21',
        'url' => '',
        'order_index' => '4',
        'text' => 'Ako nakupovať',
        'default_text' => 'Ako nakupovať',
    ],
    [
        'id' => null,
        'property_id' => null,
        'from_page_id' => null,
        'to_page_id' => '376199',
        'url' => '',
        'order_index' => '5',
        'text' => 'Podmienky spracovávania cookies',
        'default_text' => 'Podmienky spracovávania cookies',
    ],
    [
        'id' => null,
        'property_id' => null,
        'from_page_id' => null,
        'to_page_id' => '187',
        'url' => '',
        'order_index' => '6',
        'text' => 'Pravidlá a podmienky',
        'default_text' => 'Pravidlá a podmienky',
    ],
]);
        $page18->save();
    }

    public function down()
    {
        // remove page: Informácie (footer_information)
        $pageId = $this->getPageIdByTag('footer_information');
        if ($pageId !== null) {
            \PageFactory::get($pageId)->delete();
        }

        // remove page: Pravidlá spracovávania cookies (cookies_policy)
        $pageId = $this->getPageIdByTag('cookies_policy');
        if ($pageId !== null) {
            \PageFactory::get($pageId)->delete();
        }
    }
}
