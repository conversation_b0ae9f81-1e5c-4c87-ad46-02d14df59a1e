<?php

use Buxus\Migration\AbstractMigration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * Automatic generation from (buxus_rinoparts_test) at 2021-11-05 11:42:18
 */
class OnixRequests extends AbstractMigration
{
    public function up()
    {
        // create table imports
        Schema::create('onix_requests', function (Blueprint $table) {
            $table->bigIncrements('id');

            $table->string('method');
            $table->float('response_time');
            $table->text('request');
            $table->text('request_data')->nullable()->default(null);

            $table->string('http_status_code');
            $table->string('content_length');
            $table->string('result_code')->nullable()->default(null);
            $table->text('errors')->nullable()->default(null);
            $table->text('warnings')->nullable()->default(null);
            $table->text('result_data')->nullable()->default(null);

            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('onix_requests');
    }
}
