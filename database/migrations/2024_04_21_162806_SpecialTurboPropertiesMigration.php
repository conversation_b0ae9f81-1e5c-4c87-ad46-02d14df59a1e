<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;

/**
 * Automatic generation from (rinoparts) at 2024-01-26 13:38:06
 * Property generator: property=special_turbo_supplier_code,special_turbo_price_without_vat,special_turbo_stock_balance,special_turbo_oe_number,special_turbo_oe_numbers,special_turbo_latest_import
 */
class SpecialTurboPropertiesMigration extends AbstractMigration
{
    public function up()
    {
        // property: Special Turbo - Dodávateľský kód(special_turbo_supplier_code)
        $propertySpecialTurboSupplierCode = $this->propertyManager()->propertyExistsByTag('special_turbo_supplier_code');
        if ($propertySpecialTurboSupplierCode === false) {
            $propertySpecialTurboSupplierCode = new Property();
            $propertySpecialTurboSupplierCode->setTag('special_turbo_supplier_code');
            $propertySpecialTurboSupplierCode->setDescription('');
            $propertySpecialTurboSupplierCode->setExtendedDescription('');
            $propertySpecialTurboSupplierCode->setName('Special Turbo - Dodávateľský kód');
            $propertySpecialTurboSupplierCode->setClassId(4);
            $propertySpecialTurboSupplierCode->setShowType(null);
            $propertySpecialTurboSupplierCode->setShowTypeTag('text');
            $propertySpecialTurboSupplierCode->setValueType('oneline_text');
            $propertySpecialTurboSupplierCode->setDefaultValue('');
            $propertySpecialTurboSupplierCode->setMultiOperations(false);
            $propertySpecialTurboSupplierCode->setInputString('');
            $propertySpecialTurboSupplierCode->setAttribute('tab', 'SpecialTurbo');
            $propertySpecialTurboSupplierCode->setAttribute('size', '60');
            $propertySpecialTurboSupplierCode->setAttribute('maxlength', '');
            $propertySpecialTurboSupplierCode->setAttribute('readonly', 'F');
            $propertySpecialTurboSupplierCode->setAttribute('pattern', '');
            $propertySpecialTurboSupplierCode->setAttribute('inherit_value', 'F');
            $propertySpecialTurboSupplierCode->setAttribute('onchange-js', '');
            $propertySpecialTurboSupplierCode->setAttribute('onkeyup-js', '');
            $propertySpecialTurboSupplierCode->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertySpecialTurboSupplierCode);
        } else {
            $this->writeLine('Property with tag special_turbo_supplier_code already exists');
            $this->setDataKey('property_special_turbo_supplier_code_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('special_turbo_supplier_code', 'eshop_product', false);
        }

        // property: Special Turbo - Cena bez DPH(special_turbo_price_without_vat)
        $propertySpecialTurboPriceWithoutVat = $this->propertyManager()->propertyExistsByTag('special_turbo_price_without_vat');
        if ($propertySpecialTurboPriceWithoutVat === false) {
            $propertySpecialTurboPriceWithoutVat = new Property();
            $propertySpecialTurboPriceWithoutVat->setTag('special_turbo_price_without_vat');
            $propertySpecialTurboPriceWithoutVat->setDescription('');
            $propertySpecialTurboPriceWithoutVat->setExtendedDescription('');
            $propertySpecialTurboPriceWithoutVat->setName('Special Turbo - Cena bez DPH');
            $propertySpecialTurboPriceWithoutVat->setClassId(4);
            $propertySpecialTurboPriceWithoutVat->setShowType(null);
            $propertySpecialTurboPriceWithoutVat->setShowTypeTag('text');
            $propertySpecialTurboPriceWithoutVat->setValueType('oneline_text');
            $propertySpecialTurboPriceWithoutVat->setDefaultValue('');
            $propertySpecialTurboPriceWithoutVat->setMultiOperations(false);
            $propertySpecialTurboPriceWithoutVat->setInputString('');
            $propertySpecialTurboPriceWithoutVat->setAttribute('tab', 'SpecialTurbo');
            $propertySpecialTurboPriceWithoutVat->setAttribute('size', '60');
            $propertySpecialTurboPriceWithoutVat->setAttribute('maxlength', '');
            $propertySpecialTurboPriceWithoutVat->setAttribute('readonly', 'F');
            $propertySpecialTurboPriceWithoutVat->setAttribute('pattern', '');
            $propertySpecialTurboPriceWithoutVat->setAttribute('inherit_value', 'F');
            $propertySpecialTurboPriceWithoutVat->setAttribute('onchange-js', '');
            $propertySpecialTurboPriceWithoutVat->setAttribute('onkeyup-js', '');
            $propertySpecialTurboPriceWithoutVat->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertySpecialTurboPriceWithoutVat);
        } else {
            $this->writeLine('Property with tag special_turbo_price_without_vat already exists');
            $this->setDataKey('property_special_turbo_price_without_vat_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('special_turbo_price_without_vat', 'eshop_product', false);
        }

        // property: Special Turbo - Skladová zásoba(special_turbo_stock_balance)
        $propertySpecialTurboStockBalance = $this->propertyManager()->propertyExistsByTag('special_turbo_stock_balance');
        if ($propertySpecialTurboStockBalance === false) {
            $propertySpecialTurboStockBalance = new Property();
            $propertySpecialTurboStockBalance->setTag('special_turbo_stock_balance');
            $propertySpecialTurboStockBalance->setDescription('');
            $propertySpecialTurboStockBalance->setExtendedDescription('');
            $propertySpecialTurboStockBalance->setName('Special Turbo - Skladová zásoba');
            $propertySpecialTurboStockBalance->setClassId(4);
            $propertySpecialTurboStockBalance->setShowType(null);
            $propertySpecialTurboStockBalance->setShowTypeTag('text');
            $propertySpecialTurboStockBalance->setValueType('oneline_text');
            $propertySpecialTurboStockBalance->setDefaultValue('');
            $propertySpecialTurboStockBalance->setMultiOperations(false);
            $propertySpecialTurboStockBalance->setInputString('');
            $propertySpecialTurboStockBalance->setAttribute('tab', 'SpecialTurbo');
            $propertySpecialTurboStockBalance->setAttribute('size', '60');
            $propertySpecialTurboStockBalance->setAttribute('maxlength', '');
            $propertySpecialTurboStockBalance->setAttribute('readonly', 'F');
            $propertySpecialTurboStockBalance->setAttribute('pattern', '');
            $propertySpecialTurboStockBalance->setAttribute('inherit_value', 'F');
            $propertySpecialTurboStockBalance->setAttribute('onchange-js', '');
            $propertySpecialTurboStockBalance->setAttribute('onkeyup-js', '');
            $propertySpecialTurboStockBalance->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertySpecialTurboStockBalance);
        } else {
            $this->writeLine('Property with tag special_turbo_stock_balance already exists');
            $this->setDataKey('property_special_turbo_stock_balance_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('special_turbo_stock_balance', 'eshop_product', false);
        }

        // property: Special Turbo - OE number(special_turbo_oe_number)
        $propertySpecialTurboOeNumber = $this->propertyManager()->propertyExistsByTag('special_turbo_oe_number');
        if ($propertySpecialTurboOeNumber === false) {
            $propertySpecialTurboOeNumber = new Property();
            $propertySpecialTurboOeNumber->setTag('special_turbo_oe_number');
            $propertySpecialTurboOeNumber->setDescription('');
            $propertySpecialTurboOeNumber->setExtendedDescription('');
            $propertySpecialTurboOeNumber->setName('Special Turbo - OE number');
            $propertySpecialTurboOeNumber->setClassId(4);
            $propertySpecialTurboOeNumber->setShowType(null);
            $propertySpecialTurboOeNumber->setShowTypeTag('text');
            $propertySpecialTurboOeNumber->setValueType('oneline_text');
            $propertySpecialTurboOeNumber->setDefaultValue('');
            $propertySpecialTurboOeNumber->setMultiOperations(false);
            $propertySpecialTurboOeNumber->setInputString('');
            $propertySpecialTurboOeNumber->setAttribute('tab', 'SpecialTurbo');
            $propertySpecialTurboOeNumber->setAttribute('size', '60');
            $propertySpecialTurboOeNumber->setAttribute('maxlength', '');
            $propertySpecialTurboOeNumber->setAttribute('readonly', 'F');
            $propertySpecialTurboOeNumber->setAttribute('pattern', '');
            $propertySpecialTurboOeNumber->setAttribute('inherit_value', 'F');
            $propertySpecialTurboOeNumber->setAttribute('onchange-js', '');
            $propertySpecialTurboOeNumber->setAttribute('onkeyup-js', '');
            $propertySpecialTurboOeNumber->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertySpecialTurboOeNumber);
        } else {
            $this->writeLine('Property with tag special_turbo_oe_number already exists');
            $this->setDataKey('property_special_turbo_oe_number_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('special_turbo_oe_number', 'eshop_product', false);
        }

        // property: Special Turbo - OE numbers(special_turbo_oe_numbers)
        $propertySpecialTurboOeNumbers = $this->propertyManager()->propertyExistsByTag('special_turbo_oe_numbers');
        if ($propertySpecialTurboOeNumbers === false) {
            $propertySpecialTurboOeNumbers = new Property();
            $propertySpecialTurboOeNumbers->setTag('special_turbo_oe_numbers');
            $propertySpecialTurboOeNumbers->setDescription('');
            $propertySpecialTurboOeNumbers->setExtendedDescription('');
            $propertySpecialTurboOeNumbers->setName('Special Turbo - OE numbers');
            $propertySpecialTurboOeNumbers->setClassId(4);
            $propertySpecialTurboOeNumbers->setShowType(null);
            $propertySpecialTurboOeNumbers->setShowTypeTag('text');
            $propertySpecialTurboOeNumbers->setValueType('oneline_text');
            $propertySpecialTurboOeNumbers->setDefaultValue('');
            $propertySpecialTurboOeNumbers->setMultiOperations(false);
            $propertySpecialTurboOeNumbers->setInputString('');
            $propertySpecialTurboOeNumbers->setAttribute('tab', 'SpecialTurbo');
            $propertySpecialTurboOeNumbers->setAttribute('size', '60');
            $propertySpecialTurboOeNumbers->setAttribute('maxlength', '');
            $propertySpecialTurboOeNumbers->setAttribute('readonly', 'F');
            $propertySpecialTurboOeNumbers->setAttribute('pattern', '');
            $propertySpecialTurboOeNumbers->setAttribute('inherit_value', 'F');
            $propertySpecialTurboOeNumbers->setAttribute('onchange-js', '');
            $propertySpecialTurboOeNumbers->setAttribute('onkeyup-js', '');
            $propertySpecialTurboOeNumbers->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertySpecialTurboOeNumbers);
        } else {
            $this->writeLine('Property with tag special_turbo_oe_numbers already exists');
            $this->setDataKey('property_special_turbo_oe_numbers_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('special_turbo_oe_numbers', 'eshop_product', false);
        }

        // property: Special Turbo - Posledný import(special_turbo_latest_import)
        $propertySpecialTurboLatestImport = $this->propertyManager()->propertyExistsByTag('special_turbo_latest_import');
        if ($propertySpecialTurboLatestImport === false) {
            $propertySpecialTurboLatestImport = new Property();
            $propertySpecialTurboLatestImport->setTag('special_turbo_latest_import');
            $propertySpecialTurboLatestImport->setDescription('');
            $propertySpecialTurboLatestImport->setExtendedDescription('');
            $propertySpecialTurboLatestImport->setName('Special Turbo - Posledný import');
            $propertySpecialTurboLatestImport->setClassId(4);
            $propertySpecialTurboLatestImport->setShowType(null);
            $propertySpecialTurboLatestImport->setShowTypeTag('text');
            $propertySpecialTurboLatestImport->setValueType('oneline_text');
            $propertySpecialTurboLatestImport->setDefaultValue('');
            $propertySpecialTurboLatestImport->setMultiOperations(false);
            $propertySpecialTurboLatestImport->setInputString('');
            $propertySpecialTurboLatestImport->setAttribute('tab', 'SpecialTurbo');
            $propertySpecialTurboLatestImport->setAttribute('size', '60');
            $propertySpecialTurboLatestImport->setAttribute('maxlength', '');
            $propertySpecialTurboLatestImport->setAttribute('readonly', 'F');
            $propertySpecialTurboLatestImport->setAttribute('pattern', '');
            $propertySpecialTurboLatestImport->setAttribute('inherit_value', 'F');
            $propertySpecialTurboLatestImport->setAttribute('onchange-js', '');
            $propertySpecialTurboLatestImport->setAttribute('onkeyup-js', '');
            $propertySpecialTurboLatestImport->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertySpecialTurboLatestImport);
        } else {
            $this->writeLine('Property with tag special_turbo_latest_import already exists');
            $this->setDataKey('property_special_turbo_latest_import_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('special_turbo_latest_import', 'eshop_product', false);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }

    public function down()
    {
        // remove property: Special Turbo - Posledný import(special_turbo_latest_import)
        $propertySpecialTurboLatestImport = $this->propertyManager()->propertyExistsByTag('special_turbo_latest_import');
        if (($propertySpecialTurboLatestImport !== false) && ($this->getDataKey('property_special_turbo_latest_import_existed') === null)) {
            $this->propertyManager()->removeProperty($propertySpecialTurboLatestImport);
        }

        // remove property: Special Turbo - OE numbers(special_turbo_oe_numbers)
        $propertySpecialTurboOeNumbers = $this->propertyManager()->propertyExistsByTag('special_turbo_oe_numbers');
        if (($propertySpecialTurboOeNumbers !== false) && ($this->getDataKey('property_special_turbo_oe_numbers_existed') === null)) {
            $this->propertyManager()->removeProperty($propertySpecialTurboOeNumbers);
        }

        // remove property: Special Turbo - OE number(special_turbo_oe_number)
        $propertySpecialTurboOeNumber = $this->propertyManager()->propertyExistsByTag('special_turbo_oe_number');
        if (($propertySpecialTurboOeNumber !== false) && ($this->getDataKey('property_special_turbo_oe_number_existed') === null)) {
            $this->propertyManager()->removeProperty($propertySpecialTurboOeNumber);
        }

        // remove property: Special Turbo - Skladová zásoba(special_turbo_stock_balance)
        $propertySpecialTurboStockBalance = $this->propertyManager()->propertyExistsByTag('special_turbo_stock_balance');
        if (($propertySpecialTurboStockBalance !== false) && ($this->getDataKey('property_special_turbo_stock_balance_existed') === null)) {
            $this->propertyManager()->removeProperty($propertySpecialTurboStockBalance);
        }

        // remove property: Special Turbo - Cena bez DPH(special_turbo_price_without_vat)
        $propertySpecialTurboPriceWithoutVat = $this->propertyManager()->propertyExistsByTag('special_turbo_price_without_vat');
        if (($propertySpecialTurboPriceWithoutVat !== false) && ($this->getDataKey('property_special_turbo_price_without_vat_existed') === null)) {
            $this->propertyManager()->removeProperty($propertySpecialTurboPriceWithoutVat);
        }

        // remove property: Special Turbo - Dodávateľský kód(special_turbo_supplier_code)
        $propertySpecialTurboSupplierCode = $this->propertyManager()->propertyExistsByTag('special_turbo_supplier_code');
        if (($propertySpecialTurboSupplierCode !== false) && ($this->getDataKey('property_special_turbo_supplier_code_existed') === null)) {
            $this->propertyManager()->removeProperty($propertySpecialTurboSupplierCode);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }
}
