<?php

use Buxus\Migration\AbstractMigration;

/**
 * Automatic generation from (rinoparts) at 2022-05-18 08:59:20
 * Page generator: page_id=376215
 */
class InformationEmailsFolderMigration extends AbstractMigration
{
    public function up()
    {
        // page: Informačné emaily(ID: 376215 TAG: information_emails)
        $pageId = $this->getPageIdByTag('information_emails');
        if ($pageId === null) {
            $pageType = $this->pageTypesManager()->getPageTypeByTag('folder');
            $page376215 = \PageFactory::create($this->getPageIdByTag('E-maily shop modulu'), $pageType->getId());
        } else {
            $page376215 = \PageFactory::get($pageId);
        }
        $page376215->setPageName('Informačné emaily');
        $page376215->setPageTag('information_emails');
        $page376215->setPageStateId('2');
        $page376215->setPageClassId(1);
        // set template on MAIN PAGE index::error404
        $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('folder'), 'index', 'error404');
        $page376215->save();

        // page: Nevyplnené ONIX ID - email(ID: 376216 TAG: empty_onix_id_email)
        $pageType = $this->pageTypesManager()->getPageTypeByTag('basic_email');
        $page376216 = \PageFactory::create($page376215->getPageId(), $pageType->getId());
        $page376216->setPageName('Nevyplnené ONIX ID - email');
        $page376216->setPageTag('empty_onix_id_email');
        $page376216->setPageStateId(2);
        $page376216->setPageClassId(1);
        $page376216->setSortDateTime(date('Y-m-d H:i:s', time() + 0));
        $page376216->setValue('email_sender', '<EMAIL>');
        $page376216->setValue('email_recipients', '<EMAIL>');
        $page376216->setValue('email_subject', 'Nevyplnené ONIX ID pri objednávke s VS: {{VARIABLE_SYMBOL}}');
        $page376216->setValue('mail_embed_images', 'F');
        $page376216->setValue('email_body_html', '<p>Pri objednávke s variabilným symbolom <strong>{{VARIABLE_SYMBOL}}&nbsp;</strong>je nevyplnené ONIX ID.<br /><br />Detaily používateľa<br /><br />ID: {{USER_ID}}, FIRMA {{COMPANY_NAME}}<br /><a href="{{LINK}}">Preklik na používateľa {{USER_ID}}</a></p>');
        $page376216->setValue('rendered_email_bottom_text', '');
        $page376216->setValue('email_body_html_cz', '');
        $page376216->setValue('email_body_html_en', '');
        $page376216->setValue('email_subject_cz', '');
        $page376216->setValue('email_subject_en', '');
        $page376216->setValue('rendered_email_bottom_text_cz', '');
        $page376216->setValue('rendered_email_bottom_text_en', '');
        $page376216->setValue('attachment_list', []);
        // set template on MAIN PAGE index::error404
        $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('basic_email'), 'index', 'error404');
        $page376216->save();
    }

    public function down()
    {
        // remove page: Informačné emaily (information_emails)
        $pageId = $this->getPageIdByTag('information_emails');
        if ($pageId !== null) {
            \PageFactory::get($pageId)->delete();
        }
    }
}
