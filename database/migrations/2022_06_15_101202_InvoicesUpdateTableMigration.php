<?php

use Buxus\Migration\AbstractMigration;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;

/**
 * Automatic generation from (rinoparts) at 2022-06-15 10:12:02
 */
class InvoicesUpdateTableMigration extends AbstractMigration
{
    public function up()
    {
        // update table tblInvoicesUpdateLog
        Schema::create('tblInvoicesUpdateLog', function (Blueprint $table) {
            $table->unsignedBigInteger('id', true);
            $table->string('update_tag');
            $table->string('state');
            $table->timestamp('created_at');
        });
    }

    public function down()
    {
        // revert changes to table tblInvoicesUpdateLog
        Schema::dropIfExists('tblInvoicesUpdateLog');
    }
}
