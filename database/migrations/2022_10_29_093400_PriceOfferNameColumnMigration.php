<?php

use Buxus\Migration\AbstractMigration;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;

/**
 * Automatic generation from (rinoparts) at 2022-10-29 09:34:00
 */
class PriceOfferNameColumnMigration extends AbstractMigration
{
    public function up()
    {
        // update table tblPriceOffers
        Schema::table('tblPriceOffers', function (Blueprint $table) {
            $table->string('name')->after('id')->nullable();
        });
    }

    public function down()
    {
        // revert changes to table tblPriceOffers
        Schema::table('tblPriceOffers', function (Blueprint $table) {
            $table->dropColumn('name');
        });
    }
}
