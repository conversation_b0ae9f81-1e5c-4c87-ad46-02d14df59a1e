<?php

use Buxus\Migration\AbstractMigration;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;

/**
 * Automatic generation from (buxus_rinoparts_test) at 2022-01-19 14:31:49
 */
class OnixCards extends AbstractMigration
{
    public function up()
    {
        Schema::create('onix_cards', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->datetime('inserted');
            $table->bigInteger('product_id');
            $table->string('product_name');
        });
    }

    public function down()
    {
        Schema::dropIfExists('onix_cards');
    }
}
