<?php

namespace Krabica\Migrations;

use Buxus\Migration\AbstractMigration;
use Buxus\Util\PageIds;

class DynamicCategoriesDemo extends AbstractMigration
{
    public function dependencies()
    {
        return array(
            '\DynamicCategory\Migrations\DynamicCategories',
        );
    }

    public function up()
    {
        // page: Akciové produkty(ID: 147 TAG: akciove_produkty)
        $page_id = $this->getPageIdByTag('akciove_produkty');
        if ($page_id === NULL) {
            $page_type = $this->pageTypesManager()->getPageTypeByTag('dynamic_category');
            $page_147 = \PageFactory::create($this->getPageIdByTag('eshop_catalog'), $page_type->getId());
        } else {
            $page_147 = \PageFactory::get($page_id);
        }
        $page_147->setPageName('Akciové produkty');
        $page_147->setPageTag('akciove_produkty');
        $page_147->setPageStateId('1');
        $page_147->setPageClassId('1');
        $page_147->setValue('title', 'Akciové produkty');
        $page_147->setValue('query', '{"type":"eshop_product_akcia","data":null,"inv":"0"}');
        $page_147->setValue('filter_mask', 'a:3:{s:5:"price";b:1;s:8:"category";b:1;s:5:"flasg";b:0;}');
        $page_147->setValue('seo_url_name', '/katalog-produktov/akciove-produkty');
        // set template on MAIN PAGE eshop_catalog::dynamic-product-list
        $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('dynamic_category'), 'product-catalog', 'dynamic-product-list');
        $page_147->save();

        // regenerate page tags
        PageIds::generatePageTagsList();
    }

    public function down()
    {
        // remove page: Akciové produkty (akciove_produkty)
        $page_id = $this->getPageIdByTag('akciove_produkty');
        if ($page_id !== NULL) {
            \PageFactory::get($page_id)->delete();
        }

        // regenerate page tags
        PageIds::generatePageTagsList();
    }
}