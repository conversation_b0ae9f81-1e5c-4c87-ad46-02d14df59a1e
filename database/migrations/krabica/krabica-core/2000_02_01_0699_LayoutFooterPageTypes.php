<?php

namespace Krabica\Migrations;

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;
use Buxus\PageType\PageType;
use Buxus\PageType\PageTypePropertyItem;

/**
 * Automatic generation from (buxus_dev) at 2019-09-06 13:58:00
 * PageType generator: page_type=footer_column_links_text,fat_footer
 * Page generator: page_id=279
 */
class LayoutFooterPageTypes extends AbstractMigration
{
    public function dependencies()
    {
        return array(
            '\Layout\Migrations\LayoutBasic',
        );
    }

    public function up()
    {
        // property: HTML5 Semantic Element(html5_semantic_element)
        $propertyHtml5SemanticElement = $this->propertyManager()->propertyExistsByTag('html5_semantic_element');
        if ($propertyHtml5SemanticElement === false) {
            $propertyHtml5SemanticElement = new Property();
            $propertyHtml5SemanticElement->setTag('html5_semantic_element');
            $propertyHtml5SemanticElement->setDescription('HTML5 Semantic Element
empty or:
    header
    nav
    section
    article
    aside
    figure
    figcaption
    footer
    details
    summary
    mark
    time
');
            $propertyHtml5SemanticElement->setExtendedDescription('');
            $propertyHtml5SemanticElement->setName('HTML5 Semantic Element');
            $propertyHtml5SemanticElement->setClassId(4);
            $propertyHtml5SemanticElement->setShowType(null);
            $propertyHtml5SemanticElement->setShowTypeTag('text');
            $propertyHtml5SemanticElement->setValueType('oneline_text');
            $propertyHtml5SemanticElement->setDefaultValue('');
            $propertyHtml5SemanticElement->setMultiOperations(false);
            $propertyHtml5SemanticElement->setInputString(null);
            $propertyHtml5SemanticElement->setAttribute('tab', '');
            $propertyHtml5SemanticElement->setAttribute('size', '60');
            $propertyHtml5SemanticElement->setAttribute('maxlength', '');
            $propertyHtml5SemanticElement->setAttribute('readonly', 'F');
            $propertyHtml5SemanticElement->setAttribute('pattern', '');
            $propertyHtml5SemanticElement->setAttribute('inherit_value', 'F');
            $propertyHtml5SemanticElement->setAttribute('onchange-js', '');
            $propertyHtml5SemanticElement->setAttribute('onkeyup-js', '');
            $propertyHtml5SemanticElement->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyHtml5SemanticElement);
        } else {
            $this->writeLine('Property with tag html5_semantic_element already exists');
            $this->setDataKey('property_html5_semantic_element_existed', true);
        }

        // page type: Layout (layout)
        $pageTypeLayout = $this->pageTypesManager()->pageTypeExistsByTag('layout');
        if ($pageTypeLayout === false) {
            $pageTypeLayout = new PageType();
            $pageTypeLayout->setTag('layout');
            $pageTypeLayout->setName('Layout');
            $pageTypeLayout->setPageClassId(1);
            $pageTypeLayout->setDefaultTemplateId(2);
            $pageTypeLayout->setDeleteTrigger('');
            $pageTypeLayout->setIncludeInSync(null);
            $pageTypeLayout->setPageDetailsLayout('');
            $pageTypeLayout->setPageSortTypeTag('sort_date_time');
            $pageTypeLayout->setPageTypeOrder(500);
            $pageTypeLayout->setPostmoveTrigger('');
            $pageTypeLayout->setPostsubmitTrigger('');
            $pageTypeLayout->setPresubmitTrigger('');
            $pageTypeLayout->setParent(null);
        } else {
            $this->writeLine('Page type with tag layout already exists');
            $this->setDataKey('page_type_layout_existed', true);
        }
        $property = $this->propertyManager()->getPropertyByTag('html5_semantic_element');
        $propertyId = $property->getId();
        $tmp = $pageTypeLayout->getPropertyItemForPropertyId($propertyId);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder(1);
            $tmp->setRequired(false);
            $pageTypeLayout->addPropertyItem($tmp);
        }
        $this->pageTypesManager()->savePageType($pageTypeLayout);
        // set template on MAIN PAGE layout::layout
        $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('layout'), 'layout', 'layout');

        // property: CSS trieda(css_class)
        $propertyCssClass = $this->propertyManager()->propertyExistsByTag('css_class');
        if ($propertyCssClass === false) {
            $propertyCssClass = new Property();
            $propertyCssClass->setTag('css_class');
            $propertyCssClass->setDescription('CSS trieda alebo zoznam tried oddelený medzerou');
            $propertyCssClass->setExtendedDescription('');
            $propertyCssClass->setName('CSS trieda');
            $propertyCssClass->setClassId(4);
            $propertyCssClass->setShowType(null);
            $propertyCssClass->setShowTypeTag('text');
            $propertyCssClass->setValueType('oneline_text');
            $propertyCssClass->setDefaultValue('');
            $propertyCssClass->setMultiOperations(false);
            $propertyCssClass->setInputString('');
            $propertyCssClass->setAttribute('tab', '');
            $propertyCssClass->setAttribute('size', '25');
            $propertyCssClass->setAttribute('maxlength', '');
            $propertyCssClass->setAttribute('readonly', 'F');
            $propertyCssClass->setAttribute('pattern', '');
            $propertyCssClass->setAttribute('inherit_value', 'F');
            $propertyCssClass->setAttribute('onchange-js', '');
            $propertyCssClass->setAttribute('onkeyup-js', '');
            $propertyCssClass->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyCssClass);
        } else {
            $this->writeLine('Property with tag css_class already exists');
            $this->setDataKey('property_css_class_existed', true);
        }

        // property: Titulok(title)
        $propertyTitle = $this->propertyManager()->propertyExistsByTag('title');
        if ($propertyTitle === false) {
            $propertyTitle = new Property();
            $propertyTitle->setTag('title');
            $propertyTitle->setDescription('Štandardný názov stránky. Text, ktorý sa zobrazí v záložke prehliadača. Podľa SEO odporúčaní max. 65 znakov.');
            $propertyTitle->setExtendedDescription('');
            $propertyTitle->setName('Titulok');
            $propertyTitle->setClassId(4);
            $propertyTitle->setShowType(null);
            $propertyTitle->setShowTypeTag('text');
            $propertyTitle->setValueType('oneline_text');
            $propertyTitle->setDefaultValue('');
            $propertyTitle->setMultiOperations(false);
            $propertyTitle->setInputString(null);
            $propertyTitle->setAttribute('tab', '');
            $propertyTitle->setAttribute('size', '60');
            $propertyTitle->setAttribute('maxlength', '');
            $propertyTitle->setAttribute('readonly', 'F');
            $propertyTitle->setAttribute('pattern', '');
            $propertyTitle->setAttribute('inherit_value', 'F');
            $propertyTitle->setAttribute('onchange-js', '');
            $propertyTitle->setAttribute('onkeyup-js', '');
            $propertyTitle->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyTitle);
        } else {
            $this->writeLine('Property with tag title already exists');
            $this->setDataKey('property_title_existed', true);
        }

        // property: Content before(content_before)
        $propertyContentBefore = $this->propertyManager()->propertyExistsByTag('content_before');
        if ($propertyContentBefore === false) {
            $propertyContentBefore = new Property();
            $propertyContentBefore->setTag('content_before');
            $propertyContentBefore->setDescription('Obsah na začiatok');
            $propertyContentBefore->setExtendedDescription('');
            $propertyContentBefore->setName('Content before');
            $propertyContentBefore->setClassId(4);
            $propertyContentBefore->setShowType(null);
            $propertyContentBefore->setShowTypeTag('textarea');
            $propertyContentBefore->setValueType('multiline_text');
            $propertyContentBefore->setDefaultValue('');
            $propertyContentBefore->setMultiOperations(false);
            $propertyContentBefore->setInputString('');
            $propertyContentBefore->setAttribute('tab', '');
            $propertyContentBefore->setAttribute('cols', '60');
            $propertyContentBefore->setAttribute('rows', '3');
            $propertyContentBefore->setAttribute('dhtml-edit', '1');
            $propertyContentBefore->setAttribute('dhtml-configuration', 'full_no_p');
            $propertyContentBefore->setAttribute('import-word', '0');
            $propertyContentBefore->setAttribute('auto', '');
            $propertyContentBefore->setAttribute('inherit_value', 'F');
            $propertyContentBefore->setAttribute('onchange-js', '');
            $propertyContentBefore->setAttribute('onkeyup-js', '');
            $propertyContentBefore->setAttribute('onkeydown-js', '');
            $propertyContentBefore->setAttribute('pattern', '');
            $this->propertyManager()->saveProperty($propertyContentBefore);
        } else {
            $this->writeLine('Property with tag content_before already exists');
            $this->setDataKey('property_content_before_existed', true);
        }

        // property: Zoznam odkazov(link_list)
        $propertyLinkList = $this->propertyManager()->propertyExistsByTag('link_list');
        if ($propertyLinkList === false) {
            $propertyLinkList = new Property();
            $propertyLinkList->setTag('link_list');
            $propertyLinkList->setDescription('Zoznam odkazov');
            $propertyLinkList->setExtendedDescription('');
            $propertyLinkList->setName('Zoznam odkazov');
            $propertyLinkList->setClassId(4);
            $propertyLinkList->setShowType(null);
            $propertyLinkList->setShowTypeTag('page_list');
            $propertyLinkList->setValueType('page_list');
            $propertyLinkList->setDefaultValue('');
            $propertyLinkList->setMultiOperations(false);
            $propertyLinkList->setInputString(null);
            $propertyLinkList->setAttribute('tab', '');
            $propertyLinkList->setAttribute('root_page_id', '');
            $propertyLinkList->setAttribute('page_type_id', '');
            $propertyLinkList->setAttribute('default_sort', 'tblPages.sort_date_time');
            $propertyLinkList->setAttribute('advanced_mode', 'T');
            $propertyLinkList->setAttribute('external_url', 'T');
            $propertyLinkList->setAttribute('max_items', '');
            $propertyLinkList->setAttribute('middle_col_width', '');
            $propertyLinkList->setAttribute('inherit_value', 'F');
            $propertyLinkList->setAttribute('apply_user_rights', 'F');
            $propertyLinkList->setAttribute('property_for_link_name', 'title');
            $propertyLinkList->setAttribute('properties_for_search', 'tblPages.page_name,title');
            $propertyLinkList->setAttribute('parent_page_type_id', '');
            $propertyLinkList->setAttribute('options', []);
            $this->propertyManager()->saveProperty($propertyLinkList);
        } else {
            $this->writeLine('Property with tag link_list already exists');
            $this->setDataKey('property_link_list_existed', true);
        }

        // property: Content after(content_after)
        $propertyContentAfter = $this->propertyManager()->propertyExistsByTag('content_after');
        if ($propertyContentAfter === false) {
            $propertyContentAfter = new Property();
            $propertyContentAfter->setTag('content_after');
            $propertyContentAfter->setDescription('Obsah na koniec');
            $propertyContentAfter->setExtendedDescription('');
            $propertyContentAfter->setName('Content after');
            $propertyContentAfter->setClassId(4);
            $propertyContentAfter->setShowType(null);
            $propertyContentAfter->setShowTypeTag('textarea');
            $propertyContentAfter->setValueType('multiline_text');
            $propertyContentAfter->setDefaultValue('');
            $propertyContentAfter->setMultiOperations(false);
            $propertyContentAfter->setInputString('');
            $propertyContentAfter->setAttribute('tab', '');
            $propertyContentAfter->setAttribute('cols', '60');
            $propertyContentAfter->setAttribute('rows', '3');
            $propertyContentAfter->setAttribute('dhtml-edit', '1');
            $propertyContentAfter->setAttribute('dhtml-configuration', 'full_no_p');
            $propertyContentAfter->setAttribute('import-word', '0');
            $propertyContentAfter->setAttribute('auto', '');
            $propertyContentAfter->setAttribute('inherit_value', 'F');
            $propertyContentAfter->setAttribute('onchange-js', '');
            $propertyContentAfter->setAttribute('onkeyup-js', '');
            $propertyContentAfter->setAttribute('onkeydown-js', '');
            $propertyContentAfter->setAttribute('pattern', '');
            $this->propertyManager()->saveProperty($propertyContentAfter);
        } else {
            $this->writeLine('Property with tag content_after already exists');
            $this->setDataKey('property_content_after_existed', true);
        }

        // page type: Stĺpec v pätičke - text s odkazmi (footer_column_links_text)
        $pageTypeFooterColumnLinksText = $this->pageTypesManager()->pageTypeExistsByTag('footer_column_links_text');
        if ($pageTypeFooterColumnLinksText === false) {
            $pageTypeFooterColumnLinksText = new PageType();
            $pageTypeFooterColumnLinksText->setTag('footer_column_links_text');
            $pageTypeFooterColumnLinksText->setName('Stĺpec v pätičke - text s odkazmi');
            $pageTypeFooterColumnLinksText->setPageClassId(1);
            $pageTypeFooterColumnLinksText->setDefaultTemplateId(1);
            $pageTypeFooterColumnLinksText->setDeleteTrigger('');
            $pageTypeFooterColumnLinksText->setIncludeInSync(null);
            $pageTypeFooterColumnLinksText->setPageDetailsLayout('');
            $pageTypeFooterColumnLinksText->setPageSortTypeTag('sort_date_time');
            $pageTypeFooterColumnLinksText->setPageTypeOrder(0);
            $pageTypeFooterColumnLinksText->setPostmoveTrigger('');
            $pageTypeFooterColumnLinksText->setPostsubmitTrigger('');
            $pageTypeFooterColumnLinksText->setPresubmitTrigger('');
            $parent = $this->pageTypesManager()->getPageTypeByTag('layout');
            $pageTypeFooterColumnLinksText->setParent($parent);
        } else {
            $this->writeLine('Page type with tag footer_column_links_text already exists');
            $this->setDataKey('page_type_footer_column_links_text_existed', true);
        }
        $property = $this->propertyManager()->getPropertyByTag('html5_semantic_element');
        $propertyId = $property->getId();
        $tmp = $pageTypeFooterColumnLinksText->getPropertyItemForPropertyId($propertyId);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder(1);
            $tmp->setRequired(false);
            $pageTypeFooterColumnLinksText->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('css_class');
        $propertyId = $property->getId();
        $tmp = $pageTypeFooterColumnLinksText->getPropertyItemForPropertyId($propertyId);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder(2);
            $tmp->setRequired(false);
            $pageTypeFooterColumnLinksText->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('title');
        $propertyId = $property->getId();
        $tmp = $pageTypeFooterColumnLinksText->getPropertyItemForPropertyId($propertyId);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder(3);
            $tmp->setRequired(false);
            $pageTypeFooterColumnLinksText->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('content_before');
        $propertyId = $property->getId();
        $tmp = $pageTypeFooterColumnLinksText->getPropertyItemForPropertyId($propertyId);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder(4);
            $tmp->setRequired(false);
            $pageTypeFooterColumnLinksText->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('link_list');
        $propertyId = $property->getId();
        $tmp = $pageTypeFooterColumnLinksText->getPropertyItemForPropertyId($propertyId);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder(5);
            $tmp->setRequired(false);
            $pageTypeFooterColumnLinksText->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('content_after');
        $propertyId = $property->getId();
        $tmp = $pageTypeFooterColumnLinksText->getPropertyItemForPropertyId($propertyId);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder(6);
            $tmp->setRequired(false);
            $pageTypeFooterColumnLinksText->addPropertyItem($tmp);
        }
        $this->pageTypesManager()->savePageType($pageTypeFooterColumnLinksText);

        // property: Footer - text vľavo(footer_text_left)
        $propertyFooterTextLeft = $this->propertyManager()->propertyExistsByTag('footer_text_left');
        if ($propertyFooterTextLeft === false) {
            $propertyFooterTextLeft = new Property();
            $propertyFooterTextLeft->setTag('footer_text_left');
            $propertyFooterTextLeft->setDescription('');
            $propertyFooterTextLeft->setExtendedDescription('');
            $propertyFooterTextLeft->setName('Footer - text vľavo');
            $propertyFooterTextLeft->setClassId(4);
            $propertyFooterTextLeft->setShowType(null);
            $propertyFooterTextLeft->setShowTypeTag('textarea');
            $propertyFooterTextLeft->setValueType('multiline_text');
            $propertyFooterTextLeft->setDefaultValue('');
            $propertyFooterTextLeft->setMultiOperations(false);
            $propertyFooterTextLeft->setInputString('');
            $propertyFooterTextLeft->setAttribute('tab', '');
            $propertyFooterTextLeft->setAttribute('cols', '60');
            $propertyFooterTextLeft->setAttribute('rows', '3');
            $propertyFooterTextLeft->setAttribute('dhtml-edit', '1');
            $propertyFooterTextLeft->setAttribute('dhtml-configuration', 'full');
            $propertyFooterTextLeft->setAttribute('import-word', '0');
            $propertyFooterTextLeft->setAttribute('auto', '');
            $propertyFooterTextLeft->setAttribute('inherit_value', 'F');
            $propertyFooterTextLeft->setAttribute('onchange-js', '');
            $propertyFooterTextLeft->setAttribute('onkeyup-js', '');
            $propertyFooterTextLeft->setAttribute('onkeydown-js', '');
            $propertyFooterTextLeft->setAttribute('pattern', '');
            $this->propertyManager()->saveProperty($propertyFooterTextLeft);
        } else {
            $this->writeLine('Property with tag footer_text_left already exists');
            $this->setDataKey('property_footer_text_left_existed', true);
        }

        // property: Footer - text vpravo(footer_text_right)
        $propertyFooterTextRight = $this->propertyManager()->propertyExistsByTag('footer_text_right');
        if ($propertyFooterTextRight === false) {
            $propertyFooterTextRight = new Property();
            $propertyFooterTextRight->setTag('footer_text_right');
            $propertyFooterTextRight->setDescription('');
            $propertyFooterTextRight->setExtendedDescription('');
            $propertyFooterTextRight->setName('Footer - text vpravo');
            $propertyFooterTextRight->setClassId(4);
            $propertyFooterTextRight->setShowType(null);
            $propertyFooterTextRight->setShowTypeTag('textarea');
            $propertyFooterTextRight->setValueType('multiline_text');
            $propertyFooterTextRight->setDefaultValue('');
            $propertyFooterTextRight->setMultiOperations(false);
            $propertyFooterTextRight->setInputString('');
            $propertyFooterTextRight->setAttribute('tab', '');
            $propertyFooterTextRight->setAttribute('cols', '60');
            $propertyFooterTextRight->setAttribute('rows', '3');
            $propertyFooterTextRight->setAttribute('dhtml-edit', '1');
            $propertyFooterTextRight->setAttribute('dhtml-configuration', 'full');
            $propertyFooterTextRight->setAttribute('import-word', '0');
            $propertyFooterTextRight->setAttribute('auto', '');
            $propertyFooterTextRight->setAttribute('inherit_value', 'F');
            $propertyFooterTextRight->setAttribute('onchange-js', '');
            $propertyFooterTextRight->setAttribute('onkeyup-js', '');
            $propertyFooterTextRight->setAttribute('onkeydown-js', '');
            $propertyFooterTextRight->setAttribute('pattern', '');
            $this->propertyManager()->saveProperty($propertyFooterTextRight);
        } else {
            $this->writeLine('Property with tag footer_text_right already exists');
            $this->setDataKey('property_footer_text_right_existed', true);
        }

        // page type: Fat footer (fat_footer)
        $pageTypeFatFooter = $this->pageTypesManager()->pageTypeExistsByTag('fat_footer');
        if ($pageTypeFatFooter === false) {
            $pageTypeFatFooter = new PageType();
            $pageTypeFatFooter->setTag('fat_footer');
            $pageTypeFatFooter->setName('Fat footer');
            $pageTypeFatFooter->setPageClassId(1);
            $pageTypeFatFooter->setDefaultTemplateId(1);
            $pageTypeFatFooter->setDeleteTrigger('');
            $pageTypeFatFooter->setIncludeInSync(null);
            $pageTypeFatFooter->setPageDetailsLayout('');
            $pageTypeFatFooter->setPageSortTypeTag('sort_date_time');
            $pageTypeFatFooter->setPageTypeOrder(0);
            $pageTypeFatFooter->setPostmoveTrigger('');
            $pageTypeFatFooter->setPostsubmitTrigger('');
            $pageTypeFatFooter->setPresubmitTrigger('');
            $parent = $this->pageTypesManager()->getPageTypeByTag('layout');
            $pageTypeFatFooter->setParent($parent);
        } else {
            $this->writeLine('Page type with tag fat_footer already exists');
            $this->setDataKey('page_type_fat_footer_existed', true);
        }
        $property = $this->propertyManager()->getPropertyByTag('html5_semantic_element');
        $propertyId = $property->getId();
        $tmp = $pageTypeFatFooter->getPropertyItemForPropertyId($propertyId);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder(1);
            $tmp->setRequired(false);
            $pageTypeFatFooter->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('footer_text_left');
        $propertyId = $property->getId();
        $tmp = $pageTypeFatFooter->getPropertyItemForPropertyId($propertyId);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder(2);
            $tmp->setRequired(false);
            $pageTypeFatFooter->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('footer_text_right');
        $propertyId = $property->getId();
        $tmp = $pageTypeFatFooter->getPropertyItemForPropertyId($propertyId);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder(3);
            $tmp->setRequired(false);
            $pageTypeFatFooter->addPropertyItem($tmp);
        }
        $this->pageTypesManager()->savePageType($pageTypeFatFooter);

        if ($this->pageTypeExists('homepage')) {
            $this->addPageTypeSuperiorPageType('layout', 'homepage');
        }
        if ($this->pageTypeExists('fat_footer')) {
            $this->addPageTypeSuperiorPageType('footer_column_links_text', 'fat_footer');
        }
        if ($this->pageTypeExists('main_page')) {
            $this->addPageTypeSuperiorPageType('fat_footer', 'main_page');
        }

        // regenerate property constants
        $this->propertyManager()->generateConstants();

        // regenerate page types constants
        $this->pageTypesManager()->generateConstants();
    }

    public function down()
    {
        // remove page type: Fat footer (fat_footer)
        $pageTypeFatFooter = $this->pageTypesManager()->pageTypeExistsByTag('fat_footer');
        if (($pageTypeFatFooter != false) && (is_null($this->getDataKey('page_type_fat_footer_existed')))) {
            $this->pageTypesManager()->removePageType($pageTypeFatFooter);
        }

        // remove property: Footer - text vpravo(footer_text_right)
        $propertyFooterTextRight = $this->propertyManager()->propertyExistsByTag('footer_text_right');
        if ($propertyFooterTextRight !== false) {
            $supportedPageTypes = $this->propertyManager()->getSupportedPageTypes($propertyFooterTextRight);
            if (($this->getDataKey('property_footer_text_right_existed') === null) && (count($supportedPageTypes) === 0)) {
                $this->propertyManager()->removeProperty($propertyFooterTextRight);
            }
        }

        // remove property: Footer - text vľavo(footer_text_left)
        $propertyFooterTextLeft = $this->propertyManager()->propertyExistsByTag('footer_text_left');
        if ($propertyFooterTextLeft !== false) {
            $supportedPageTypes = $this->propertyManager()->getSupportedPageTypes($propertyFooterTextLeft);
            if (($this->getDataKey('property_footer_text_left_existed') === null) && (count($supportedPageTypes) === 0)) {
                $this->propertyManager()->removeProperty($propertyFooterTextLeft);
            }
        }

        // remove page type: Stĺpec v pätičke - text s odkazmi (footer_column_links_text)
        $pageTypeFooterColumnLinksText = $this->pageTypesManager()->pageTypeExistsByTag('footer_column_links_text');
        if (($pageTypeFooterColumnLinksText != false) && (is_null($this->getDataKey('page_type_footer_column_links_text_existed')))) {
            $this->pageTypesManager()->removePageType($pageTypeFooterColumnLinksText);
        }

        // remove property: Content after(content_after)
        $propertyContentAfter = $this->propertyManager()->propertyExistsByTag('content_after');
        if ($propertyContentAfter !== false) {
            $supportedPageTypes = $this->propertyManager()->getSupportedPageTypes($propertyContentAfter);
            if (($this->getDataKey('property_content_after_existed') === null) && (count($supportedPageTypes) === 0)) {
                $this->propertyManager()->removeProperty($propertyContentAfter);
            }
        }

        // remove property: Zoznam odkazov(link_list)
        $propertyLinkList = $this->propertyManager()->propertyExistsByTag('link_list');
        if ($propertyLinkList !== false) {
            $supportedPageTypes = $this->propertyManager()->getSupportedPageTypes($propertyLinkList);
            if (($this->getDataKey('property_link_list_existed') === null) && (count($supportedPageTypes) === 0)) {
                $this->propertyManager()->removeProperty($propertyLinkList);
            }
        }

        // remove property: Content before(content_before)
        $propertyContentBefore = $this->propertyManager()->propertyExistsByTag('content_before');
        if ($propertyContentBefore !== false) {
            $supportedPageTypes = $this->propertyManager()->getSupportedPageTypes($propertyContentBefore);
            if (($this->getDataKey('property_content_before_existed') === null) && (count($supportedPageTypes) === 0)) {
                $this->propertyManager()->removeProperty($propertyContentBefore);
            }
        }

        // remove property: Titulok(title)
        $propertyTitle = $this->propertyManager()->propertyExistsByTag('title');
        if ($propertyTitle !== false) {
            $supportedPageTypes = $this->propertyManager()->getSupportedPageTypes($propertyTitle);
            if (($this->getDataKey('property_title_existed') === null) && (count($supportedPageTypes) === 0)) {
                $this->propertyManager()->removeProperty($propertyTitle);
            }
        }

        // remove property: CSS trieda(css_class)
        $propertyCssClass = $this->propertyManager()->propertyExistsByTag('css_class');
        if ($propertyCssClass !== false) {
            $supportedPageTypes = $this->propertyManager()->getSupportedPageTypes($propertyCssClass);
            if (($this->getDataKey('property_css_class_existed') === null) && (count($supportedPageTypes) === 0)) {
                $this->propertyManager()->removeProperty($propertyCssClass);
            }
        }

        // remove page type: Layout (layout)
        $pageTypeLayout = $this->pageTypesManager()->pageTypeExistsByTag('layout');
        if (($pageTypeLayout != false) && (is_null($this->getDataKey('page_type_layout_existed')))) {
            $this->pageTypesManager()->removePageType($pageTypeLayout);
        }

        // remove property: HTML5 Semantic Element(html5_semantic_element)
        $propertyHtml5SemanticElement = $this->propertyManager()->propertyExistsByTag('html5_semantic_element');
        if ($propertyHtml5SemanticElement !== false) {
            $supportedPageTypes = $this->propertyManager()->getSupportedPageTypes($propertyHtml5SemanticElement);
            if (($this->getDataKey('property_html5_semantic_element_existed') === null) && (count($supportedPageTypes) === 0)) {
                $this->propertyManager()->removeProperty($propertyHtml5SemanticElement);
            }
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();

        // regenerate page types constants
        $this->pageTypesManager()->generateConstants();
    }
}
