<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;

/**
 * Automatic generation from (rinoparts) at 2021-11-02 13:41:56
 * Property generator: property=eshop_eur_price_including_vat_cz
 */
class PriceCZKIncludingVat extends AbstractMigration
{
    public function up()
    {
        // property: Cena CZK s DPH(eshop_eur_price_including_vat_cz)
        $propertyEshopEurPriceIncludingVatCz = $this->propertyManager()->propertyExistsByTag('eshop_eur_price_including_vat_cz');
        if ($propertyEshopEurPriceIncludingVatCz === false) {
            $propertyEshopEurPriceIncludingVatCz = new Property();
            $propertyEshopEurPriceIncludingVatCz->setTag('eshop_eur_price_including_vat_cz');
            $propertyEshopEurPriceIncludingVatCz->setDescription('Cena CZK s DPH');
            $propertyEshopEurPriceIncludingVatCz->setExtendedDescription('');
            $propertyEshopEurPriceIncludingVatCz->setName('Cena CZK s DPH');
            $propertyEshopEurPriceIncludingVatCz->setClassId(4);
            $propertyEshopEurPriceIncludingVatCz->setShowType(null);
            $propertyEshopEurPriceIncludingVatCz->setShowTypeTag('text');
            $propertyEshopEurPriceIncludingVatCz->setValueType('oneline_text');
            $propertyEshopEurPriceIncludingVatCz->setDefaultValue('');
            $propertyEshopEurPriceIncludingVatCz->setMultiOperations(false);
            $propertyEshopEurPriceIncludingVatCz->setInputString('');
            $propertyEshopEurPriceIncludingVatCz->setAttribute('tab', '');
            $propertyEshopEurPriceIncludingVatCz->setAttribute('size', '60');
            $propertyEshopEurPriceIncludingVatCz->setAttribute('maxlength', '');
            $propertyEshopEurPriceIncludingVatCz->setAttribute('readonly', 'F');
            $propertyEshopEurPriceIncludingVatCz->setAttribute('pattern', '');
            $propertyEshopEurPriceIncludingVatCz->setAttribute('inherit_value', 'F');
            $propertyEshopEurPriceIncludingVatCz->setAttribute('onchange-js', '');
            $propertyEshopEurPriceIncludingVatCz->setAttribute('onkeyup-js', '');
            $propertyEshopEurPriceIncludingVatCz->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyEshopEurPriceIncludingVatCz);
        } else {
            $this->writeLine('Property with tag eshop_eur_price_including_vat_cz already exists');
            $this->setDataKey('property_eshop_eur_price_including_vat_cz_existed', true);
        }
        if ($this->pageTypeExists('eshop_transport_type')) {
            $this->addPropertyToPageType('eshop_eur_price_including_vat_cz', 'eshop_transport_type', false);
        }
        if ($this->pageTypeExists('eshop_payment_type')) {
            $this->addPropertyToPageType('eshop_eur_price_including_vat_cz', 'eshop_payment_type', false);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }

    public function down()
    {
        // remove property: Cena CZK s DPH(eshop_eur_price_including_vat_cz)
        $propertyEshopEurPriceIncludingVatCz = $this->propertyManager()->propertyExistsByTag('eshop_eur_price_including_vat_cz');
        if (($propertyEshopEurPriceIncludingVatCz !== false) && ($this->getDataKey('property_eshop_eur_price_including_vat_cz_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyEshopEurPriceIncludingVatCz);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }
}
