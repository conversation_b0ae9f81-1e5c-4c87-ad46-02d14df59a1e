<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;

/**
 * Automatic generation from (rinoparts) at 2024-01-26 13:38:06
 * Property generator: property=eminia_supplier_code,eminia_price_without_vat,eminia_stock_balance,eminia_oe_number,eminia_oe_numbers,eminia_latest_import
 */
class EminiaPropertiesMigration extends AbstractMigration
{
    public function up()
    {
        // property: Eminia - Dodávateľský kód(eminia_supplier_code)
        $propertyEminiaSupplierCode = $this->propertyManager()->propertyExistsByTag('eminia_supplier_code');
        if ($propertyEminiaSupplierCode === false) {
            $propertyEminiaSupplierCode = new Property();
            $propertyEminiaSupplierCode->setTag('eminia_supplier_code');
            $propertyEminiaSupplierCode->setDescription('');
            $propertyEminiaSupplierCode->setExtendedDescription('');
            $propertyEminiaSupplierCode->setName('Eminia - Dodávateľský kód');
            $propertyEminiaSupplierCode->setClassId(4);
            $propertyEminiaSupplierCode->setShowType(null);
            $propertyEminiaSupplierCode->setShowTypeTag('text');
            $propertyEminiaSupplierCode->setValueType('oneline_text');
            $propertyEminiaSupplierCode->setDefaultValue('');
            $propertyEminiaSupplierCode->setMultiOperations(false);
            $propertyEminiaSupplierCode->setInputString('');
            $propertyEminiaSupplierCode->setAttribute('tab', 'Eminia');
            $propertyEminiaSupplierCode->setAttribute('size', '60');
            $propertyEminiaSupplierCode->setAttribute('maxlength', '');
            $propertyEminiaSupplierCode->setAttribute('readonly', 'F');
            $propertyEminiaSupplierCode->setAttribute('pattern', '');
            $propertyEminiaSupplierCode->setAttribute('inherit_value', 'F');
            $propertyEminiaSupplierCode->setAttribute('onchange-js', '');
            $propertyEminiaSupplierCode->setAttribute('onkeyup-js', '');
            $propertyEminiaSupplierCode->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyEminiaSupplierCode);
        } else {
            $this->writeLine('Property with tag eminia_supplier_code already exists');
            $this->setDataKey('property_eminia_supplier_code_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('eminia_supplier_code', 'eshop_product', false);
        }

        // property: Eminia - Cena bez DPH(eminia_price_without_vat)
        $propertyEminiaPriceWithoutVat = $this->propertyManager()->propertyExistsByTag('eminia_price_without_vat');
        if ($propertyEminiaPriceWithoutVat === false) {
            $propertyEminiaPriceWithoutVat = new Property();
            $propertyEminiaPriceWithoutVat->setTag('eminia_price_without_vat');
            $propertyEminiaPriceWithoutVat->setDescription('');
            $propertyEminiaPriceWithoutVat->setExtendedDescription('');
            $propertyEminiaPriceWithoutVat->setName('Eminia - Cena bez DPH');
            $propertyEminiaPriceWithoutVat->setClassId(4);
            $propertyEminiaPriceWithoutVat->setShowType(null);
            $propertyEminiaPriceWithoutVat->setShowTypeTag('text');
            $propertyEminiaPriceWithoutVat->setValueType('oneline_text');
            $propertyEminiaPriceWithoutVat->setDefaultValue('');
            $propertyEminiaPriceWithoutVat->setMultiOperations(false);
            $propertyEminiaPriceWithoutVat->setInputString('');
            $propertyEminiaPriceWithoutVat->setAttribute('tab', 'Eminia');
            $propertyEminiaPriceWithoutVat->setAttribute('size', '60');
            $propertyEminiaPriceWithoutVat->setAttribute('maxlength', '');
            $propertyEminiaPriceWithoutVat->setAttribute('readonly', 'F');
            $propertyEminiaPriceWithoutVat->setAttribute('pattern', '');
            $propertyEminiaPriceWithoutVat->setAttribute('inherit_value', 'F');
            $propertyEminiaPriceWithoutVat->setAttribute('onchange-js', '');
            $propertyEminiaPriceWithoutVat->setAttribute('onkeyup-js', '');
            $propertyEminiaPriceWithoutVat->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyEminiaPriceWithoutVat);
        } else {
            $this->writeLine('Property with tag eminia_price_without_vat already exists');
            $this->setDataKey('property_eminia_price_without_vat_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('eminia_price_without_vat', 'eshop_product', false);
        }

        // property: Eminia - Skladová zásoba(eminia_stock_balance)
        $propertyEminiaStockBalance = $this->propertyManager()->propertyExistsByTag('eminia_stock_balance');
        if ($propertyEminiaStockBalance === false) {
            $propertyEminiaStockBalance = new Property();
            $propertyEminiaStockBalance->setTag('eminia_stock_balance');
            $propertyEminiaStockBalance->setDescription('');
            $propertyEminiaStockBalance->setExtendedDescription('');
            $propertyEminiaStockBalance->setName('Eminia - Skladová zásoba');
            $propertyEminiaStockBalance->setClassId(4);
            $propertyEminiaStockBalance->setShowType(null);
            $propertyEminiaStockBalance->setShowTypeTag('text');
            $propertyEminiaStockBalance->setValueType('oneline_text');
            $propertyEminiaStockBalance->setDefaultValue('');
            $propertyEminiaStockBalance->setMultiOperations(false);
            $propertyEminiaStockBalance->setInputString('');
            $propertyEminiaStockBalance->setAttribute('tab', 'Eminia');
            $propertyEminiaStockBalance->setAttribute('size', '60');
            $propertyEminiaStockBalance->setAttribute('maxlength', '');
            $propertyEminiaStockBalance->setAttribute('readonly', 'F');
            $propertyEminiaStockBalance->setAttribute('pattern', '');
            $propertyEminiaStockBalance->setAttribute('inherit_value', 'F');
            $propertyEminiaStockBalance->setAttribute('onchange-js', '');
            $propertyEminiaStockBalance->setAttribute('onkeyup-js', '');
            $propertyEminiaStockBalance->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyEminiaStockBalance);
        } else {
            $this->writeLine('Property with tag eminia_stock_balance already exists');
            $this->setDataKey('property_eminia_stock_balance_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('eminia_stock_balance', 'eshop_product', false);
        }

        // property: Eminia - OE number(eminia_oe_number)
        $propertyEminiaOeNumber = $this->propertyManager()->propertyExistsByTag('eminia_oe_number');
        if ($propertyEminiaOeNumber === false) {
            $propertyEminiaOeNumber = new Property();
            $propertyEminiaOeNumber->setTag('eminia_oe_number');
            $propertyEminiaOeNumber->setDescription('');
            $propertyEminiaOeNumber->setExtendedDescription('');
            $propertyEminiaOeNumber->setName('Eminia - OE number');
            $propertyEminiaOeNumber->setClassId(4);
            $propertyEminiaOeNumber->setShowType(null);
            $propertyEminiaOeNumber->setShowTypeTag('text');
            $propertyEminiaOeNumber->setValueType('oneline_text');
            $propertyEminiaOeNumber->setDefaultValue('');
            $propertyEminiaOeNumber->setMultiOperations(false);
            $propertyEminiaOeNumber->setInputString('');
            $propertyEminiaOeNumber->setAttribute('tab', 'Eminia');
            $propertyEminiaOeNumber->setAttribute('size', '60');
            $propertyEminiaOeNumber->setAttribute('maxlength', '');
            $propertyEminiaOeNumber->setAttribute('readonly', 'F');
            $propertyEminiaOeNumber->setAttribute('pattern', '');
            $propertyEminiaOeNumber->setAttribute('inherit_value', 'F');
            $propertyEminiaOeNumber->setAttribute('onchange-js', '');
            $propertyEminiaOeNumber->setAttribute('onkeyup-js', '');
            $propertyEminiaOeNumber->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyEminiaOeNumber);
        } else {
            $this->writeLine('Property with tag eminia_oe_number already exists');
            $this->setDataKey('property_eminia_oe_number_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('eminia_oe_number', 'eshop_product', false);
        }

        // property: Eminia - OE numbers(eminia_oe_numbers)
        $propertyEminiaOeNumbers = $this->propertyManager()->propertyExistsByTag('eminia_oe_numbers');
        if ($propertyEminiaOeNumbers === false) {
            $propertyEminiaOeNumbers = new Property();
            $propertyEminiaOeNumbers->setTag('eminia_oe_numbers');
            $propertyEminiaOeNumbers->setDescription('');
            $propertyEminiaOeNumbers->setExtendedDescription('');
            $propertyEminiaOeNumbers->setName('Eminia - OE numbers');
            $propertyEminiaOeNumbers->setClassId(4);
            $propertyEminiaOeNumbers->setShowType(null);
            $propertyEminiaOeNumbers->setShowTypeTag('text');
            $propertyEminiaOeNumbers->setValueType('oneline_text');
            $propertyEminiaOeNumbers->setDefaultValue('');
            $propertyEminiaOeNumbers->setMultiOperations(false);
            $propertyEminiaOeNumbers->setInputString('');
            $propertyEminiaOeNumbers->setAttribute('tab', 'Eminia');
            $propertyEminiaOeNumbers->setAttribute('size', '60');
            $propertyEminiaOeNumbers->setAttribute('maxlength', '');
            $propertyEminiaOeNumbers->setAttribute('readonly', 'F');
            $propertyEminiaOeNumbers->setAttribute('pattern', '');
            $propertyEminiaOeNumbers->setAttribute('inherit_value', 'F');
            $propertyEminiaOeNumbers->setAttribute('onchange-js', '');
            $propertyEminiaOeNumbers->setAttribute('onkeyup-js', '');
            $propertyEminiaOeNumbers->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyEminiaOeNumbers);
        } else {
            $this->writeLine('Property with tag eminia_oe_numbers already exists');
            $this->setDataKey('property_eminia_oe_numbers_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('eminia_oe_numbers', 'eshop_product', false);
        }

        // property: Eminia - Posledný import(eminia_latest_import)
        $propertyEminiaLatestImport = $this->propertyManager()->propertyExistsByTag('eminia_latest_import');
        if ($propertyEminiaLatestImport === false) {
            $propertyEminiaLatestImport = new Property();
            $propertyEminiaLatestImport->setTag('eminia_latest_import');
            $propertyEminiaLatestImport->setDescription('');
            $propertyEminiaLatestImport->setExtendedDescription('');
            $propertyEminiaLatestImport->setName('Eminia - Posledný import');
            $propertyEminiaLatestImport->setClassId(4);
            $propertyEminiaLatestImport->setShowType(null);
            $propertyEminiaLatestImport->setShowTypeTag('text');
            $propertyEminiaLatestImport->setValueType('oneline_text');
            $propertyEminiaLatestImport->setDefaultValue('');
            $propertyEminiaLatestImport->setMultiOperations(false);
            $propertyEminiaLatestImport->setInputString('');
            $propertyEminiaLatestImport->setAttribute('tab', 'Eminia');
            $propertyEminiaLatestImport->setAttribute('size', '60');
            $propertyEminiaLatestImport->setAttribute('maxlength', '');
            $propertyEminiaLatestImport->setAttribute('readonly', 'F');
            $propertyEminiaLatestImport->setAttribute('pattern', '');
            $propertyEminiaLatestImport->setAttribute('inherit_value', 'F');
            $propertyEminiaLatestImport->setAttribute('onchange-js', '');
            $propertyEminiaLatestImport->setAttribute('onkeyup-js', '');
            $propertyEminiaLatestImport->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyEminiaLatestImport);
        } else {
            $this->writeLine('Property with tag eminia_latest_import already exists');
            $this->setDataKey('property_eminia_latest_import_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('eminia_latest_import', 'eshop_product', false);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }

    public function down()
    {
        // remove property: Eminia - Posledný import(eminia_latest_import)
        $propertyEminiaLatestImport = $this->propertyManager()->propertyExistsByTag('eminia_latest_import');
        if (($propertyEminiaLatestImport !== false) && ($this->getDataKey('property_eminia_latest_import_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyEminiaLatestImport);
        }

        // remove property: Eminia - OE numbers(eminia_oe_numbers)
        $propertyEminiaOeNumbers = $this->propertyManager()->propertyExistsByTag('eminia_oe_numbers');
        if (($propertyEminiaOeNumbers !== false) && ($this->getDataKey('property_eminia_oe_numbers_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyEminiaOeNumbers);
        }

        // remove property: Eminia - OE number(eminia_oe_number)
        $propertyEminiaOeNumber = $this->propertyManager()->propertyExistsByTag('eminia_oe_number');
        if (($propertyEminiaOeNumber !== false) && ($this->getDataKey('property_eminia_oe_number_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyEminiaOeNumber);
        }

        // remove property: Eminia - Skladová zásoba(eminia_stock_balance)
        $propertyEminiaStockBalance = $this->propertyManager()->propertyExistsByTag('eminia_stock_balance');
        if (($propertyEminiaStockBalance !== false) && ($this->getDataKey('property_eminia_stock_balance_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyEminiaStockBalance);
        }

        // remove property: Eminia - Cena bez DPH(eminia_price_without_vat)
        $propertyEminiaPriceWithoutVat = $this->propertyManager()->propertyExistsByTag('eminia_price_without_vat');
        if (($propertyEminiaPriceWithoutVat !== false) && ($this->getDataKey('property_eminia_price_without_vat_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyEminiaPriceWithoutVat);
        }

        // remove property: Eminia - Dodávateľský kód(eminia_supplier_code)
        $propertyEminiaSupplierCode = $this->propertyManager()->propertyExistsByTag('eminia_supplier_code');
        if (($propertyEminiaSupplierCode !== false) && ($this->getDataKey('property_eminia_supplier_code_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyEminiaSupplierCode);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }
}
