<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;

/**
 * Automatic generation from (rinoparts) at 2024-01-26 13:38:06
 * Property generator: property=casco_supplier_code,casco_price_without_vat,casco_stock_balance,casco_oe_number,casco_oe_numbers,casco_latest_import
 */
class CascoPropertiesMigration extends AbstractMigration
{
    public function up()
    {
        // property: Casco - Dodávateľský kód(casco_supplier_code)
        $propertyCascoSupplierCode = $this->propertyManager()->propertyExistsByTag('casco_supplier_code');
        if ($propertyCascoSupplierCode === false) {
            $propertyCascoSupplierCode = new Property();
            $propertyCascoSupplierCode->setTag('casco_supplier_code');
            $propertyCascoSupplierCode->setDescription('');
            $propertyCascoSupplierCode->setExtendedDescription('');
            $propertyCascoSupplierCode->setName('Casco - Dodávateľský kód');
            $propertyCascoSupplierCode->setClassId(4);
            $propertyCascoSupplierCode->setShowType(null);
            $propertyCascoSupplierCode->setShowTypeTag('text');
            $propertyCascoSupplierCode->setValueType('oneline_text');
            $propertyCascoSupplierCode->setDefaultValue('');
            $propertyCascoSupplierCode->setMultiOperations(false);
            $propertyCascoSupplierCode->setInputString('');
            $propertyCascoSupplierCode->setAttribute('tab', 'Casco');
            $propertyCascoSupplierCode->setAttribute('size', '60');
            $propertyCascoSupplierCode->setAttribute('maxlength', '');
            $propertyCascoSupplierCode->setAttribute('readonly', 'F');
            $propertyCascoSupplierCode->setAttribute('pattern', '');
            $propertyCascoSupplierCode->setAttribute('inherit_value', 'F');
            $propertyCascoSupplierCode->setAttribute('onchange-js', '');
            $propertyCascoSupplierCode->setAttribute('onkeyup-js', '');
            $propertyCascoSupplierCode->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyCascoSupplierCode);
        } else {
            $this->writeLine('Property with tag casco_supplier_code already exists');
            $this->setDataKey('property_casco_supplier_code_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('casco_supplier_code', 'eshop_product', false);
        }

        // property: Casco - Cena bez DPH(casco_price_without_vat)
        $propertyCascoPriceWithoutVat = $this->propertyManager()->propertyExistsByTag('casco_price_without_vat');
        if ($propertyCascoPriceWithoutVat === false) {
            $propertyCascoPriceWithoutVat = new Property();
            $propertyCascoPriceWithoutVat->setTag('casco_price_without_vat');
            $propertyCascoPriceWithoutVat->setDescription('');
            $propertyCascoPriceWithoutVat->setExtendedDescription('');
            $propertyCascoPriceWithoutVat->setName('Casco - Cena bez DPH');
            $propertyCascoPriceWithoutVat->setClassId(4);
            $propertyCascoPriceWithoutVat->setShowType(null);
            $propertyCascoPriceWithoutVat->setShowTypeTag('text');
            $propertyCascoPriceWithoutVat->setValueType('oneline_text');
            $propertyCascoPriceWithoutVat->setDefaultValue('');
            $propertyCascoPriceWithoutVat->setMultiOperations(false);
            $propertyCascoPriceWithoutVat->setInputString('');
            $propertyCascoPriceWithoutVat->setAttribute('tab', 'Casco');
            $propertyCascoPriceWithoutVat->setAttribute('size', '60');
            $propertyCascoPriceWithoutVat->setAttribute('maxlength', '');
            $propertyCascoPriceWithoutVat->setAttribute('readonly', 'F');
            $propertyCascoPriceWithoutVat->setAttribute('pattern', '');
            $propertyCascoPriceWithoutVat->setAttribute('inherit_value', 'F');
            $propertyCascoPriceWithoutVat->setAttribute('onchange-js', '');
            $propertyCascoPriceWithoutVat->setAttribute('onkeyup-js', '');
            $propertyCascoPriceWithoutVat->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyCascoPriceWithoutVat);
        } else {
            $this->writeLine('Property with tag casco_price_without_vat already exists');
            $this->setDataKey('property_casco_price_without_vat_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('casco_price_without_vat', 'eshop_product', false);
        }

        // property: Casco - Skladová zásoba(casco_stock_balance)
        $propertyCascoStockBalance = $this->propertyManager()->propertyExistsByTag('casco_stock_balance');
        if ($propertyCascoStockBalance === false) {
            $propertyCascoStockBalance = new Property();
            $propertyCascoStockBalance->setTag('casco_stock_balance');
            $propertyCascoStockBalance->setDescription('');
            $propertyCascoStockBalance->setExtendedDescription('');
            $propertyCascoStockBalance->setName('Casco - Skladová zásoba');
            $propertyCascoStockBalance->setClassId(4);
            $propertyCascoStockBalance->setShowType(null);
            $propertyCascoStockBalance->setShowTypeTag('text');
            $propertyCascoStockBalance->setValueType('oneline_text');
            $propertyCascoStockBalance->setDefaultValue('');
            $propertyCascoStockBalance->setMultiOperations(false);
            $propertyCascoStockBalance->setInputString('');
            $propertyCascoStockBalance->setAttribute('tab', 'Casco');
            $propertyCascoStockBalance->setAttribute('size', '60');
            $propertyCascoStockBalance->setAttribute('maxlength', '');
            $propertyCascoStockBalance->setAttribute('readonly', 'F');
            $propertyCascoStockBalance->setAttribute('pattern', '');
            $propertyCascoStockBalance->setAttribute('inherit_value', 'F');
            $propertyCascoStockBalance->setAttribute('onchange-js', '');
            $propertyCascoStockBalance->setAttribute('onkeyup-js', '');
            $propertyCascoStockBalance->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyCascoStockBalance);
        } else {
            $this->writeLine('Property with tag casco_stock_balance already exists');
            $this->setDataKey('property_casco_stock_balance_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('casco_stock_balance', 'eshop_product', false);
        }

        // property: Casco - OE number(casco_oe_number)
        $propertyCascoOeNumber = $this->propertyManager()->propertyExistsByTag('casco_oe_number');
        if ($propertyCascoOeNumber === false) {
            $propertyCascoOeNumber = new Property();
            $propertyCascoOeNumber->setTag('casco_oe_number');
            $propertyCascoOeNumber->setDescription('');
            $propertyCascoOeNumber->setExtendedDescription('');
            $propertyCascoOeNumber->setName('Casco - OE number');
            $propertyCascoOeNumber->setClassId(4);
            $propertyCascoOeNumber->setShowType(null);
            $propertyCascoOeNumber->setShowTypeTag('text');
            $propertyCascoOeNumber->setValueType('oneline_text');
            $propertyCascoOeNumber->setDefaultValue('');
            $propertyCascoOeNumber->setMultiOperations(false);
            $propertyCascoOeNumber->setInputString('');
            $propertyCascoOeNumber->setAttribute('tab', 'Casco');
            $propertyCascoOeNumber->setAttribute('size', '60');
            $propertyCascoOeNumber->setAttribute('maxlength', '');
            $propertyCascoOeNumber->setAttribute('readonly', 'F');
            $propertyCascoOeNumber->setAttribute('pattern', '');
            $propertyCascoOeNumber->setAttribute('inherit_value', 'F');
            $propertyCascoOeNumber->setAttribute('onchange-js', '');
            $propertyCascoOeNumber->setAttribute('onkeyup-js', '');
            $propertyCascoOeNumber->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyCascoOeNumber);
        } else {
            $this->writeLine('Property with tag casco_oe_number already exists');
            $this->setDataKey('property_casco_oe_number_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('casco_oe_number', 'eshop_product', false);
        }

        // property: Casco - OE numbers(casco_oe_numbers)
        $propertyCascoOeNumbers = $this->propertyManager()->propertyExistsByTag('casco_oe_numbers');
        if ($propertyCascoOeNumbers === false) {
            $propertyCascoOeNumbers = new Property();
            $propertyCascoOeNumbers->setTag('casco_oe_numbers');
            $propertyCascoOeNumbers->setDescription('');
            $propertyCascoOeNumbers->setExtendedDescription('');
            $propertyCascoOeNumbers->setName('Casco - OE numbers');
            $propertyCascoOeNumbers->setClassId(4);
            $propertyCascoOeNumbers->setShowType(null);
            $propertyCascoOeNumbers->setShowTypeTag('text');
            $propertyCascoOeNumbers->setValueType('oneline_text');
            $propertyCascoOeNumbers->setDefaultValue('');
            $propertyCascoOeNumbers->setMultiOperations(false);
            $propertyCascoOeNumbers->setInputString('');
            $propertyCascoOeNumbers->setAttribute('tab', 'Casco');
            $propertyCascoOeNumbers->setAttribute('size', '60');
            $propertyCascoOeNumbers->setAttribute('maxlength', '');
            $propertyCascoOeNumbers->setAttribute('readonly', 'F');
            $propertyCascoOeNumbers->setAttribute('pattern', '');
            $propertyCascoOeNumbers->setAttribute('inherit_value', 'F');
            $propertyCascoOeNumbers->setAttribute('onchange-js', '');
            $propertyCascoOeNumbers->setAttribute('onkeyup-js', '');
            $propertyCascoOeNumbers->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyCascoOeNumbers);
        } else {
            $this->writeLine('Property with tag casco_oe_numbers already exists');
            $this->setDataKey('property_casco_oe_numbers_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('casco_oe_numbers', 'eshop_product', false);
        }

        // property: Casco - Posledný import(casco_latest_import)
        $propertyCascoLatestImport = $this->propertyManager()->propertyExistsByTag('casco_latest_import');
        if ($propertyCascoLatestImport === false) {
            $propertyCascoLatestImport = new Property();
            $propertyCascoLatestImport->setTag('casco_latest_import');
            $propertyCascoLatestImport->setDescription('');
            $propertyCascoLatestImport->setExtendedDescription('');
            $propertyCascoLatestImport->setName('Casco - Posledný import');
            $propertyCascoLatestImport->setClassId(4);
            $propertyCascoLatestImport->setShowType(null);
            $propertyCascoLatestImport->setShowTypeTag('text');
            $propertyCascoLatestImport->setValueType('oneline_text');
            $propertyCascoLatestImport->setDefaultValue('');
            $propertyCascoLatestImport->setMultiOperations(false);
            $propertyCascoLatestImport->setInputString('');
            $propertyCascoLatestImport->setAttribute('tab', 'Casco');
            $propertyCascoLatestImport->setAttribute('size', '60');
            $propertyCascoLatestImport->setAttribute('maxlength', '');
            $propertyCascoLatestImport->setAttribute('readonly', 'F');
            $propertyCascoLatestImport->setAttribute('pattern', '');
            $propertyCascoLatestImport->setAttribute('inherit_value', 'F');
            $propertyCascoLatestImport->setAttribute('onchange-js', '');
            $propertyCascoLatestImport->setAttribute('onkeyup-js', '');
            $propertyCascoLatestImport->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyCascoLatestImport);
        } else {
            $this->writeLine('Property with tag casco_latest_import already exists');
            $this->setDataKey('property_casco_latest_import_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('casco_latest_import', 'eshop_product', false);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }

    public function down()
    {
        // remove property: Casco - Posledný import(casco_latest_import)
        $propertyCascoLatestImport = $this->propertyManager()->propertyExistsByTag('casco_latest_import');
        if (($propertyCascoLatestImport !== false) && ($this->getDataKey('property_casco_latest_import_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyCascoLatestImport);
        }

        // remove property: Casco - OE numbers(casco_oe_numbers)
        $propertyCascoOeNumbers = $this->propertyManager()->propertyExistsByTag('casco_oe_numbers');
        if (($propertyCascoOeNumbers !== false) && ($this->getDataKey('property_casco_oe_numbers_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyCascoOeNumbers);
        }

        // remove property: Casco - OE number(casco_oe_number)
        $propertyCascoOeNumber = $this->propertyManager()->propertyExistsByTag('casco_oe_number');
        if (($propertyCascoOeNumber !== false) && ($this->getDataKey('property_casco_oe_number_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyCascoOeNumber);
        }

        // remove property: Casco - Skladová zásoba(casco_stock_balance)
        $propertyCascoStockBalance = $this->propertyManager()->propertyExistsByTag('casco_stock_balance');
        if (($propertyCascoStockBalance !== false) && ($this->getDataKey('property_casco_stock_balance_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyCascoStockBalance);
        }

        // remove property: Casco - Cena bez DPH(casco_price_without_vat)
        $propertyCascoPriceWithoutVat = $this->propertyManager()->propertyExistsByTag('casco_price_without_vat');
        if (($propertyCascoPriceWithoutVat !== false) && ($this->getDataKey('property_casco_price_without_vat_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyCascoPriceWithoutVat);
        }

        // remove property: Casco - Dodávateľský kód(casco_supplier_code)
        $propertyCascoSupplierCode = $this->propertyManager()->propertyExistsByTag('casco_supplier_code');
        if (($propertyCascoSupplierCode !== false) && ($this->getDataKey('property_casco_supplier_code_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyCascoSupplierCode);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }
}
