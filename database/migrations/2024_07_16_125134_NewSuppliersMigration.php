<?php

use Buxus\Migration\AbstractMigration;

/**
 * Automatic generation from (rinoparts) at 2024-07-16 12:51:34
 * Page generator: page_id=1729079,1729080,1729078
 */
class NewSuppliersMigration extends AbstractMigration
{
    public function up()
    {
        // page: Vignal 1(ID: 1729079 TAG: vignal_supplier)
        $pageId = $this->getPageIdByTag('vignal_supplier');
        if ($pageId === null) {
            $pageType = $this->pageTypesManager()->getPageTypeByTag('rolller_item_supplier');
            $page1729079 = \PageFactory::create($this->getPageIdByTag('ciselnik-dodavatel'), $pageType->getId());
        } else {
            $page1729079 = \PageFactory::get($pageId);
        }
        $page1729079->setPageName('Vignal 1');
        $page1729079->setPageTag('vignal_supplier');
        $page1729079->setPageStateId('1');
        $page1729079->setPageClassId(1);
        $page1729079->setValue('title', 'Vignal');
        $page1729079->setValue('title_en', '');
        $page1729079->setValue('title_cz', '');
        $page1729079->setValue('image', '');
        $page1729079->setValue('delivery_time', '');
        $page1729079->setValue('delivery_time_cz', '');
        $page1729079->setValue('delivery_time_en', '');
        $page1729079->setValue('onix_supplier_name', 'SAS VIGNAL SYSTEMS');
        $page1729079->save();

        // page: CLEAN 1(ID: 1729080 TAG: clean_supplier)
        $pageId = $this->getPageIdByTag('clean_supplier');
        if ($pageId === null) {
            $pageType = $this->pageTypesManager()->getPageTypeByTag('rolller_item_supplier');
            $page1729080 = \PageFactory::create($this->getPageIdByTag('ciselnik-dodavatel'), $pageType->getId());
        } else {
            $page1729080 = \PageFactory::get($pageId);
        }
        $page1729080->setPageName('CLEAN 1');
        $page1729080->setPageTag('clean_supplier');
        $page1729080->setPageStateId('1');
        $page1729080->setPageClassId(1);
        $page1729080->setValue('title', 'CLEAN');
        $page1729080->setValue('title_en', '');
        $page1729080->setValue('title_cz', '');
        $page1729080->setValue('image', '');
        $page1729080->setValue('delivery_time', '');
        $page1729080->setValue('delivery_time_cz', '');
        $page1729080->setValue('delivery_time_en', '');
        $page1729080->setValue('onix_supplier_name', 'Delgrosso S.r.l.');
        $page1729080->save();

        // page: SABO 1(ID: 1729078 TAG: sabo_supplier)
        $pageId = $this->getPageIdByTag('sabo_supplier');
        if ($pageId === null) {
            $pageType = $this->pageTypesManager()->getPageTypeByTag('rolller_item_supplier');
            $page1729078 = \PageFactory::create($this->getPageIdByTag('ciselnik-dodavatel'), $pageType->getId());
        } else {
            $page1729078 = \PageFactory::get($pageId);
        }
        $page1729078->setPageName('SABO 1');
        $page1729078->setPageTag('sabo_supplier');
        $page1729078->setPageStateId('1');
        $page1729078->setPageClassId(1);
        $page1729078->setValue('title', 'SABO');
        $page1729078->setValue('title_en', '');
        $page1729078->setValue('title_cz', '');
        $page1729078->setValue('image', '');
        $page1729078->setValue('delivery_time', '');
        $page1729078->setValue('delivery_time_cz', '');
        $page1729078->setValue('delivery_time_en', '');
        $page1729078->setValue('onix_supplier_name', 'Roberto Nuti s.p.a.');
        $page1729078->save();
    }

    public function down()
    {
        // remove page: SABO 1 (sabo_supplier)
        $pageId = $this->getPageIdByTag('sabo_supplier');
        if ($pageId !== null) {
            \PageFactory::get($pageId)->delete();
        }

        // remove page: CLEAN 1 (clean_supplier)
        $pageId = $this->getPageIdByTag('clean_supplier');
        if ($pageId !== null) {
            \PageFactory::get($pageId)->delete();
        }

        // remove page: Vignal 1 (vignal_supplier)
        $pageId = $this->getPageIdByTag('vignal_supplier');
        if ($pageId !== null) {
            \PageFactory::get($pageId)->delete();
        }
    }
}
