<?php

use Buxus\Migration\AbstractMigration;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;

/**
 * Automatic generation from (rinoparts) at 2022-08-11 15:28:35
 */
class Create_price_offer_items_table extends AbstractMigration
{
    public function up()
    {
        // create table tblPriceOfferItems
        Schema::create('tblPriceOfferItems', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('price_offer_id');
            $table->string('page_id');
            $table->string('amount');
            $table->timestamps();

            $table->foreign('price_offer_id')->references('id')->on('tblPriceOffers');
            $table->foreign('page_id')->references('page_id')->on('tblPages');
        });
    }

    public function down()
    {
        // drop table tblPriceOfferItems
        Schema::dropIfExists('tblPriceOfferItems');
    }
}
