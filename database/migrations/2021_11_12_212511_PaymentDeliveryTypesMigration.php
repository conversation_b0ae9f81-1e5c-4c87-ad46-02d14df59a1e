<?php

use Buxus\Migration\AbstractMigration;

/**
 * Automatic generation from (rinoparts) at 2021-11-12 21:25:10
 * Page generator: page_id=371614,374622,371616,371613,162
 */
class PaymentDeliveryTypesMigration extends AbstractMigration
{
    public function up()
    {
        // page: Faktúra(ID: 371614 TAG: invoice)
        $pageId = $this->getPageIdByTag('invoice');
        if ($pageId === null) {
            $pageType = $this->pageTypesManager()->getPageTypeByTag('eshop_payment_type');
            $page371614 = \PageFactory::create($this->getPageIdByTag('eshop_payment_types'), $pageType->getId());
        } else {
            $page371614 = \PageFactory::get($pageId);
        }
        $page371614->setPageName('Faktúra');
        $page371614->setPageTag('invoice');
        $page371614->setPageStateId('1');
        $page371614->setPageClassId(1);
        $page371614->setValue('exchange_rate_cz', '');
        $page371614->setValue('title', 'Faktúra');
        $page371614->setValue('title_en', '');
        $page371614->setValue('title_cz', '');
        $page371614->setValue('eshop_tag', 'invoice');
        $page371614->setValue('eshop_transaction_description', '');
        $page371614->setValue('eshop_description', 'Faktúru obdržíte pri prevzatí tovaru');
        $page371614->setValue('eshop_eur_price_including_vat', '0');
        $page371614->setValue('testing_active', 'F');
        $page371614->setValue('eshop_eur_price_including_vat_cz', '0');
        $page371614->save();

        // page: Dobierka(ID: 374622 TAG: cash_on_delivery)
        $pageId = $this->getPageIdByTag('cash_on_delivery');
        if ($pageId === null) {
            $pageType = $this->pageTypesManager()->getPageTypeByTag('eshop_payment_type');
            $page374622 = \PageFactory::create($this->getPageIdByTag('eshop_payment_types'), $pageType->getId());
        } else {
            $page374622 = \PageFactory::get($pageId);
        }
        $page374622->setPageName('Dobierka');
        $page374622->setPageTag('cash_on_delivery');
        $page374622->setPageStateId('1');
        $page374622->setPageClassId(1);
        $page374622->setValue('exchange_rate_cz', '');
        $page374622->setValue('title', 'Dobierka');
        $page374622->setValue('title_en', '');
        $page374622->setValue('title_cz', '');
        $page374622->setValue('eshop_tag', 'cash_on_delivery');
        $page374622->setValue('eshop_transaction_description', '');
        $page374622->setValue('eshop_description', '');
        $page374622->setValue('eshop_eur_price_including_vat', '0');
        $page374622->setValue('testing_active', 'F');
        $page374622->setValue('eshop_eur_price_including_vat_cz', '');
        $page374622->save();

        // page: Hotovosť(ID: 371616 TAG: payment_cash)
        $pageId = $this->getPageIdByTag('payment_cash');
        if ($pageId === null) {
            $pageType = $this->pageTypesManager()->getPageTypeByTag('eshop_payment_type');
            $page371616 = \PageFactory::create($this->getPageIdByTag('eshop_payment_types'), $pageType->getId());
        } else {
            $page371616 = \PageFactory::get($pageId);
        }
        $page371616->setPageName('Hotovosť');
        $page371616->setPageTag('payment_cash');
        $page371616->setPageStateId('1');
        $page371616->setPageClassId(1);
        $page371616->setValue('exchange_rate_cz', '');
        $page371616->setValue('title', 'Hotovosť');
        $page371616->setValue('title_en', '');
        $page371616->setValue('title_cz', '');
        $page371616->setValue('eshop_tag', 'cash');
        $page371616->setValue('eshop_transaction_description', '');
        $page371616->setValue('eshop_description', '');
        $page371616->setValue('eshop_eur_price_including_vat', '0');
        $page371616->setValue('testing_active', 'F');
        $page371616->setValue('eshop_eur_price_including_vat_cz', '');
        $page371616->save();

        // page: Kuriérska služba(ID: 371613 TAG: courier)
        $pageId = $this->getPageIdByTag('courier');
        if ($pageId === null) {
            $pageType = $this->pageTypesManager()->getPageTypeByTag('eshop_transport_type');
            $page371613 = \PageFactory::create($this->getPageIdByTag('eshop_delivery_types'), $pageType->getId());
        } else {
            $page371613 = \PageFactory::get($pageId);
        }
        $page371613->setPageName('Kuriérska služba');
        $page371613->setPageTag('courier');
        $page371613->setPageStateId('1');
        $page371613->setPageClassId(1);
        $page371613->setValue('exchange_rate_cz', '');
        $page371613->setValue('title', 'Kuriérska služba');
        $page371613->setValue('title_en', '');
        $page371613->setValue('title_cz', '');
        $page371613->setValue('eshop_tag', 'courier');
        $page371613->setValue('eshop_description', 'Zásielka Vám bude doručená na druhý pracovný deň po vyexpedovaní');
        $page371613->setValue('eshop_eur_price_without_vat', '');
        $page371613->setValue('eshop_eur_price_including_vat', '4');
        $page371613->setValue('eshop_eur_price_including_vat_cz', '150');
        $page371613->setValue('testing_active', 'F');
        $page371613->setValue('minimal_price_for_free_delivery', '');
        $page371613->setValue('minimum_price_for_cz_discount', '1501');
        $page371613->save();

        // page: Osobný odber - Žilina(ID: 162 TAG: personal)
        $pageId = $this->getPageIdByTag('personal');
        if ($pageId === null) {
            $pageType = $this->pageTypesManager()->getPageTypeByTag('eshop_transport_type');
            $page162 = \PageFactory::create($this->getPageIdByTag('eshop_delivery_types'), $pageType->getId());
        } else {
            $page162 = \PageFactory::get($pageId);
        }
        $page162->setPageName('Osobný odber - Žilina');
        $page162->setPageTag('personal');
        $page162->setPageStateId('1');
        $page162->setPageClassId(1);
        $page162->setValue('exchange_rate_cz', '');
        $page162->setValue('title', 'Osobný odber - Žilina');
        $page162->setValue('title_en', '');
        $page162->setValue('title_cz', '');
        $page162->setValue('eshop_tag', 'personal');
        $page162->setValue('eshop_description', 'Tovar si prevezmete osobne vo firme Rinoparts s.r.o. - pobočka Žilina');
        $page162->setValue('eshop_eur_price_without_vat', '');
        $page162->setValue('eshop_eur_price_including_vat', '0');
        $page162->setValue('eshop_vat_rate', '20');
        $page162->setValue('testing_active', 'F');
        $page162->setValue('minimal_price_for_free_delivery', '');
        $page162->setValue('minimum_price_for_cz_discount', '');
        $page162->setValue('eshop_eur_price_including_vat_cz', '');
        $page162->save();

        $page371613->setValue('eshop_payment_type', [
            0 => $page374622->getPageId(),
            1 => $page371614->getPageId(),
            ]);
        $page371613->save();

        $page162->setValue('eshop_payment_type', [
            0 => $page371614->getPageId(),
            1 => $page371616->getPageId(),
            ]);
        $page162->save();
    }

    public function down()
    {
        // remove page: Osobný odber - Žilina (personal)
        $pageId = $this->getPageIdByTag('personal');
        if ($pageId !== null) {
            \PageFactory::get($pageId)->delete();
        }

        // remove page: Kuriérska služba (courier)
        $pageId = $this->getPageIdByTag('courier');
        if ($pageId !== null) {
            \PageFactory::get($pageId)->delete();
        }

        // remove page: Hotovosť (payment_cash)
        $pageId = $this->getPageIdByTag('payment_cash');
        if ($pageId !== null) {
            \PageFactory::get($pageId)->delete();
        }

        // remove page: Dobierka (cash_on_delivery)
        $pageId = $this->getPageIdByTag('cash_on_delivery');
        if ($pageId !== null) {
            \PageFactory::get($pageId)->delete();
        }

        // remove page: Faktúra (invoice)
        $pageId = $this->getPageIdByTag('invoice');
        if ($pageId !== null) {
            \PageFactory::get($pageId)->delete();
        }
    }
}
