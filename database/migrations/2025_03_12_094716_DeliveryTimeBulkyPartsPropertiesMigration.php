<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;

/**
 * Automatic generation from (rinoparts) at 2025-03-12 09:47:16
 * Property generator: property=delivery_time_bulky,delivery_time_bulky_cz,delivery_time_bulky_en
 */
class DeliveryTimeBulkyPartsPropertiesMigration extends AbstractMigration
{
    public function up()
    {
        // property: Doba dodania - nadrozmer(delivery_time_bulky)
        $propertyDeliveryTimeBulky = $this->propertyManager()->propertyExistsByTag('delivery_time_bulky');
        if ($propertyDeliveryTimeBulky === false) {
            $propertyDeliveryTimeBulky = new Property();
            $propertyDeliveryTimeBulky->setTag('delivery_time_bulky');
            $propertyDeliveryTimeBulky->setDescription('');
            $propertyDeliveryTimeBulky->setExtendedDescription('');
            $propertyDeliveryTimeBulky->setName('Doba dodania - nadrozmer');
            $propertyDeliveryTimeBulky->setClassId(4);
            $propertyDeliveryTimeBulky->setShowType(null);
            $propertyDeliveryTimeBulky->setShowTypeTag('text');
            $propertyDeliveryTimeBulky->setValueType('oneline_text');
            $propertyDeliveryTimeBulky->setDefaultValue('');
            $propertyDeliveryTimeBulky->setMultiOperations(false);
            $propertyDeliveryTimeBulky->setInputString('');
            $propertyDeliveryTimeBulky->setAttribute('tab', '');
            $propertyDeliveryTimeBulky->setAttribute('size', '60');
            $propertyDeliveryTimeBulky->setAttribute('maxlength', '');
            $propertyDeliveryTimeBulky->setAttribute('readonly', 'F');
            $propertyDeliveryTimeBulky->setAttribute('pattern', '');
            $propertyDeliveryTimeBulky->setAttribute('inherit_value', 'F');
            $propertyDeliveryTimeBulky->setAttribute('onchange-js', '');
            $propertyDeliveryTimeBulky->setAttribute('onkeyup-js', '');
            $propertyDeliveryTimeBulky->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyDeliveryTimeBulky);
        } else {
            $this->writeLine('Property with tag delivery_time_bulky already exists');
            $this->setDataKey('property_delivery_time_bulky_existed', true);
        }
        if ($this->pageTypeExists('rolller_item_supplier')) {
            $this->addPropertyToPageType('delivery_time_bulky', 'rolller_item_supplier', false);
        }

        // property: Doba dodania - nadrozmer [CZ](delivery_time_bulky_cz)
        $propertyDeliveryTimeBulkyCz = $this->propertyManager()->propertyExistsByTag('delivery_time_bulky_cz');
        if ($propertyDeliveryTimeBulkyCz === false) {
            $propertyDeliveryTimeBulkyCz = new Property();
            $propertyDeliveryTimeBulkyCz->setTag('delivery_time_bulky_cz');
            $propertyDeliveryTimeBulkyCz->setDescription('');
            $propertyDeliveryTimeBulkyCz->setExtendedDescription('');
            $propertyDeliveryTimeBulkyCz->setName('Doba dodania - nadrozmer [CZ]');
            $propertyDeliveryTimeBulkyCz->setClassId(4);
            $propertyDeliveryTimeBulkyCz->setShowType(null);
            $propertyDeliveryTimeBulkyCz->setShowTypeTag('text');
            $propertyDeliveryTimeBulkyCz->setValueType('oneline_text');
            $propertyDeliveryTimeBulkyCz->setDefaultValue('');
            $propertyDeliveryTimeBulkyCz->setMultiOperations(false);
            $propertyDeliveryTimeBulkyCz->setInputString('');
            $propertyDeliveryTimeBulkyCz->setAttribute('tab', 'CZ');
            $propertyDeliveryTimeBulkyCz->setAttribute('size', '60');
            $propertyDeliveryTimeBulkyCz->setAttribute('maxlength', '');
            $propertyDeliveryTimeBulkyCz->setAttribute('readonly', 'F');
            $propertyDeliveryTimeBulkyCz->setAttribute('pattern', '');
            $propertyDeliveryTimeBulkyCz->setAttribute('inherit_value', 'F');
            $propertyDeliveryTimeBulkyCz->setAttribute('onchange-js', '');
            $propertyDeliveryTimeBulkyCz->setAttribute('onkeyup-js', '');
            $propertyDeliveryTimeBulkyCz->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyDeliveryTimeBulkyCz);
        } else {
            $this->writeLine('Property with tag delivery_time_bulky_cz already exists');
            $this->setDataKey('property_delivery_time_bulky_cz_existed', true);
        }
        if ($this->pageTypeExists('rolller_item_supplier')) {
            $this->addPropertyToPageType('delivery_time_bulky_cz', 'rolller_item_supplier', false);
        }

        // property: Doba dodania - nadrozmer [EN](delivery_time_bulky_en)
        $propertyDeliveryTimeBulkyEn = $this->propertyManager()->propertyExistsByTag('delivery_time_bulky_en');
        if ($propertyDeliveryTimeBulkyEn === false) {
            $propertyDeliveryTimeBulkyEn = new Property();
            $propertyDeliveryTimeBulkyEn->setTag('delivery_time_bulky_en');
            $propertyDeliveryTimeBulkyEn->setDescription('');
            $propertyDeliveryTimeBulkyEn->setExtendedDescription('');
            $propertyDeliveryTimeBulkyEn->setName('Doba dodania - nadrozmer [EN]');
            $propertyDeliveryTimeBulkyEn->setClassId(4);
            $propertyDeliveryTimeBulkyEn->setShowType(null);
            $propertyDeliveryTimeBulkyEn->setShowTypeTag('text');
            $propertyDeliveryTimeBulkyEn->setValueType('oneline_text');
            $propertyDeliveryTimeBulkyEn->setDefaultValue('');
            $propertyDeliveryTimeBulkyEn->setMultiOperations(false);
            $propertyDeliveryTimeBulkyEn->setInputString('');
            $propertyDeliveryTimeBulkyEn->setAttribute('tab', 'EN');
            $propertyDeliveryTimeBulkyEn->setAttribute('size', '60');
            $propertyDeliveryTimeBulkyEn->setAttribute('maxlength', '');
            $propertyDeliveryTimeBulkyEn->setAttribute('readonly', 'F');
            $propertyDeliveryTimeBulkyEn->setAttribute('pattern', '');
            $propertyDeliveryTimeBulkyEn->setAttribute('inherit_value', 'F');
            $propertyDeliveryTimeBulkyEn->setAttribute('onchange-js', '');
            $propertyDeliveryTimeBulkyEn->setAttribute('onkeyup-js', '');
            $propertyDeliveryTimeBulkyEn->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyDeliveryTimeBulkyEn);
        } else {
            $this->writeLine('Property with tag delivery_time_bulky_en already exists');
            $this->setDataKey('property_delivery_time_bulky_en_existed', true);
        }
        if ($this->pageTypeExists('rolller_item_supplier')) {
            $this->addPropertyToPageType('delivery_time_bulky_en', 'rolller_item_supplier', false);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }

    public function down()
    {
        // remove property: Doba dodania - nadrozmer [EN](delivery_time_bulky_en)
        $propertyDeliveryTimeBulkyEn = $this->propertyManager()->propertyExistsByTag('delivery_time_bulky_en');
        if (($propertyDeliveryTimeBulkyEn !== false) && ($this->getDataKey('property_delivery_time_bulky_en_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyDeliveryTimeBulkyEn);
        }

        // remove property: Doba dodania - nadrozmer [CZ](delivery_time_bulky_cz)
        $propertyDeliveryTimeBulkyCz = $this->propertyManager()->propertyExistsByTag('delivery_time_bulky_cz');
        if (($propertyDeliveryTimeBulkyCz !== false) && ($this->getDataKey('property_delivery_time_bulky_cz_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyDeliveryTimeBulkyCz);
        }

        // remove property: Doba dodania - nadrozmer(delivery_time_bulky)
        $propertyDeliveryTimeBulky = $this->propertyManager()->propertyExistsByTag('delivery_time_bulky');
        if (($propertyDeliveryTimeBulky !== false) && ($this->getDataKey('property_delivery_time_bulky_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyDeliveryTimeBulky);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }
}
