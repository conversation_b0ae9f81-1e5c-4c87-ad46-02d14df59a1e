<?php

use Buxus\Migration\AbstractMigration;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;

/**
 * Automatic generation from (rinoparts) at 2021-09-28 20:53:08
 */
class Create_margin_levels_table extends AbstractMigration
{
    public function up()
    {
        // create table margin_levels
        Schema::create('margin_levels', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->foreignId('producer_id')->constrained('producers')->onDelete('cascade');
            $table->float('price_from');
            $table->float('margin');
            $table->float('margin_eu');
            $table->timestamps();
        });
    }

    public function down()
    {
        // drop table margin_levels
        Schema::dropIfExists('margin_levels');
    }
}
