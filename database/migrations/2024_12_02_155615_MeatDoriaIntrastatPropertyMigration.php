<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;

/**
 * Automatic generation from (rinoparts) at 2024-12-02 15:56:15
 * Property generator: property=meat_doria_intrastat
 */
class MeatDoriaIntrastatPropertyMigration extends AbstractMigration
{
    public function up()
    {
        // property: MeatDoria - Intrastat číslo(meat_doria_intrastat)
        $propertyMeatDoriaIntrastat = $this->propertyManager()->propertyExistsByTag('meat_doria_intrastat');
        if ($propertyMeatDoriaIntrastat === false) {
            $propertyMeatDoriaIntrastat = new Property();
            $propertyMeatDoriaIntrastat->setTag('meat_doria_intrastat');
            $propertyMeatDoriaIntrastat->setDescription('');
            $propertyMeatDoriaIntrastat->setExtendedDescription('');
            $propertyMeatDoriaIntrastat->setName('MeatDoria - Intrastat číslo');
            $propertyMeatDoriaIntrastat->setClassId(4);
            $propertyMeatDoriaIntrastat->setShowType(null);
            $propertyMeatDoriaIntrastat->setShowTypeTag('text');
            $propertyMeatDoriaIntrastat->setValueType('oneline_text');
            $propertyMeatDoriaIntrastat->setDefaultValue('');
            $propertyMeatDoriaIntrastat->setMultiOperations(false);
            $propertyMeatDoriaIntrastat->setInputString('');
            $propertyMeatDoriaIntrastat->setAttribute('tab', 'MeatDoria');
            $propertyMeatDoriaIntrastat->setAttribute('size', '60');
            $propertyMeatDoriaIntrastat->setAttribute('maxlength', '');
            $propertyMeatDoriaIntrastat->setAttribute('readonly', 'F');
            $propertyMeatDoriaIntrastat->setAttribute('pattern', '');
            $propertyMeatDoriaIntrastat->setAttribute('inherit_value', 'F');
            $propertyMeatDoriaIntrastat->setAttribute('onchange-js', '');
            $propertyMeatDoriaIntrastat->setAttribute('onkeyup-js', '');
            $propertyMeatDoriaIntrastat->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyMeatDoriaIntrastat);
        } else {
            $this->writeLine('Property with tag meat_doria_intrastat already exists');
            $this->setDataKey('property_meat_doria_intrastat_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('meat_doria_intrastat', 'eshop_product', false);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }

    public function down()
    {
        // remove property: MeatDoria - Intrastat číslo(meat_doria_intrastat)
        $propertyMeatDoriaIntrastat = $this->propertyManager()->propertyExistsByTag('meat_doria_intrastat');
        if (($propertyMeatDoriaIntrastat !== false) && ($this->getDataKey('property_meat_doria_intrastat_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyMeatDoriaIntrastat);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }
}
