<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Translate\TranslatorFactory;
use Buxus\Property\Property;

/**
 * Automatic generation from (rinoparts) at 2021-10-26 12:36:40
 * Translation generator: sections=sk.cart
 * Property generator: property=iveco_big_db_latest_import,iveco_small_db_latest_import
 */
class ImportDateProperties extends AbstractMigration
{
    public function dependencies()
    {
        return [
            'Buxus\\Translate\\Migrations\\TranslationTableMigration',
        ];
}

    public function up()
    {
        $translator = TranslatorFactory::get();
        $editor = $translator->getEditor();

        if (!(in_array('sk', $translator->getAvailableLanguages()))) {
            $translator->setAvailableLanguages(array_merge($translator->getAvailableLanguages(), ['sk',]));
        }

        // section: cart, language: sk
        $translations = [
            'alebo_si_nechajte' => 'Alebo si nechajte',
            'login_popup_activated_user_text' => 'Ak sa prihlásite, nemusíte vyplňovať Vaše údaje. Ak si nepamätáte heslo, môžete si vyžiadať reset hesla.',
            'login_popup_activated_user_title' => 'Konto pre tento e-mail už existuje',
            'login_popup_automatic_registration_text' => 'Prihláste sa heslom, ktoré ste dostali e-mailom.',
            'login_popup_automatic_registration_title' => 'Z tejto e-mailovej adresy ste už uskutočnili nákup',
            'zaslat_docasne_heslo_na_email' => 'zaslať dočasné heslo na e-mail',
            ];
        foreach ($translations as $key => $value) {
            $editor->setTranslation($key, $value, 'cart', 'sk', true);
        }
        $editor->save();

        // property: IVECO Big DB - posledný import(iveco_big_db_latest_import)
        $propertyIvecoBigDbLatestImport = $this->propertyManager()->propertyExistsByTag('iveco_big_db_latest_import');
        if ($propertyIvecoBigDbLatestImport === false) {
            $propertyIvecoBigDbLatestImport = new Property();
            $propertyIvecoBigDbLatestImport->setTag('iveco_big_db_latest_import');
            $propertyIvecoBigDbLatestImport->setDescription('');
            $propertyIvecoBigDbLatestImport->setExtendedDescription('');
            $propertyIvecoBigDbLatestImport->setName('IVECO Big DB - posledný import');
            $propertyIvecoBigDbLatestImport->setClassId(4);
            $propertyIvecoBigDbLatestImport->setShowType(null);
            $propertyIvecoBigDbLatestImport->setShowTypeTag('text');
            $propertyIvecoBigDbLatestImport->setValueType('oneline_text');
            $propertyIvecoBigDbLatestImport->setDefaultValue('');
            $propertyIvecoBigDbLatestImport->setMultiOperations(false);
            $propertyIvecoBigDbLatestImport->setInputString('');
            $propertyIvecoBigDbLatestImport->setAttribute('tab', '');
            $propertyIvecoBigDbLatestImport->setAttribute('size', '60');
            $propertyIvecoBigDbLatestImport->setAttribute('maxlength', '');
            $propertyIvecoBigDbLatestImport->setAttribute('readonly', 'F');
            $propertyIvecoBigDbLatestImport->setAttribute('pattern', '');
            $propertyIvecoBigDbLatestImport->setAttribute('inherit_value', 'F');
            $propertyIvecoBigDbLatestImport->setAttribute('onchange-js', '');
            $propertyIvecoBigDbLatestImport->setAttribute('onkeyup-js', '');
            $propertyIvecoBigDbLatestImport->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyIvecoBigDbLatestImport);
        } else {
            $this->writeLine('Property with tag iveco_big_db_latest_import already exists');
            $this->setDataKey('property_iveco_big_db_latest_import_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('iveco_big_db_latest_import', 'eshop_product', false);
        }

        // property: IVECO Small DB - posledný import(iveco_small_db_latest_import)
        $propertyIvecoSmallDbLatestImport = $this->propertyManager()->propertyExistsByTag('iveco_small_db_latest_import');
        if ($propertyIvecoSmallDbLatestImport === false) {
            $propertyIvecoSmallDbLatestImport = new Property();
            $propertyIvecoSmallDbLatestImport->setTag('iveco_small_db_latest_import');
            $propertyIvecoSmallDbLatestImport->setDescription('');
            $propertyIvecoSmallDbLatestImport->setExtendedDescription('');
            $propertyIvecoSmallDbLatestImport->setName('IVECO Small DB - posledný import');
            $propertyIvecoSmallDbLatestImport->setClassId(4);
            $propertyIvecoSmallDbLatestImport->setShowType(null);
            $propertyIvecoSmallDbLatestImport->setShowTypeTag('text');
            $propertyIvecoSmallDbLatestImport->setValueType('oneline_text');
            $propertyIvecoSmallDbLatestImport->setDefaultValue('');
            $propertyIvecoSmallDbLatestImport->setMultiOperations(false);
            $propertyIvecoSmallDbLatestImport->setInputString('');
            $propertyIvecoSmallDbLatestImport->setAttribute('tab', '');
            $propertyIvecoSmallDbLatestImport->setAttribute('size', '60');
            $propertyIvecoSmallDbLatestImport->setAttribute('maxlength', '');
            $propertyIvecoSmallDbLatestImport->setAttribute('readonly', 'F');
            $propertyIvecoSmallDbLatestImport->setAttribute('pattern', '');
            $propertyIvecoSmallDbLatestImport->setAttribute('inherit_value', 'F');
            $propertyIvecoSmallDbLatestImport->setAttribute('onchange-js', '');
            $propertyIvecoSmallDbLatestImport->setAttribute('onkeyup-js', '');
            $propertyIvecoSmallDbLatestImport->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyIvecoSmallDbLatestImport);
        } else {
            $this->writeLine('Property with tag iveco_small_db_latest_import already exists');
            $this->setDataKey('property_iveco_small_db_latest_import_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('iveco_small_db_latest_import', 'eshop_product', false);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }

    public function down()
    {
        // remove property: IVECO Small DB - posledný import(iveco_small_db_latest_import)
        $propertyIvecoSmallDbLatestImport = $this->propertyManager()->propertyExistsByTag('iveco_small_db_latest_import');
        if (($propertyIvecoSmallDbLatestImport !== false) && ($this->getDataKey('property_iveco_small_db_latest_import_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyIvecoSmallDbLatestImport);
        }

        // remove property: IVECO Big DB - posledný import(iveco_big_db_latest_import)
        $propertyIvecoBigDbLatestImport = $this->propertyManager()->propertyExistsByTag('iveco_big_db_latest_import');
        if (($propertyIvecoBigDbLatestImport !== false) && ($this->getDataKey('property_iveco_big_db_latest_import_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyIvecoBigDbLatestImport);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }
}
