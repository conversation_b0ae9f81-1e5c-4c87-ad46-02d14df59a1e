<?php

use Buxus\Migration\AbstractMigration;

/**
 * Automatic generation from (rinoparts) at 2021-11-09 09:51:17
 * Page generator: page_id=371616
 */
class CashPaymentMethodMigration extends AbstractMigration
{
    public function up()
    {
        // page: Hotovosť(ID: 371616 TAG: payment_cash)
        $pageId = $this->getPageIdByTag('payment_cash');
        if ($pageId === null) {
            $pageType = $this->pageTypesManager()->getPageTypeByTag('eshop_payment_type');
            $page371616 = \PageFactory::create($this->getPageIdByTag('eshop_payment_types'), $pageType->getId());
        } else {
            $page371616 = \PageFactory::get($pageId);
        }
        $page371616->setPageName('Hotovosť');
        $page371616->setPageTag('payment_cash');
        $page371616->setPageStateId('1');
        $page371616->setPageClassId(1);
        $page371616->setValue('exchange_rate_cz', '');
        $page371616->setValue('title', 'Hotovosť');
        $page371616->setValue('title_en', '');
        $page371616->setValue('title_cz', '');
        $page371616->setValue('eshop_tag', 'cash');
        $page371616->setValue('eshop_transaction_description', '');
        $page371616->setValue('eshop_description', '');
        $page371616->setValue('eshop_eur_price_including_vat', '0');
        $page371616->setValue('testing_active', 'F');
        $page371616->setValue('eshop_eur_price_including_vat_cz', '');
        $page371616->save();
    }

    public function down()
    {
        // remove page: Hotovosť (payment_cash)
        $pageId = $this->getPageIdByTag('payment_cash');
        if ($pageId !== null) {
            \PageFactory::get($pageId)->delete();
        }
    }
}
