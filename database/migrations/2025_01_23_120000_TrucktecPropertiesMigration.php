<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;

/**
 * Automatic generation from (rinoparts) at 2025-01-23 12:00:00
 * Property generator: property=trucktec_supplier_code,trucktec_price_without_vat,trucktec_stock_balance,trucktec_oe_number,trucktec_oe_numbers,trucktec_latest_import
 */
class TrucktecPropertiesMigration extends AbstractMigration
{
    public function up()
    {
        // property: Trucktec - Dodávateľský kód(trucktec_supplier_code)
        $propertyTrucktecSupplierCode = $this->propertyManager()->propertyExistsByTag('trucktec_supplier_code');
        if ($propertyTrucktecSupplierCode === false) {
            $propertyTrucktecSupplierCode = new Property();
            $propertyTrucktecSupplierCode->setTag('trucktec_supplier_code');
            $propertyTrucktecSupplierCode->setDescription('');
            $propertyTrucktecSupplierCode->setExtendedDescription('');
            $propertyTrucktecSupplierCode->setName('Trucktec - Dodávateľský kód');
            $propertyTrucktecSupplierCode->setClassId(4);
            $propertyTrucktecSupplierCode->setShowType(null);
            $propertyTrucktecSupplierCode->setShowTypeTag('text');
            $propertyTrucktecSupplierCode->setValueType('oneline_text');
            $propertyTrucktecSupplierCode->setDefaultValue('');
            $propertyTrucktecSupplierCode->setMultiOperations(false);
            $propertyTrucktecSupplierCode->setInputString('');
            $propertyTrucktecSupplierCode->setAttribute('tab', 'Trucktec');
            $propertyTrucktecSupplierCode->setAttribute('size', '60');
            $propertyTrucktecSupplierCode->setAttribute('maxlength', '');
            $propertyTrucktecSupplierCode->setAttribute('readonly', 'F');
            $propertyTrucktecSupplierCode->setAttribute('pattern', '');
            $propertyTrucktecSupplierCode->setAttribute('inherit_value', 'F');
            $propertyTrucktecSupplierCode->setAttribute('onchange-js', '');
            $propertyTrucktecSupplierCode->setAttribute('onkeyup-js', '');
            $propertyTrucktecSupplierCode->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyTrucktecSupplierCode);
        } else {
            $this->writeLine('Property with tag trucktec_supplier_code already exists');
            $this->setDataKey('property_trucktec_supplier_code_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('trucktec_supplier_code', 'eshop_product', false);
        }

        // property: Trucktec - Cena bez DPH(trucktec_price_without_vat)
        $propertyTrucktecPriceWithoutVat = $this->propertyManager()->propertyExistsByTag('trucktec_price_without_vat');
        if ($propertyTrucktecPriceWithoutVat === false) {
            $propertyTrucktecPriceWithoutVat = new Property();
            $propertyTrucktecPriceWithoutVat->setTag('trucktec_price_without_vat');
            $propertyTrucktecPriceWithoutVat->setDescription('');
            $propertyTrucktecPriceWithoutVat->setExtendedDescription('');
            $propertyTrucktecPriceWithoutVat->setName('Trucktec - Cena bez DPH');
            $propertyTrucktecPriceWithoutVat->setClassId(4);
            $propertyTrucktecPriceWithoutVat->setShowType(null);
            $propertyTrucktecPriceWithoutVat->setShowTypeTag('text');
            $propertyTrucktecPriceWithoutVat->setValueType('oneline_text');
            $propertyTrucktecPriceWithoutVat->setDefaultValue('');
            $propertyTrucktecPriceWithoutVat->setMultiOperations(false);
            $propertyTrucktecPriceWithoutVat->setInputString('');
            $propertyTrucktecPriceWithoutVat->setAttribute('tab', 'Trucktec');
            $propertyTrucktecPriceWithoutVat->setAttribute('size', '60');
            $propertyTrucktecPriceWithoutVat->setAttribute('maxlength', '');
            $propertyTrucktecPriceWithoutVat->setAttribute('readonly', 'F');
            $propertyTrucktecPriceWithoutVat->setAttribute('pattern', '');
            $propertyTrucktecPriceWithoutVat->setAttribute('inherit_value', 'F');
            $propertyTrucktecPriceWithoutVat->setAttribute('onchange-js', '');
            $propertyTrucktecPriceWithoutVat->setAttribute('onkeyup-js', '');
            $propertyTrucktecPriceWithoutVat->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyTrucktecPriceWithoutVat);
        } else {
            $this->writeLine('Property with tag trucktec_price_without_vat already exists');
            $this->setDataKey('property_trucktec_price_without_vat_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('trucktec_price_without_vat', 'eshop_product', false);
        }

        // property: Trucktec - Skladová zásoba(trucktec_stock_balance)
        $propertyTrucktecStockBalance = $this->propertyManager()->propertyExistsByTag('trucktec_stock_balance');
        if ($propertyTrucktecStockBalance === false) {
            $propertyTrucktecStockBalance = new Property();
            $propertyTrucktecStockBalance->setTag('trucktec_stock_balance');
            $propertyTrucktecStockBalance->setDescription('');
            $propertyTrucktecStockBalance->setExtendedDescription('');
            $propertyTrucktecStockBalance->setName('Trucktec - Skladová zásoba');
            $propertyTrucktecStockBalance->setClassId(4);
            $propertyTrucktecStockBalance->setShowType(null);
            $propertyTrucktecStockBalance->setShowTypeTag('text');
            $propertyTrucktecStockBalance->setValueType('oneline_text');
            $propertyTrucktecStockBalance->setDefaultValue('');
            $propertyTrucktecStockBalance->setMultiOperations(false);
            $propertyTrucktecStockBalance->setInputString('');
            $propertyTrucktecStockBalance->setAttribute('tab', 'Trucktec');
            $propertyTrucktecStockBalance->setAttribute('size', '60');
            $propertyTrucktecStockBalance->setAttribute('maxlength', '');
            $propertyTrucktecStockBalance->setAttribute('readonly', 'F');
            $propertyTrucktecStockBalance->setAttribute('pattern', '');
            $propertyTrucktecStockBalance->setAttribute('inherit_value', 'F');
            $propertyTrucktecStockBalance->setAttribute('onchange-js', '');
            $propertyTrucktecStockBalance->setAttribute('onkeyup-js', '');
            $propertyTrucktecStockBalance->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyTrucktecStockBalance);
        } else {
            $this->writeLine('Property with tag trucktec_stock_balance already exists');
            $this->setDataKey('property_trucktec_stock_balance_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('trucktec_stock_balance', 'eshop_product', false);
        }

        // property: Trucktec - OE number(trucktec_oe_number)
        $propertyTrucktecOeNumber = $this->propertyManager()->propertyExistsByTag('trucktec_oe_number');
        if ($propertyTrucktecOeNumber === false) {
            $propertyTrucktecOeNumber = new Property();
            $propertyTrucktecOeNumber->setTag('trucktec_oe_number');
            $propertyTrucktecOeNumber->setDescription('');
            $propertyTrucktecOeNumber->setExtendedDescription('');
            $propertyTrucktecOeNumber->setName('Trucktec - OE number');
            $propertyTrucktecOeNumber->setClassId(4);
            $propertyTrucktecOeNumber->setShowType(null);
            $propertyTrucktecOeNumber->setShowTypeTag('text');
            $propertyTrucktecOeNumber->setValueType('oneline_text');
            $propertyTrucktecOeNumber->setDefaultValue('');
            $propertyTrucktecOeNumber->setMultiOperations(false);
            $propertyTrucktecOeNumber->setInputString('');
            $propertyTrucktecOeNumber->setAttribute('tab', 'Trucktec');
            $propertyTrucktecOeNumber->setAttribute('size', '60');
            $propertyTrucktecOeNumber->setAttribute('maxlength', '');
            $propertyTrucktecOeNumber->setAttribute('readonly', 'F');
            $propertyTrucktecOeNumber->setAttribute('pattern', '');
            $propertyTrucktecOeNumber->setAttribute('inherit_value', 'F');
            $propertyTrucktecOeNumber->setAttribute('onchange-js', '');
            $propertyTrucktecOeNumber->setAttribute('onkeyup-js', '');
            $propertyTrucktecOeNumber->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyTrucktecOeNumber);
        } else {
            $this->writeLine('Property with tag trucktec_oe_number already exists');
            $this->setDataKey('property_trucktec_oe_number_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('trucktec_oe_number', 'eshop_product', false);
        }

        // property: Trucktec - OE numbers(trucktec_oe_numbers)
        $propertyTrucktecOeNumbers = $this->propertyManager()->propertyExistsByTag('trucktec_oe_numbers');
        if ($propertyTrucktecOeNumbers === false) {
            $propertyTrucktecOeNumbers = new Property();
            $propertyTrucktecOeNumbers->setTag('trucktec_oe_numbers');
            $propertyTrucktecOeNumbers->setDescription('');
            $propertyTrucktecOeNumbers->setExtendedDescription('');
            $propertyTrucktecOeNumbers->setName('Trucktec - OE numbers');
            $propertyTrucktecOeNumbers->setClassId(4);
            $propertyTrucktecOeNumbers->setShowType(null);
            $propertyTrucktecOeNumbers->setShowTypeTag('text');
            $propertyTrucktecOeNumbers->setValueType('oneline_text');
            $propertyTrucktecOeNumbers->setDefaultValue('');
            $propertyTrucktecOeNumbers->setMultiOperations(false);
            $propertyTrucktecOeNumbers->setInputString('');
            $propertyTrucktecOeNumbers->setAttribute('tab', 'Trucktec');
            $propertyTrucktecOeNumbers->setAttribute('size', '60');
            $propertyTrucktecOeNumbers->setAttribute('maxlength', '');
            $propertyTrucktecOeNumbers->setAttribute('readonly', 'F');
            $propertyTrucktecOeNumbers->setAttribute('pattern', '');
            $propertyTrucktecOeNumbers->setAttribute('inherit_value', 'F');
            $propertyTrucktecOeNumbers->setAttribute('onchange-js', '');
            $propertyTrucktecOeNumbers->setAttribute('onkeyup-js', '');
            $propertyTrucktecOeNumbers->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyTrucktecOeNumbers);
        } else {
            $this->writeLine('Property with tag trucktec_oe_numbers already exists');
            $this->setDataKey('property_trucktec_oe_numbers_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('trucktec_oe_numbers', 'eshop_product', false);
        }

        // property: Trucktec - Posledný import(trucktec_latest_import)
        $propertyTrucktecLatestImport = $this->propertyManager()->propertyExistsByTag('trucktec_latest_import');
        if ($propertyTrucktecLatestImport === false) {
            $propertyTrucktecLatestImport = new Property();
            $propertyTrucktecLatestImport->setTag('trucktec_latest_import');
            $propertyTrucktecLatestImport->setDescription('');
            $propertyTrucktecLatestImport->setExtendedDescription('');
            $propertyTrucktecLatestImport->setName('Trucktec - Posledný import');
            $propertyTrucktecLatestImport->setClassId(4);
            $propertyTrucktecLatestImport->setShowType(null);
            $propertyTrucktecLatestImport->setShowTypeTag('text');
            $propertyTrucktecLatestImport->setValueType('oneline_text');
            $propertyTrucktecLatestImport->setDefaultValue('');
            $propertyTrucktecLatestImport->setMultiOperations(false);
            $propertyTrucktecLatestImport->setInputString('');
            $propertyTrucktecLatestImport->setAttribute('tab', 'Trucktec');
            $propertyTrucktecLatestImport->setAttribute('size', '60');
            $propertyTrucktecLatestImport->setAttribute('maxlength', '');
            $propertyTrucktecLatestImport->setAttribute('readonly', 'F');
            $propertyTrucktecLatestImport->setAttribute('pattern', '');
            $propertyTrucktecLatestImport->setAttribute('inherit_value', 'F');
            $propertyTrucktecLatestImport->setAttribute('onchange-js', '');
            $propertyTrucktecLatestImport->setAttribute('onkeyup-js', '');
            $propertyTrucktecLatestImport->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyTrucktecLatestImport);
        } else {
            $this->writeLine('Property with tag trucktec_latest_import already exists');
            $this->setDataKey('property_trucktec_latest_import_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('trucktec_latest_import', 'eshop_product', false);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }

    public function down()
    {
        // remove property: Trucktec - Posledný import(trucktec_latest_import)
        $propertyTrucktecLatestImport = $this->propertyManager()->propertyExistsByTag('trucktec_latest_import');
        if (($propertyTrucktecLatestImport !== false) && ($this->getDataKey('property_trucktec_latest_import_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyTrucktecLatestImport);
        }

        // remove property: Trucktec - OE numbers(trucktec_oe_numbers)
        $propertyTrucktecOeNumbers = $this->propertyManager()->propertyExistsByTag('trucktec_oe_numbers');
        if (($propertyTrucktecOeNumbers !== false) && ($this->getDataKey('property_trucktec_oe_numbers_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyTrucktecOeNumbers);
        }

        // remove property: Trucktec - OE number(trucktec_oe_number)
        $propertyTrucktecOeNumber = $this->propertyManager()->propertyExistsByTag('trucktec_oe_number');
        if (($propertyTrucktecOeNumber !== false) && ($this->getDataKey('property_trucktec_oe_number_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyTrucktecOeNumber);
        }

        // remove property: Trucktec - Skladová zásoba(trucktec_stock_balance)
        $propertyTrucktecStockBalance = $this->propertyManager()->propertyExistsByTag('trucktec_stock_balance');
        if (($propertyTrucktecStockBalance !== false) && ($this->getDataKey('property_trucktec_stock_balance_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyTrucktecStockBalance);
        }

        // remove property: Trucktec - Cena bez DPH(trucktec_price_without_vat)
        $propertyTrucktecPriceWithoutVat = $this->propertyManager()->propertyExistsByTag('trucktec_price_without_vat');
        if (($propertyTrucktecPriceWithoutVat !== false) && ($this->getDataKey('property_trucktec_price_without_vat_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyTrucktecPriceWithoutVat);
        }

        // remove property: Trucktec - Dodávateľský kód(trucktec_supplier_code)
        $propertyTrucktecSupplierCode = $this->propertyManager()->propertyExistsByTag('trucktec_supplier_code');
        if (($propertyTrucktecSupplierCode !== false) && ($this->getDataKey('property_trucktec_supplier_code_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyTrucktecSupplierCode);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }
}
