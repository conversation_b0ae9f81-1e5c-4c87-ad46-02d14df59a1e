<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;

/**
 * Automatic generation from (rinoparts) at 2023-01-19 09:40:45
 * Property generator: property=supplier_ean
 */
class SupplierEanPropertyMigration extends AbstractMigration
{
    public function up()
    {
        // property: Dodávateľský EAN(supplier_ean)
        $propertySupplierEan = $this->propertyManager()->propertyExistsByTag('supplier_ean');
        if ($propertySupplierEan === false) {
            $propertySupplierEan = new Property();
            $propertySupplierEan->setTag('supplier_ean');
            $propertySupplierEan->setDescription('');
            $propertySupplierEan->setExtendedDescription('');
            $propertySupplierEan->setName('Dodávateľský EAN');
            $propertySupplierEan->setClassId(4);
            $propertySupplierEan->setShowType(null);
            $propertySupplierEan->setShowTypeTag('text');
            $propertySupplierEan->setValueType('oneline_text');
            $propertySupplierEan->setDefaultValue('');
            $propertySupplierEan->setMultiOperations(false);
            $propertySupplierEan->setInputString('');
            $propertySupplierEan->setAttribute('tab', 'Onix');
            $propertySupplierEan->setAttribute('size', '60');
            $propertySupplierEan->setAttribute('maxlength', '');
            $propertySupplierEan->setAttribute('readonly', 'F');
            $propertySupplierEan->setAttribute('pattern', '');
            $propertySupplierEan->setAttribute('inherit_value', 'F');
            $propertySupplierEan->setAttribute('onchange-js', '');
            $propertySupplierEan->setAttribute('onkeyup-js', '');
            $propertySupplierEan->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertySupplierEan);
        } else {
            $this->writeLine('Property with tag supplier_ean already exists');
            $this->setDataKey('property_supplier_ean_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('supplier_ean', 'eshop_product', false);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }

    public function down()
    {
        // remove property: Dodávateľský EAN(supplier_ean)
        $propertySupplierEan = $this->propertyManager()->propertyExistsByTag('supplier_ean');
        if (($propertySupplierEan !== false) && ($this->getDataKey('property_supplier_ean_existed') === null)) {
            $this->propertyManager()->removeProperty($propertySupplierEan);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }
}
