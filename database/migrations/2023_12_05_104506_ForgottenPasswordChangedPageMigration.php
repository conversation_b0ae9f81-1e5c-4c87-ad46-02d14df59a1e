<?php

use Buxus\Migration\AbstractMigration;

/**
 * Automatic generation from (buxus_rinoparts_test) at 2023-12-05 10:45:06
 * Page generator: page_id=830048
 */
class ForgottenPasswordChangedPageMigration extends AbstractMigration
{
    public function up()
    {
        // page: <PERSON>abudnut<PERSON> heslo: Ú<PERSON><PERSON><PERSON><PERSON><PERSON> zmenen<PERSON> he<PERSON>lo(ID: 830048 TAG: auth_forgotten_password_changed)
        $pageId = $this->getPageIdByTag('auth_forgotten_password_changed');
        if ($pageId === null) {
            $pageType = $this->pageTypesManager()->getPageTypeByTag('service_page');
            $page830048 = \PageFactory::create($this->getPageIdByTag('auth_forgotten_password'), $pageType->getId());
        } else {
            $page830048 = \PageFactory::get($pageId);
        }
        $page830048->setPageName('Zabudnuté heslo: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> zmenen<PERSON> heslo');
        $page830048->setPageTag('auth_forgotten_password_changed');
        $page830048->setPageStateId('2');
        $page830048->setPageClassId(1);
        $page830048->setValue('title', 'Úspešné zmenené heslo');
        $page830048->setValue('title_en', 'Successfully changed password');
        $page830048->setValue('title_cz', 'Úspěšné změnené heslo');
        $page830048->setValue('text', '<p>Vaše heslo bolo úspešne zmenené. Stále však musíte počkať na schválenie prihlasovania administrátorom.</p>');
        $page830048->setValue('text_en', '<p>Your password has been successfully changed. However, you still need to go through admin login approval.</p>');
        $page830048->setValue('text_cz', '<p>Vaše heslo bylo úspěšně změněno. Stále však musíte počkat na schválení přihlašování administrátorem.</p>');
        $page830048->setValue('seo_url_name', null);
        $page830048->setValue('meta_title', '');
        $page830048->setValue('seo_url_name_en', null);
        $page830048->setValue('meta_title_en', '');
        $page830048->setValue('seo_url_name_cz', null);
        $page830048->setValue('meta_title_cz', '');
        // set template index::index
        $page830048->getPageTemplate()->setController('index');
        $page830048->getPageTemplate()->setAction('index');
        $page830048->save();
    }

    public function down()
    {
        // remove page: Zabudnuté heslo: Úspešné zmenené heslo (auth_forgotten_password_changed)
        $pageId = $this->getPageIdByTag('auth_forgotten_password_changed');
        if ($pageId !== null) {
            \PageFactory::get($pageId)->delete();
        }
    }
}
