<?php

use Buxus\Migration\AbstractMigration;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;

/**
 * Automatic generation from (rinoparts) at 2021-09-28 18:58:21
 */
class Create_producers_table extends AbstractMigration
{
    public function up()
    {
        // create table producers
        Schema::create('producers', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('name')->unique();
            $table->boolean('price_levels_on');
            $table->timestamps();
        });
    }

    public function down()
    {
        // drop table brands
        Schema::dropIfExists('producers');
    }
}
