<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;

/**
 * Automatic generation from (buxus_babytrend_live) at 2018-02-17 13:03:08
 * Property generator: property=minimal_price_for_free_delivery
 */

class CreateMinimalPriceForFreeDeliveryProperty extends AbstractMigration
{
	public function up()
	{
		// property: Minimalna cena s DPH pre dopravu ZADARMO(minimal_price_for_free_delivery)
		$property_minimal_price_for_free_delivery = $this->propertyManager()->propertyExistsByTag('minimal_price_for_free_delivery');
		if ($property_minimal_price_for_free_delivery === false) {
			$property_minimal_price_for_free_delivery = new Property();
			$property_minimal_price_for_free_delivery->setTag('minimal_price_for_free_delivery');
			$property_minimal_price_for_free_delivery->setDescription('Cena s DPH od ktorej sa nebude rátať za tento typ dopravy žiaden poplatok');
			$property_minimal_price_for_free_delivery->setExtendedDescription(NULL);
			$property_minimal_price_for_free_delivery->setName('Minimálna cena s DPH pre dopravu ZADARMO');
			$property_minimal_price_for_free_delivery->setClassId('4');
			$property_minimal_price_for_free_delivery->setShowType(NULL);
			$property_minimal_price_for_free_delivery->setShowTypeTag('text');
			$property_minimal_price_for_free_delivery->setValueType('oneline_text');
			$property_minimal_price_for_free_delivery->setDefaultValue('');
			$property_minimal_price_for_free_delivery->setMultiOperations(false);
			$property_minimal_price_for_free_delivery->setInputString(NULL);
			$property_minimal_price_for_free_delivery->setAttribute('tab', '');
			$property_minimal_price_for_free_delivery->setAttribute('size', '10');
			$property_minimal_price_for_free_delivery->setAttribute('maxlength', '');
			$property_minimal_price_for_free_delivery->setAttribute('readonly', 'F');
			$property_minimal_price_for_free_delivery->setAttribute('pattern', '');
			$property_minimal_price_for_free_delivery->setAttribute('inherit_value', 'F');
			$property_minimal_price_for_free_delivery->setAttribute('onchange-js', '');
			$property_minimal_price_for_free_delivery->setAttribute('onkeyup-js', '');
			$property_minimal_price_for_free_delivery->setAttribute('onkeydown-js', '');
			$this->propertyManager()->saveProperty($property_minimal_price_for_free_delivery);
		} else {
			$this->writeLine('Property with tag minimal_price_for_free_delivery already exists');
			$this->setDataKey('property_minimal_price_for_free_delivery_existed', true);
		}
		if ($this->pageTypeExists('eshop_transport_type')) {
			$this->addPropertyToPageType('minimal_price_for_free_delivery', 'eshop_transport_type', false);
		}

		// regenerate property constants
		$this->propertyManager()->generateConstants();

	}

	public function down()
	{
		// remove property: Minimalna cena s DPH pre dopravu ZADARMO(minimal_price_for_free_delivery)
		$property_minimal_price_for_free_delivery = $this->propertyManager()->propertyExistsByTag('minimal_price_for_free_delivery');
		if ($property_minimal_price_for_free_delivery != false) {
			$supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_minimal_price_for_free_delivery);
			if ((is_null($this->getDataKey('property_minimal_price_for_free_delivery_existed'))) && (count($supported_page_types) == 0)) {
				$this->propertyManager()->removeProperty($property_minimal_price_for_free_delivery);
			}
		}

		// regenerate property constants
		$this->propertyManager()->generateConstants();

	}
}
