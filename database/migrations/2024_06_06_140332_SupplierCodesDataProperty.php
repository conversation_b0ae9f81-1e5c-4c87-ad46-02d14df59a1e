<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;

/**
 * Automatic generation from (buxus_rinoparts_live) at 2024-06-06 14:03:32
 * Property generator: property=onix_supplier_codes_data
 */
class SupplierCodesDataProperty extends AbstractMigration
{
    public function up()
    {
        // property: Supplier codes data(onix_supplier_codes_data)
        $propertyOnixSupplierCodesData = $this->propertyManager()->propertyExistsByTag('onix_supplier_codes_data');
        if ($propertyOnixSupplierCodesData === false) {
            $propertyOnixSupplierCodesData = new Property();
            $propertyOnixSupplierCodesData->setTag('onix_supplier_codes_data');
            $propertyOnixSupplierCodesData->setDescription('');
            $propertyOnixSupplierCodesData->setExtendedDescription('');
            $propertyOnixSupplierCodesData->setName('Supplier codes data');
            $propertyOnixSupplierCodesData->setClassId(4);
            $propertyOnixSupplierCodesData->setShowType(null);
            $propertyOnixSupplierCodesData->setShowTypeTag('data_table');
            $propertyOnixSupplierCodesData->setValueType('data_table');
            $propertyOnixSupplierCodesData->setDefaultValue('');
            $propertyOnixSupplierCodesData->setMultiOperations(false);
            $propertyOnixSupplierCodesData->setInputString('');
            $propertyOnixSupplierCodesData->setAttribute('tab', 'Onix');
            $propertyOnixSupplierCodesData->setAttribute('options', []);
            $propertyOnixSupplierCodesData->setAttribute('row_options', []);
            $propertyOnixSupplierCodesData->setAttribute('default_rows', '');
            $propertyOnixSupplierCodesData->setAttribute('default_columns', '');
            $propertyOnixSupplierCodesData->setAttribute('enable_column_add', '0');
            $propertyOnixSupplierCodesData->setAttribute('enable_column_remove', '0');
            $propertyOnixSupplierCodesData->setAttribute('enable_row_add', '0');
            $propertyOnixSupplierCodesData->setAttribute('enable_row_remove', '0');
            $this->propertyManager()->saveProperty($propertyOnixSupplierCodesData);
        } else {
            $this->writeLine('Property with tag onix_supplier_codes_data already exists');
            $this->setDataKey('property_onix_supplier_codes_data_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('onix_supplier_codes_data', 'eshop_product', false);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }

    public function down()
    {
        // remove property: Supplier codes data(onix_supplier_codes_data)
        $propertyOnixSupplierCodesData = $this->propertyManager()->propertyExistsByTag('onix_supplier_codes_data');
        if (($propertyOnixSupplierCodesData !== false) && ($this->getDataKey('property_onix_supplier_codes_data_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyOnixSupplierCodesData);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }
}
