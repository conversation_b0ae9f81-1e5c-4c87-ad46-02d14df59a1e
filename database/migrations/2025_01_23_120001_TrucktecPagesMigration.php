<?php

use Buxus\Migration\AbstractMigration;

/**
 * Automatic generation from (rinoparts) at 2025-01-23 12:00:01
 * Page generator: page_id=trucktec_supplier,trucktec_settings
 */
class TrucktecPagesMigration extends AbstractMigration
{
    public function up()
    {
        // page: Trucktec(TAG: trucktec_supplier)
        $pageId = $this->getPageIdByTag('trucktec_supplier');
        if ($pageId === null) {
            $pageType = $this->pageTypesManager()->getPageTypeByTag('rolller_item_supplier');
            $pageTrucktecSupplier = \PageFactory::create($this->getPageIdByTag('ciselnik-dodavatel'), $pageType->getId());
        } else {
            $pageTrucktecSupplier = \PageFactory::get($pageId);
        }
        $pageTrucktecSupplier->setPageName('Trucktec');
        $pageTrucktecSupplier->setPageTag('trucktec_supplier');
        $pageTrucktecSupplier->setPageStateId('1');
        $pageTrucktecSupplier->setPageClassId(1);
        $pageTrucktecSupplier->setValue('title', 'Trucktec');
        $pageTrucktecSupplier->setValue('title_en', '');
        $pageTrucktecSupplier->setValue('title_cz', '');
        $pageTrucktecSupplier->setValue('image', '');
        $pageTrucktecSupplier->setValue('delivery_time', '');
        $pageTrucktecSupplier->setValue('delivery_time_cz', '');
        $pageTrucktecSupplier->setValue('delivery_time_en', '');
        $pageTrucktecSupplier->save();

        // page: Trucktec Settings(TAG: trucktec_settings)
        $pageId = $this->getPageIdByTag('trucktec_settings');
        if ($pageId === null) {
            $pageType = $this->pageTypesManager()->getPageTypeByTag('supplier_catalog_settings');
            $pageTrucktecSettings = \PageFactory::create($this->getPageIdByTag('suppliers_settings'), $pageType->getId());
        } else {
            $pageTrucktecSettings = \PageFactory::get($pageId);
        }
        $pageTrucktecSettings->setPageName('Trucktec Settings');
        $pageTrucktecSettings->setPageTag('trucktec_settings');
        $pageTrucktecSettings->setPageStateId('2');
        $pageTrucktecSettings->setPageClassId(1);
        $pageTrucktecSettings->setValue('transport_surcharge', '4');
        $pageTrucktecSettings->save();
    }

    public function down()
    {
        // remove page: Trucktec Settings (trucktec_settings)
        $pageId = $this->getPageIdByTag('trucktec_settings');
        if ($pageId !== null) {
            \PageFactory::get($pageId)->delete();
        }

        // remove page: Trucktec (trucktec_supplier)
        $pageId = $this->getPageIdByTag('trucktec_supplier');
        if ($pageId !== null) {
            \PageFactory::get($pageId)->delete();
        }
    }
}
