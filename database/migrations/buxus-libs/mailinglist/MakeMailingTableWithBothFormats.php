<?php

namespace Mailinglist\Migrations;

use Buxus\Migration\AbstractMigration;
use Illuminate\Support\Facades\Schema;

class MakeMailingTableWithBothFormats extends AbstractMigration
{
    public function dependencies()
    {
        return [
            BaseMailinglistTables::class,
        ];
    }

    public function up()
    {
        if (Schema::hasColumn('tblMailingListBatchEmails', 'format')) {
            Schema::table('tblMailingListBatchEmails', function($table) {
                $table->dropColumn('format');
            });
        }

        if (Schema::hasColumn('tblMailingListBatchEmails', 'body')) {
            Schema::table('tblMailingListBatchEmails', function($table) {
                $table->renameColumn('body', 'body_text');
            });
        }

        if (!Schema::hasColumn('tblMailingListBatchEmails', 'body_html')) {
            Schema::table('tblMailingListBatchEmails', function($table) {
                $table->text('body_html')->nullable()->after('body_text');
            });
        }

        if (!Schema::hasColumn('tblMailingListBatchEmails', 'embed_images')) {
            Schema::table('tblMailingListBatchEmails', function($table) {
                $table->boolean('embed_images')->default(false);
            });
        }

        if (Schema::hasColumn('tblMailingListBatchEmailsBackup', 'format')) {
            Schema::table('tblMailingListBatchEmailsBackup', function($table) {
                $table->dropColumn('format');
            });
        }

        if (Schema::hasColumn('tblMailingListBatchEmailsBackup', 'body')) {
            Schema::table('tblMailingListBatchEmailsBackup', function($table) {
                $table->renameColumn('body', 'body_text');
            });
        }

        if (!Schema::hasColumn('tblMailingListBatchEmailsBackup', 'body_html')) {
            Schema::table('tblMailingListBatchEmailsBackup', function($table) {
                $table->text('body_html')->nullable()->after('body_text');
            });
        }

        if (!Schema::hasColumn('tblMailingListBatchEmailsBackup', 'embed_images')) {
            Schema::table('tblMailingListBatchEmailsBackup', function($table) {
                $table->boolean('embed_images')->default(false);
            });
        }
    }

    public function down()
    {
        if (Schema::hasColumn('tblMailingListBatchEmailsBackup', 'body_html')) {
            Schema::table('tblMailingListBatchEmailsBackup', function($table) {
                $table->dropColumn('body_html');
            });
        }
        if (Schema::hasColumn('tblMailingListBatchEmailsBackup', 'body_text')) {
            Schema::table('tblMailingListBatchEmailsBackup', function($table) {
                $table->renameColumn('body_text', 'body');
            });
        }
        if (!Schema::hasColumn('tblMailingListBatchEmailsBackup', 'format')) {
            Schema::table('tblMailingListBatchEmailsBackup', function($table) {
                $table->enum('format', ['T','F','D'])->default('T')->after('email_id');
            });
        }

        if (Schema::hasColumn('tblMailingListBatchEmailsBackup', 'embed_images')) {
            Schema::table('tblMailingListBatchEmailsBackup', function($table) {
                $table->dropColumn('embed_images');
            });
        }

        if (Schema::hasColumn('tblMailingListBatchEmails', 'body_html')) {
            Schema::table('tblMailingListBatchEmails', function($table) {
                $table->dropColumn('body_html');
            });
        }

        if (Schema::hasColumn('tblMailingListBatchEmails', 'body_text')) {
            Schema::table('tblMailingListBatchEmails', function($table) {
                $table->renameColumn('body_text', 'body');
            });
        }

        if (!Schema::hasColumn('tblMailingListBatchEmails', 'format')) {
            Schema::table('tblMailingListBatchEmails', function($table) {
                $table->enum('format', ['T','F','D'])->default('T')->after('email_id');
            });
        }

        if (Schema::hasColumn('tblMailingListBatchEmails', 'embed_images')) {
            Schema::table('tblMailingListBatchEmails', function($table) {
                $table->dropColumn('embed_images');
            });
        }
    }

}