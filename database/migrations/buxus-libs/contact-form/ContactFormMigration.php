<?php

namespace ContactForm;

use Buxus\Migration\AbstractMigration;
use Buxus\Util\PageIds;
use Email\Migrations\EmailMigration;

class ContactFormMigration extends AbstractMigration
{
    public function dependencies()
    {
        return array(
            EmailMigration::class,
        );
    }

    public function up()
    {
        // page: Kontaktný formulár 1(ID: 161 TAG: contact_form)
        $pageId = $this->getPageIdByTag('contact_form');
        if ($pageId === null) {
            $pageType = $this->pageTypesManager()->getPageTypeByTag('folder');
            $page161 = \PageFactory::create($this->getPageIdByTag('admin_settings'), $pageType->getId());
        } else {
            $page161 = \PageFactory::get($pageId);
        }
        $page161->setPageName('Kontaktný formulár');
        $page161->setPageTag('contact_form');
        $page161->setPageStateId('2');
        $page161->setPageClassId('1');
        $page161->save();

        // page: Stránka formuláru(ID: 163 TAG: Kontaktný formulár)
        $pageType = $this->pageTypesManager()->getPageTypeByTag('service_page');
        $page163 = \PageFactory::create($page161->getPageId(), $pageType->getId());
        $page163->setPageName('Kontaktný formulár - stránka formuláru');
        $page163->setPageTag('contact_form_page');
        $page163->setPageStateId('1');
        $page163->setPageClassId('1');
        $page163->setSortDateTime(date('Y-m-d H:i:s', time() + 0));
        $page163->setPropertyValue('title', 'Kontaktný formulár');
        $page163->setPropertyValue('text', '<p>Napíšte nám Vašu otázku a my Vás budeme v priebehu dvoch dní kontaktovať.</p>');
        // set template form::contact
        $page163->getPageTemplate()->setController('contact-form');
        $page163->getPageTemplate()->setAction('contact-form');
        $page163->save();

        // page: Úspešné odoslanie kontaktného formuláru(ID: 164 TAG: Úspešné odoslanie kontaktného formuláru)
        $pageType = $this->pageTypesManager()->getPageTypeByTag('service_page');
        $page164 = \PageFactory::create($page161->getPageId(), $pageType->getId());
        $page164->setPageName('Úspešné odoslanie kontaktného formuláru');
        $page164->setPageTag('contact_form_sent');
        $page164->setPageStateId('2');
        $page164->setPageClassId('1');
        $page164->setSortDateTime(date('Y-m-d H:i:s', time() + 1));
        $page164->setPropertyValue('title', 'Úspešné odoslanie formuláru');
        $page164->setPropertyValue('text', '<p>Váš formulár bol úspešne odoslaný. V priebehu dvoch dní Vás budeme kontaktovať.</p>');
        // set template index::index
        $page164->getPageTemplate()->setController('index');
        $page164->getPageTemplate()->setAction('index');
        $page164->save();


        // regenerate page tags
        PageIds::generatePageTagsList();

        $pageId = $this->getPageIdByTag('menu');
        if ($pageId !== null) {
            $menu = \PageFactory::get($pageId);
            $listProperty = $menu->getPropertyValue('page_list_menu_top');
            if (!empty($listProperty)) {
                $list = $menu->getPropertyValue('page_list_menu_top')->getValue();
            } else {
                $list = array();
            }

            $list[] = array(
                'to_page_id' => $page163->getPageId(),
            );
            $menu->setValue('page_list_menu_top', $list);
            $menu->save();
        }

        // add simple entities

        \BuxusDB::get()->insert('tblSimpleEntities', array(
            'entity_tag' => 'contact_form',
            'entity_name' => 'Kontaktný formulár',
            'entity_type_tag' => 'form',
        ));
        \BuxusDB::get()->insert('tblSimpleEntities', array(
            'entity_tag' => 'first_name',
            'entity_name' => 'Meno',
            'entity_type_tag' => 'form_option',
        ));
        \BuxusDB::get()->insert('tblSimpleEntities', array(
            'entity_tag' => 'surname',
            'entity_name' => 'Priezvisko',
            'entity_type_tag' => 'form_option',
        ));
        \BuxusDB::get()->insert('tblSimpleEntities', array(
            'entity_tag' => 'email',
            'entity_name' => 'Email',
            'entity_type_tag' => 'form_option',
        ));
        \BuxusDB::get()->insert('tblSimpleEntities', array(
            'entity_tag' => 'text',
            'entity_name' => 'Text',
            'entity_type_tag' => 'form_option',
        ));
    }

    public function down()
    {
        \BuxusDB::get()->delete('tblSimpleEntities', array(
            'entity_tag = ?' => 'text',
            'entity_type_tag = ?' => 'form_option',
        ));
        \BuxusDB::get()->delete('tblSimpleEntities', array(
            'entity_tag = ?' => 'email',
            'entity_type_tag = ?' => 'form_option',
        ));
        \BuxusDB::get()->delete('tblSimpleEntities', array(
            'entity_tag = ?' => 'surname',
            'entity_type_tag = ?' => 'form_option',
        ));
        \BuxusDB::get()->delete('tblSimpleEntities', array(
            'entity_tag = ?' => 'first_name',
            'entity_type_tag = ?' => 'form_option',
        ));
        \BuxusDB::get()->delete('tblSimpleEntities', array(
            'entity_tag = ?' => 'contact_form',
            'entity_type_tag = ?' => 'form',
        ));

        // remove page: Kontaktný formulár 1 (contact_form)
        $page_id = $this->getPageIdByTag('contact_form');
        if ($page_id !== null) {
            \PageFactory::get($page_id)->delete();
        }

        // regenerate page tags
        PageIds::generatePageTagsList();
    }
}
