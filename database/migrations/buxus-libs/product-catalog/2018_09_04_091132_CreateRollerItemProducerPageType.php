<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;
use Buxus\PageType\PageType;
use Buxus\PageType\PageTypePropertyItem;

/**
 * Automatic generation from (najlyze) at 2018-09-04 09:11:32
 * PageType generator: page_type=roller_item_producer
 */

class CreateRollerItemProducerPageType extends AbstractMigration
{

	public function up()
	{
		// property: Titulok(title)
		$property_title = $this->propertyManager()->propertyExistsByTag('title');
		if ($property_title === false) {
			$property_title = new Property();
			$property_title->setTag('title');
			$property_title->setDescription('Štandardný názov stránky. Text, ktorý sa zobrazí v záložke prehliadača. Podľa SEO odporúčaní max. 65 znakov.');
			$property_title->setExtendedDescription('');
			$property_title->setName('Titulok');
			$property_title->setClassId(4);
			$property_title->setShowType(NULL);
			$property_title->setShowTypeTag('text');
			$property_title->setValueType('oneline_text');
			$property_title->setDefaultValue('');
			$property_title->setMultiOperations(false);
			$property_title->setInputString(NULL);
			$property_title->setAttribute('tab', '');
			$property_title->setAttribute('size', '60');
			$property_title->setAttribute('maxlength', '');
			$property_title->setAttribute('readonly', 'F');
			$property_title->setAttribute('pattern', '');
			$property_title->setAttribute('inherit_value', 'F');
			$property_title->setAttribute('onchange-js', '');
			$property_title->setAttribute('onkeyup-js', '');
			$property_title->setAttribute('onkeydown-js', '');
			$this->propertyManager()->saveProperty($property_title);
		} else {
			$this->writeLine('Property with tag title already exists');
			$this->setDataKey('property_title_existed', true);
		}

		// property: Obrázok(image)
		$property_image = $this->propertyManager()->propertyExistsByTag('image');
		if ($property_image === false) {
			$property_image = new Property();
			$property_image->setTag('image');
			$property_image->setDescription('Obrázok');
			$property_image->setExtendedDescription('');
			$property_image->setName('Obrázok');
			$property_image->setClassId(4);
			$property_image->setShowType(NULL);
			$property_image->setShowTypeTag('image_name_upload');
			$property_image->setValueType('file');
			$property_image->setDefaultValue('');
			$property_image->setMultiOperations(false);
			$property_image->setInputString('');
			$property_image->setAttribute('tab', '');
			$property_image->setAttribute('with_upload', 'T');
			$property_image->setAttribute('simple_upload', 'F');
			$property_image->setAttribute('show_input_element', 'T');
			$property_image->setAttribute('filename', '');
			$property_image->setAttribute('show_file_name', 'T');
			$property_image->setAttribute('file_type', 'image');
			$property_image->setAttribute('pattern', '');
			$property_image->setAttribute('show_thumbnail', 'T');
			$property_image->setAttribute('max_thumbnail_width', '150');
			$property_image->setAttribute('max_thumbnail_height', '80');
			$property_image->setAttribute('upload_subdir', '');
			$property_image->setAttribute('eval-code', '//$this-&gt;parsed_parm[\'upload_subdir\'] = \'my-dir\';');
			$property_image->setAttribute('onchange-js', '');
			$this->propertyManager()->saveProperty($property_image);
		} else {
			$this->writeLine('Property with tag image already exists');
			$this->setDataKey('property_image_existed', true);
		}

		// page type: Položka číselníka (ciselnik_item)
		$page_type_ciselnik_item = $this->pageTypesManager()->pageTypeExistsByTag('ciselnik_item');
		if ($page_type_ciselnik_item === false) {
			$page_type_ciselnik_item = new PageType();
			$page_type_ciselnik_item->setTag('ciselnik_item');
			$page_type_ciselnik_item->setName('Položka číselníka');
			$page_type_ciselnik_item->setPageClassId(1);
			$page_type_ciselnik_item->setDefaultTemplateId(2);
			$page_type_ciselnik_item->setDeleteTrigger('');
			$page_type_ciselnik_item->setIncludeInSync(NULL);
			$page_type_ciselnik_item->setPageDetailsLayout('');
			$page_type_ciselnik_item->setPageSortTypeTag('sort_date_time');
			$page_type_ciselnik_item->setPageTypeOrder(0);
			$page_type_ciselnik_item->setPostmoveTrigger('');
			$page_type_ciselnik_item->setPostsubmitTrigger('');
			$page_type_ciselnik_item->setPresubmitTrigger('');
			$page_type_ciselnik_item->setParent(NULL);
		} else {
			$this->writeLine('Page type with tag ciselnik_item already exists');
			$this->setDataKey('page_type_ciselnik_item_existed', true);
		}
		$property = $this->propertyManager()->getPropertyByTag('title');
		$property_id = $property->getId();
		$tmp = $page_type_ciselnik_item->getPropertyItemForPropertyId($property_id);
		if ($tmp === null) {
			$tmp = new PageTypePropertyItem($property);
			$tmp->setOrder(1);
			$tmp->setRequired(false);
			$page_type_ciselnik_item->addPropertyItem($tmp);
		}
		$property = $this->propertyManager()->getPropertyByTag('image');
		$property_id = $property->getId();
		$tmp = $page_type_ciselnik_item->getPropertyItemForPropertyId($property_id);
		if ($tmp === null) {
			$tmp = new PageTypePropertyItem($property);
			$tmp->setOrder(2);
			$tmp->setRequired(false);
			$page_type_ciselnik_item->addPropertyItem($tmp);
		}
		$this->pageTypesManager()->savePageType($page_type_ciselnik_item);

		// page type: Výrobca (položka číselníka) (roller_item_producer)
		$page_type_roller_item_producer = $this->pageTypesManager()->pageTypeExistsByTag('roller_item_producer');
		if ($page_type_roller_item_producer === false) {
			$page_type_roller_item_producer = new PageType();
			$page_type_roller_item_producer->setTag('roller_item_producer');
			$page_type_roller_item_producer->setName('Výrobca (položka číselníka)');
			$page_type_roller_item_producer->setPageClassId(1);
			$page_type_roller_item_producer->setDefaultTemplateId(1);
			$page_type_roller_item_producer->setDeleteTrigger('');
			$page_type_roller_item_producer->setIncludeInSync(NULL);
			$page_type_roller_item_producer->setPageDetailsLayout('');
			$page_type_roller_item_producer->setPageSortTypeTag('sort_date_time');
			$page_type_roller_item_producer->setPageTypeOrder(0);
			$page_type_roller_item_producer->setPostmoveTrigger('');
			$page_type_roller_item_producer->setPostsubmitTrigger('');
			$page_type_roller_item_producer->setPresubmitTrigger('');
			$parent = $this->pageTypesManager()->getPageTypeByTag('ciselnik_item');
			$page_type_roller_item_producer->setParent($parent);
		} else {
			$this->writeLine('Page type with tag roller_item_producer already exists');
			$this->setDataKey('page_type_roller_item_producer_existed', true);
		}
		$property = $this->propertyManager()->getPropertyByTag('title');
		$property_id = $property->getId();
		$tmp = $page_type_roller_item_producer->getPropertyItemForPropertyId($property_id);
		if ($tmp === null) {
			$tmp = new PageTypePropertyItem($property);
			$tmp->setOrder(1);
			$tmp->setRequired(false);
			$page_type_roller_item_producer->addPropertyItem($tmp);
		}
		$property = $this->propertyManager()->getPropertyByTag('image');
		$property_id = $property->getId();
		$tmp = $page_type_roller_item_producer->getPropertyItemForPropertyId($property_id);
		if ($tmp === null) {
			$tmp = new PageTypePropertyItem($property);
			$tmp->setOrder(2);
			$tmp->setRequired(false);
			$page_type_roller_item_producer->addPropertyItem($tmp);
		}
		$this->pageTypesManager()->savePageType($page_type_roller_item_producer);

		if ($this->pageTypeExists('ciselnik')) {
			$this->addPageTypeSuperiorPageType('ciselnik_item', 'ciselnik');
		}
		if ($this->pageTypeExists('ciselnik')) {
			$this->addPageTypeSuperiorPageType('roller_item_producer', 'ciselnik');
		}

		// regenerate property constants
		$this->propertyManager()->generateConstants();

		// regenerate page types constants
		$this->pageTypesManager()->generateConstants();

	}

	public function down()
	{
		// remove page type: Výrobca (položka číselníka) (roller_item_producer)
		$page_type_roller_item_producer = $this->pageTypesManager()->pageTypeExistsByTag('roller_item_producer');
		if (($page_type_roller_item_producer != false) && (is_null($this->getDataKey('page_type_roller_item_producer_existed')))) {
			$this->pageTypesManager()->removePageType($page_type_roller_item_producer);
		}

		// remove page type: Položka číselníka (ciselnik_item)
		$page_type_ciselnik_item = $this->pageTypesManager()->pageTypeExistsByTag('ciselnik_item');
		if (($page_type_ciselnik_item != false) && (is_null($this->getDataKey('page_type_ciselnik_item_existed')))) {
			$this->pageTypesManager()->removePageType($page_type_ciselnik_item);
		}

		// remove property: Obrázok(image)
		$property_image = $this->propertyManager()->propertyExistsByTag('image');
		if ($property_image != false) {
			$supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_image);
			if ((is_null($this->getDataKey('property_image_existed'))) && (count($supported_page_types) == 0)) {
				$this->propertyManager()->removeProperty($property_image);
			}
		}

		// remove property: Titulok(title)
		$property_title = $this->propertyManager()->propertyExistsByTag('title');
		if ($property_title != false) {
			$supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_title);
			if ((is_null($this->getDataKey('property_title_existed'))) && (count($supported_page_types) == 0)) {
				$this->propertyManager()->removeProperty($property_title);
			}
		}

		// regenerate property constants
		$this->propertyManager()->generateConstants();

		// regenerate page types constants
		$this->pageTypesManager()->generateConstants();

	}

}
