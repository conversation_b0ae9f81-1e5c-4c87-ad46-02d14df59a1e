<?php

use Buxus\Migration\AbstractMigration;

/**
 * Automatic generation from (najlyze) at 2019-08-16 12:30:54
 */
class Fill_delivery_oraculum_default_property_values extends AbstractMigration
{
    public function up()
    {
        $page = PageFactory::get(\Buxus\Util\PageIds::getEshopPropertiesSettings());
        if (!empty($page)) {
            $page->setValue('next_day_delivery_deadline', '15:00');
            $page->setValue('shipping_duration_days', '1');
            $page->setValue('not_delivery_days', '6,7');
            $page->save();
        }
    }

    public function down()
    {
        $page = PageFactory::get(\Buxus\Util\PageIds::getEshopPropertiesSettings());
        if (!empty($page)) {
            $page->setValue('next_day_delivery_deadline', '');
            $page->setValue('shipping_duration_days', '');
            $page->setValue('not_delivery_days', '');
            $page->save();
        }
    }
}
