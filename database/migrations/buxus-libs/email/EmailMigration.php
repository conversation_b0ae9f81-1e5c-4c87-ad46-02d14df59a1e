<?php
namespace Email\Migrations;

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Types\PageList;

class EmailMigration extends AbstractMigration {
    public function up() {
        // page type: Nastavenia (settings)
        $page_type_settings = $this->pageTypesManager()->pageTypeExistsByTag('settings');
        if ($page_type_settings === false) {
            $page_type_settings = new \Buxus\PageType\PageType();
            $page_type_settings->setTag('settings');
            $page_type_settings->setName('Nastavenia');
            $page_type_settings->setPageClassId('1');
            $page_type_settings->setDefaultTemplateId('2');
            $page_type_settings->setDeleteTrigger('');
            $page_type_settings->setIncludeInSync('1');
            $page_type_settings->setPageDetailsLayout('');
            $page_type_settings->setPageSortTypeTag('sort_date_time');
            $page_type_settings->setPageTypeOrder('999');
            $page_type_settings->setPostmoveTrigger('');
            $page_type_settings->setPostsubmitTrigger('');
            $page_type_settings->setPresubmitTrigger('');
            $page_type_settings->setParent(NULL);

        } else {
            $this->writeLine('Page type with tag settings already exists');
            $this->setDataKey('page_type_settings_existed', true);
        }
        if ($this->pageTypeExists('main_page')) {
            $page_type_settings->addSuperiorPageType($this->getPageTypeByTag('main_page'));
        }
        if ($this->pageTypeExists('eshop_catalog')) {
            $page_type_settings->addSuperiorPageType($this->getPageTypeByTag('eshop_catalog'));
        }
        if ($this->pageTypeExists('settings')) {
            $page_type_settings->addSuperiorPageType($this->getPageTypeByTag('settings'));
        }
        if ($this->pageTypeExists('folder')) {
            $page_type_settings->addSuperiorPageType($this->getPageTypeByTag('folder'));
        }
        $this->pageTypesManager()->savePageType($page_type_settings);
        // set template on MAIN PAGE index::error404
        $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('settings'), 'index', 'error404');

        // property: Titulok(title)
        $property_title = $this->propertyManager()->propertyExistsByTag('title');
        if ($property_title === false) {
            $property_title = new \Buxus\Property\Types\Input();
            $property_title->setTag('title');
            $property_title->setDescription('Štandardný názov stránky.
Podľa SEO odporúčaní max. 65 znakov.');
            $property_title->setExtendedDescription('');
            $property_title->setName('Titulok');
            $property_title->setClassId('4');
            $property_title->setShowType(NULL);
            $property_title->setShowTypeTag('text');
            $property_title->setValueType('oneline_text');
            $property_title->setDefaultValue('');
            $property_title->setMultiOperations(false);
            $property_title->setInputString(NULL);
            $property_title->setAttribute('tab', '');
            $property_title->setAttribute('size', '60');
            $property_title->setAttribute('maxlength', '');
            $property_title->setAttribute('readonly', 'F');
            $property_title->setAttribute('pattern', '');
            $property_title->setAttribute('inherit_value', 'F');
            $property_title->setAttribute('onchange-js', '');
            $property_title->setAttribute('onkeyup-js', '');
            $property_title->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($property_title);
        } else {
            $this->writeLine('Property with tag title already exists');
            $this->setDataKey('property_title_existed', true);
        }

        // property: Súbor(file)
        $property_file = $this->propertyManager()->propertyExistsByTag('file');
        if ($property_file === false) {
            $property_file = new \Buxus\Property\Types\File();
            $property_file->setTag('file');
            $property_file->setDescription('Súbor pre prílohu');
            $property_file->setExtendedDescription(NULL);
            $property_file->setName('Súbor');
            $property_file->setClassId('4');
            $property_file->setShowType(NULL);
            $property_file->setShowTypeTag('file_name_upload');
            $property_file->setValueType('file');
            $property_file->setDefaultValue('');
            $property_file->setMultiOperations(false);
            $property_file->setInputString(NULL);
            $property_file->setAttribute('tab', '');
            $property_file->setAttribute('with_upload', 'T');
            $property_file->setAttribute('simple_upload', 'F');
            $property_file->setAttribute('show_input_element', 'T');
            $property_file->setAttribute('filename', '');
            $property_file->setAttribute('show_file_name', 'T');
            $property_file->setAttribute('file_type', 'document');
            $property_file->setAttribute('pattern', '');
            $property_file->setAttribute('show_thumbnail', 'T');
            $property_file->setAttribute('max_thumbnail_width', 150);
            $property_file->setAttribute('max_thumbnail_height', 80);
            $property_file->setAttribute('upload_subdir', '');
            $property_file->setAttribute('eval-code', '//$this-&gt;parsed_parm[\'upload_subdir\'] = \'my-dir\';');
            $property_file->setAttribute('onchange-js', '');
            $this->propertyManager()->saveProperty($property_file);
        } else {
            $this->writeLine('Property with tag file already exists');
            $this->setDataKey('property_file_existed', true);
        }

        // page type: Príloha (attachment)
        $page_type_attachment = $this->pageTypesManager()->pageTypeExistsByTag('attachment');
        if ($page_type_attachment === false) {
            $page_type_attachment = new \Buxus\PageType\PageType();
            $page_type_attachment->setTag('attachment');
            $page_type_attachment->setName('Príloha');
            $page_type_attachment->setPageClassId('1');
            $page_type_attachment->setDefaultTemplateId('2');
            $page_type_attachment->setDeleteTrigger('');
            $page_type_attachment->setIncludeInSync('0');
            $page_type_attachment->setPageDetailsLayout(NULL);
            $page_type_attachment->setPageSortTypeTag('sort_date_time');
            $page_type_attachment->setPageTypeOrder(NULL);
            $page_type_attachment->setPostmoveTrigger('');
            $page_type_attachment->setPostsubmitTrigger('');
            $page_type_attachment->setPresubmitTrigger('');
            $page_type_attachment->setParent(NULL);
        } else {
            $this->writeLine('Page type with tag attachment already exists');
            $this->setDataKey('page_type_attachment_existed', true);
        }
        $property = $this->propertyManager()->getPropertyByTag('title');
        $property_id = $property->getId();
        $tmp = $page_type_attachment->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('1');
            $tmp->setRequired(true);
            $page_type_attachment->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('file');
        $property_id = $property->getId();
        $tmp = $page_type_attachment->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('2');
            $tmp->setRequired(true);
            $page_type_attachment->addPropertyItem($tmp);
        }
        $this->pageTypesManager()->savePageType($page_type_attachment);

        // property: Odosielateľ(email_sender)
        $property_email_sender = $this->propertyManager()->propertyExistsByTag('email_sender');
        if ($property_email_sender === false) {
            $property_email_sender = new \Buxus\Property\Types\Input();
            $property_email_sender->setTag('email_sender');
            $property_email_sender->setDescription('Odosielateľ e-mailu.');
            $property_email_sender->setExtendedDescription(NULL);
            $property_email_sender->setName('Odosielateľ');
            $property_email_sender->setClassId('4');
            $property_email_sender->setShowType(NULL);
            $property_email_sender->setShowTypeTag('text');
            $property_email_sender->setValueType('oneline_text');
            $property_email_sender->setDefaultValue('');
            $property_email_sender->setMultiOperations(false);
            $property_email_sender->setInputString(NULL);
            $property_email_sender->setAttribute('tab', '');
            $property_email_sender->setAttribute('size', '80');
            $property_email_sender->setAttribute('maxlength', '');
            $property_email_sender->setAttribute('readonly', '0');
            $property_email_sender->setAttribute('pattern', '');
            $property_email_sender->setAttribute('inherit_value', '0');
            $property_email_sender->setAttribute('onchange-js', '');
            $property_email_sender->setAttribute('onkeyup-js', '');
            $property_email_sender->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($property_email_sender);
        } else {
            $this->writeLine('Property with tag email_sender already exists');
            $this->setDataKey('property_email_sender_existed', true);
        }

        // property: Príjemcovia(email_recipients)
        $property_email_recipients = $this->propertyManager()->propertyExistsByTag('email_recipients');
        if ($property_email_recipients === false) {
            $property_email_recipients = new \Buxus\Property\Types\Input();
            $property_email_recipients->setTag('email_recipients');
            $property_email_recipients->setDescription('Príjemcovia e-mailu. Viac príjemcov oddeľte čiarkou: ",".');
            $property_email_recipients->setExtendedDescription(NULL);
            $property_email_recipients->setName('Príjemcovia');
            $property_email_recipients->setClassId('4');
            $property_email_recipients->setShowType(NULL);
            $property_email_recipients->setShowTypeTag('textarea');
            $property_email_recipients->setValueType('multiline_text');
            $property_email_recipients->setDefaultValue('');
            $property_email_recipients->setMultiOperations(false);
            $property_email_recipients->setInputString(NULL);
            $property_email_recipients->setAttribute('tab', '');
            $property_email_recipients->setAttribute('cols', '80');
            $property_email_recipients->setAttribute('rows', '3');
            $property_email_recipients->setAttribute('dhtml-edit', '0');
            $property_email_recipients->setAttribute('dhtml-configuration', 'full');
            $property_email_recipients->setAttribute('import-word', '0');
            $property_email_recipients->setAttribute('auto', '1');
            $property_email_recipients->setAttribute('inherit_value', 'F');
            $property_email_recipients->setAttribute('onchange-js', '');
            $property_email_recipients->setAttribute('onkeyup-js', '');
            $property_email_recipients->setAttribute('onkeydown-js', '');
            $property_email_recipients->setAttribute('pattern', '');
            $this->propertyManager()->saveProperty($property_email_recipients);
        } else {
            $this->writeLine('Property with tag email_recipients already exists');
            $this->setDataKey('property_email_recipients_existed', true);
        }

        // property: Predmet(email_subject)
        $property_email_subject = $this->propertyManager()->propertyExistsByTag('email_subject');
        if ($property_email_subject === false) {
            $property_email_subject = new \Buxus\Property\Types\Input();
            $property_email_subject->setTag('email_subject');
            $property_email_subject->setDescription('Predmet e-mailu.');
            $property_email_subject->setExtendedDescription(NULL);
            $property_email_subject->setName('Predmet');
            $property_email_subject->setClassId('4');
            $property_email_subject->setShowType(NULL);
            $property_email_subject->setShowTypeTag('text');
            $property_email_subject->setValueType('oneline_text');
            $property_email_subject->setDefaultValue('');
            $property_email_subject->setMultiOperations(false);
            $property_email_subject->setInputString(NULL);
            $property_email_subject->setAttribute('tab', '');
            $property_email_subject->setAttribute('size', '80');
            $property_email_subject->setAttribute('maxlength', '');
            $property_email_subject->setAttribute('readonly', '0');
            $property_email_subject->setAttribute('pattern', '');
            $property_email_subject->setAttribute('inherit_value', '0');
            $property_email_subject->setAttribute('onchange-js', '');
            $property_email_subject->setAttribute('onkeyup-js', '');
            $property_email_subject->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($property_email_subject);
        } else {
            $this->writeLine('Property with tag email_subject already exists');
            $this->setDataKey('property_email_subject_existed', true);
        }

        // property: Správa Text(email_body)
        $property_email_body = $this->propertyManager()->propertyExistsByTag('email_body');
        if ($property_email_body === false) {
            $property_email_body = new \Buxus\Property\Types\Textarea();
            $property_email_body->setTag('email_body');
            $property_email_body->setDescription('Telo e-mailu.');
            $property_email_body->setExtendedDescription(NULL);
            $property_email_body->setName('Správa Text');
            $property_email_body->setClassId('4');
            $property_email_body->setShowType(NULL);
            $property_email_body->setShowTypeTag('textarea');
            $property_email_body->setValueType('multiline_text');
            $property_email_body->setDefaultValue('');
            $property_email_body->setMultiOperations(false);
            $property_email_body->setInputString(NULL);
            $property_email_body->setAttribute('tab', '');
            $property_email_body->setAttribute('cols', '80');
            $property_email_body->setAttribute('rows', '10');
            $property_email_body->setAttribute('dhtml-edit', '0');
            $property_email_body->setAttribute('dhtml-configuration', 'full');
            $property_email_body->setAttribute('import-word', '0');
            $property_email_body->setAttribute('auto', '1');
            $property_email_body->setAttribute('inherit_value', 'F');
            $property_email_body->setAttribute('onchange-js', '');
            $property_email_body->setAttribute('onkeyup-js', '');
            $property_email_body->setAttribute('onkeydown-js', '');
            $property_email_body->setAttribute('pattern', '');
            $this->propertyManager()->saveProperty($property_email_body);
        } else {
            $this->writeLine('Property with tag email_body already exists');
            $this->setDataKey('property_email_body_existed', true);
        }

        // property: Embedovať obrázky priamo do správy(mail_embed_images)
        $property_mail_embed_images = $this->propertyManager()->propertyExistsByTag('mail_embed_images');
        if ($property_mail_embed_images === false) {
            $property_mail_embed_images = new \Buxus\Property\Types\CheckBox();
            $property_mail_embed_images->setTag('mail_embed_images');
            $property_mail_embed_images->setDescription('Ak je nastavené, obrázky sa do HTML mailov budú priamo vkladať. Budú súčasťou emailu namiesto toho aby boli len odkazmi na web.');
            $property_mail_embed_images->setExtendedDescription(NULL);
            $property_mail_embed_images->setName('Embedovať obrázky priamo do správy');
            $property_mail_embed_images->setClassId('4');
            $property_mail_embed_images->setShowType(NULL);
            $property_mail_embed_images->setShowTypeTag('checkbox');
            $property_mail_embed_images->setValueType('logical_value');
            $property_mail_embed_images->setDefaultValue('');
            $property_mail_embed_images->setMultiOperations(false);
            $property_mail_embed_images->setInputString(NULL);
            $property_mail_embed_images->setAttribute('tab', '');
            $property_mail_embed_images->setAttribute('on_value', 'T');
            $property_mail_embed_images->setAttribute('off_value', 'F');
            $property_mail_embed_images->setAttribute('onclick-js', '');
            $property_mail_embed_images->setAttribute('inherit_value', '0');
            $this->propertyManager()->saveProperty($property_mail_embed_images);
        } else {
            $this->writeLine('Property with tag mail_embed_images already exists');
            $this->setDataKey('property_mail_embed_images_existed', true);
        }

        // property: Správa HTML(email_body_html)
        $property_email_body_html = $this->propertyManager()->propertyExistsByTag('email_body_html');
        if ($property_email_body_html === false) {
            $property_email_body_html = new \Buxus\Property\Types\Textarea();
            $property_email_body_html->setTag('email_body_html');
            $property_email_body_html->setDescription('Telo e-mailu v HTML formáte.');
            $property_email_body_html->setExtendedDescription(NULL);
            $property_email_body_html->setName('Správa HTML');
            $property_email_body_html->setClassId('4');
            $property_email_body_html->setShowType(NULL);
            $property_email_body_html->setShowTypeTag('textarea');
            $property_email_body_html->setValueType('multiline_text');
            $property_email_body_html->setDefaultValue('');
            $property_email_body_html->setMultiOperations(false);
            $property_email_body_html->setInputString(NULL);
            $property_email_body_html->setAttribute('tab', '');
            $property_email_body_html->setAttribute('cols', '60');
            $property_email_body_html->setAttribute('rows', '1');
            $property_email_body_html->setAttribute('dhtml-edit', '1');

            $property_email_body_html->setPropertyAttribute('dhtml_edit', '1');

            $property_email_body_html->setAttribute('dhtml-configuration', 'full');
            $property_email_body_html->setAttribute('import-word', '0');
            $property_email_body_html->setAttribute('auto', '1');
            $property_email_body_html->setAttribute('inherit_value', 'F');
            $property_email_body_html->setAttribute('onchange-js', '');
            $property_email_body_html->setAttribute('onkeyup-js', '');
            $property_email_body_html->setAttribute('onkeydown-js', '');
            $property_email_body_html->setAttribute('pattern', '');
            $this->propertyManager()->saveProperty($property_email_body_html);
        } else {
            $this->writeLine('Property with tag email_body_html already exists');
            $this->setDataKey('property_email_body_html_existed', true);
        }

        // property: Zoznam príloh(attachment_list)
        $property_attachment_list = $this->propertyManager()->propertyExistsByTag('attachment_list');
        if ($property_attachment_list === false) {
            $property_attachment_list = new PageList();
            $property_attachment_list->setTag('attachment_list');
            $property_attachment_list->setDescription('Zoznam príloh pre správu');
            $property_attachment_list->setExtendedDescription(NULL);
            $property_attachment_list->setName('Zoznam príloh');
            $property_attachment_list->setClassId('4');
            $property_attachment_list->setShowType(NULL);
            $property_attachment_list->setShowTypeTag('page_list');
            $property_attachment_list->setValueType('page_list');
            $property_attachment_list->setDefaultValue('');
            $property_attachment_list->setMultiOperations(false);
            $property_attachment_list->setInputString(NULL);
            $property_attachment_list->setAttribute('tab', '');
            $property_attachment_list->setAttribute('root_page_id', '');
            $property_attachment_list->setAttribute('page_type_id', $page_type_attachment->getId());
            $property_attachment_list->setAttribute('default_sort', 'tblPages.sort_date_time');
            $property_attachment_list->setAttribute('advanced_mode', 'T');
            $property_attachment_list->setAttribute('external_url', 'F');
            $property_attachment_list->setAttribute('max_items', '');
            $property_attachment_list->setAttribute('middle_col_width', '');
            $property_attachment_list->setAttribute('apply_user_rights', 'T');
            $property_attachment_list->setAttribute('property_for_link_name', 'title');
            $property_attachment_list->setAttribute('properties_for_search', 'tblPages.page_name,title');
            $this->propertyManager()->saveProperty($property_attachment_list);
        } else {
            $this->writeLine('Property with tag attachment_list already exists');
            $this->setDataKey('property_attachment_list_existed', true);
        }

        // page type: E-mail (e_mail)
        $page_type_e_mail = $this->pageTypesManager()->pageTypeExistsByTag('e_mail');
        if ($page_type_e_mail === false) {
            $page_type_e_mail = new \Buxus\PageType\PageType();
            $page_type_e_mail->setTag('e_mail');
            $page_type_e_mail->setName('E-mail');
            $page_type_e_mail->setPageClassId('1');
            $page_type_e_mail->setDefaultTemplateId('2');
            $page_type_e_mail->setDeleteTrigger('');
            $page_type_e_mail->setIncludeInSync('1');
            $page_type_e_mail->setPageDetailsLayout(NULL);
            $page_type_e_mail->setPageSortTypeTag('sort_date_time');
            $page_type_e_mail->setPageTypeOrder(NULL);
            $page_type_e_mail->setPostmoveTrigger('');
            $page_type_e_mail->setPostsubmitTrigger('');
            $page_type_e_mail->setPresubmitTrigger('');
            $page_type_e_mail->setParent(NULL);
        } else {
            $this->writeLine('Page type with tag e_mail already exists');
            $this->setDataKey('page_type_e_mail_existed', true);
        }
        $property = $this->propertyManager()->getPropertyByTag('email_sender');
        $property_id = $property->getId();
        $tmp = $page_type_e_mail->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('1');
            $tmp->setRequired(false);
            $page_type_e_mail->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('email_recipients');
        $property_id = $property->getId();
        $tmp = $page_type_e_mail->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('4');
            $tmp->setRequired(false);
            $page_type_e_mail->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('email_subject');
        $property_id = $property->getId();
        $tmp = $page_type_e_mail->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('7');
            $tmp->setRequired(false);
            $page_type_e_mail->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('email_body');
        $property_id = $property->getId();
        $tmp = $page_type_e_mail->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('10');
            $tmp->setRequired(false);
            $page_type_e_mail->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('mail_embed_images');
        $property_id = $property->getId();
        $tmp = $page_type_e_mail->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('13');
            $tmp->setRequired(false);
            $page_type_e_mail->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('email_body_html');
        $property_id = $property->getId();
        $tmp = $page_type_e_mail->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('16');
            $tmp->setRequired(false);
            $page_type_e_mail->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('attachment_list');
        $property_id = $property->getId();
        $tmp = $page_type_e_mail->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('19');
            $tmp->setRequired(false);
            $page_type_e_mail->addPropertyItem($tmp);
        }
        $this->pageTypesManager()->savePageType($page_type_e_mail);

        if ($this->pageTypeExists('settings')) {
            $this->addPageTypeSuperiorPageType('attachment', 'settings');
        }
        if ($this->pageTypeExists('settings')) {
            $this->addPageTypeSuperiorPageType('e_mail', 'settings');
        }
        if ($this->pageTypeExists('folder')) {
            $this->addPageTypeSuperiorPageType('e_mail', 'folder');
        }
        if ($this->pageTypeExists('service_page')) {
            $this->addPageTypeSuperiorPageType('e_mail', 'service_page');
        }

        // set template on MAIN PAGE index::error404
        $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('e_mail'), 'index', 'error404');
        $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('attachment'), 'index', 'error404');

        // regenerate page types constants
        $this->pageTypesManager()->generateConstants();

        // regenerate property constants
        $this->propertyManager()->generateConstants();

    }

    public function down() {
        // remove page type: E-mail (e_mail)
        $page_type_e_mail = $this->pageTypesManager()->pageTypeExistsByTag('e_mail');
        if (($page_type_e_mail != false) && (is_null($this->getDataKey('page_type_e_mail_existed')))) {
            $this->pageTypesManager()->removePageType($page_type_e_mail);
        }

        // remove property: Zoznam príloh(attachment_list)
        $property_attachment_list = $this->propertyManager()->propertyExistsByTag('attachment_list');
        if ($property_attachment_list != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_attachment_list);
            if ((is_null($this->getDataKey('property_attachment_list_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_attachment_list);
            }
        }

        // remove property: Správa HTML(email_body_html)
        $property_email_body_html = $this->propertyManager()->propertyExistsByTag('email_body_html');
        if ($property_email_body_html != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_email_body_html);
            if ((is_null($this->getDataKey('property_email_body_html_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_email_body_html);
            }
        }

        // remove property: Embedovať obrázky priamo do správy(mail_embed_images)
        $property_mail_embed_images = $this->propertyManager()->propertyExistsByTag('mail_embed_images');
        if ($property_mail_embed_images != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_mail_embed_images);
            if ((is_null($this->getDataKey('property_mail_embed_images_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_mail_embed_images);
            }
        }

        // remove property: Správa Text(email_body)
        $property_email_body = $this->propertyManager()->propertyExistsByTag('email_body');
        if ($property_email_body != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_email_body);
            if ((is_null($this->getDataKey('property_email_body_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_email_body);
            }
        }

        // remove property: Predmet(email_subject)
        $property_email_subject = $this->propertyManager()->propertyExistsByTag('email_subject');
        if ($property_email_subject != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_email_subject);
            if ((is_null($this->getDataKey('property_email_subject_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_email_subject);
            }
        }

        // remove property: Príjemcovia(email_recipients)
        $property_email_recipients = $this->propertyManager()->propertyExistsByTag('email_recipients');
        if ($property_email_recipients != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_email_recipients);
            if ((is_null($this->getDataKey('property_email_recipients_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_email_recipients);
            }
        }

        // remove property: Odosielateľ(email_sender)
        $property_email_sender = $this->propertyManager()->propertyExistsByTag('email_sender');
        if ($property_email_sender != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_email_sender);
            if ((is_null($this->getDataKey('property_email_sender_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_email_sender);
            }
        }

        // remove page type: Príloha (attachment)
        $page_type_attachment = $this->pageTypesManager()->pageTypeExistsByTag('attachment');
        if (($page_type_attachment != false) && (is_null($this->getDataKey('page_type_attachment_existed')))) {
            $this->pageTypesManager()->removePageType($page_type_attachment);
        }

        // remove property: Súbor(file)
        $property_file = $this->propertyManager()->propertyExistsByTag('file');
        if ($property_file != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_file);
            if ((is_null($this->getDataKey('property_file_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_file);
            }
        }

        // remove property: Titulok(title)
        $property_title = $this->propertyManager()->propertyExistsByTag('title');
        if ($property_title != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_title);
            if ((is_null($this->getDataKey('property_title_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_title);
            }
        }

        // regenerate page types constants
        $this->pageTypesManager()->generateConstants();

        // regenerate property constants
        $this->propertyManager()->generateConstants();

    }

}
