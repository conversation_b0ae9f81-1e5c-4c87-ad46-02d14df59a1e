<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;
use Buxus\PageType\PageType;
use Buxus\PageType\PageTypePropertyItem;

/**
 * Automatic generation from (buxus_babytrend) at 2017-07-13 17:38:30
 * PageType generator: page_type=formular
 */

class CreateFormularPageType extends AbstractMigration
{
	public function up()
	{
		// property: Titulok(title)
		$property_title = $this->propertyManager()->propertyExistsByTag('title');
		if ($property_title === false) {
			$property_title = new Property();
			$property_title->setTag('title');
			$property_title->setDescription('Štandardný názov stránky.
Podľa SEO odporúčaní max. 65 znakov.');
			$property_title->setExtendedDescription('');
			$property_title->setName('Titulok');
			$property_title->setClassId('4');
			$property_title->setShowType(NULL);
			$property_title->setShowTypeTag('text');
			$property_title->setValueType('oneline_text');
			$property_title->setDefaultValue('');
			$property_title->setMultiOperations(false);
			$property_title->setInputString(NULL);
			$property_title->setAttribute('tab', '');
			$property_title->setAttribute('size', '60');
			$property_title->setAttribute('maxlength', '');
			$property_title->setAttribute('readonly', 'F');
			$property_title->setAttribute('pattern', '');
			$property_title->setAttribute('inherit_value', 'F');
			$property_title->setAttribute('onchange-js', '');
			$property_title->setAttribute('onkeyup-js', '');
			$property_title->setAttribute('onkeydown-js', '');
			$this->propertyManager()->saveProperty($property_title);
		} else {
			$this->writeLine('Property with tag title already exists');
			$this->setDataKey('property_title_existed', true);
		}

		// property: Text(text)
		$property_text = $this->propertyManager()->propertyExistsByTag('text');
		if ($property_text === false) {
			$property_text = new Property();
			$property_text->setTag('text');
			$property_text->setDescription('Štandardný text stránky.');
			$property_text->setExtendedDescription('');
			$property_text->setName('Text');
			$property_text->setClassId('4');
			$property_text->setShowType(NULL);
			$property_text->setShowTypeTag('textarea');
			$property_text->setValueType('multiline_text');
			$property_text->setDefaultValue('');
			$property_text->setMultiOperations(false);
			$property_text->setInputString(NULL);
			$property_text->setAttribute('tab', '');
			$property_text->setAttribute('cols', '60');
			$property_text->setAttribute('rows', '');
			$property_text->setAttribute('dhtml-edit', '1');
			$property_text->setAttribute('dhtml-configuration', 'full');
			$property_text->setAttribute('import-word', '0');
			$property_text->setAttribute('auto', '1');
			$property_text->setAttribute('inherit_value', 'F');
			$property_text->setAttribute('onchange-js', '');
			$property_text->setAttribute('onkeyup-js', '');
			$property_text->setAttribute('onkeydown-js', '');
			$property_text->setAttribute('pattern', '');
			$this->propertyManager()->saveProperty($property_text);
		} else {
			$this->writeLine('Property with tag text already exists');
			$this->setDataKey('property_text_existed', true);
		}

		// property: Text po odoslaní(text_after_submit)
		$property_text_after_submit = $this->propertyManager()->propertyExistsByTag('text_after_submit');
		if ($property_text_after_submit === false) {
			$property_text_after_submit = new Property();
			$property_text_after_submit->setTag('text_after_submit');
			$property_text_after_submit->setDescription('Text, ktorý sa zobrazí po úspešnom odoslaní formulára');
			$property_text_after_submit->setExtendedDescription(NULL);
			$property_text_after_submit->setName('Text po odoslaní');
			$property_text_after_submit->setClassId('4');
			$property_text_after_submit->setShowType(NULL);
			$property_text_after_submit->setShowTypeTag('textarea');
			$property_text_after_submit->setValueType('multiline_text');
			$property_text_after_submit->setDefaultValue('');
			$property_text_after_submit->setMultiOperations(false);
			$property_text_after_submit->setInputString(NULL);
			$property_text_after_submit->setAttribute('tab', '');
			$property_text_after_submit->setAttribute('cols', '60');
			$property_text_after_submit->setAttribute('rows', '3');
			$property_text_after_submit->setAttribute('dhtml-edit', '1');
			$property_text_after_submit->setAttribute('dhtml-configuration', 'full');
			$property_text_after_submit->setAttribute('import-word', '0');
			$property_text_after_submit->setAttribute('auto', '');
			$property_text_after_submit->setAttribute('inherit_value', 'F');
			$property_text_after_submit->setAttribute('onchange-js', '');
			$property_text_after_submit->setAttribute('onkeyup-js', '');
			$property_text_after_submit->setAttribute('onkeydown-js', '');
			$property_text_after_submit->setAttribute('pattern', '');
			$this->propertyManager()->saveProperty($property_text_after_submit);
		} else {
			$this->writeLine('Property with tag text_after_submit already exists');
			$this->setDataKey('property_text_after_submit_existed', true);
		}

		// property: Ikona po odoslaní(icon_after_submit)
		$property_icon_after_submit = $this->propertyManager()->propertyExistsByTag('icon_after_submit');
		if ($property_icon_after_submit === false) {
			$property_icon_after_submit = new Property();
			$property_icon_after_submit->setTag('icon_after_submit');
			$property_icon_after_submit->setDescription('CSS trieda ikony, ktorá sa zobrazí po odoslaní formuláru');
			$property_icon_after_submit->setExtendedDescription('');
			$property_icon_after_submit->setName('Ikona po odoslaní');
			$property_icon_after_submit->setClassId('4');
			$property_icon_after_submit->setShowType(NULL);
			$property_icon_after_submit->setShowTypeTag('text');
			$property_icon_after_submit->setValueType('oneline_text');
			$property_icon_after_submit->setDefaultValue('');
			$property_icon_after_submit->setMultiOperations(false);
			$property_icon_after_submit->setInputString('');
			$property_icon_after_submit->setAttribute('tab', '');
			$property_icon_after_submit->setAttribute('size', '25');
			$property_icon_after_submit->setAttribute('maxlength', '');
			$property_icon_after_submit->setAttribute('readonly', 'F');
			$property_icon_after_submit->setAttribute('pattern', '');
			$property_icon_after_submit->setAttribute('inherit_value', 'F');
			$property_icon_after_submit->setAttribute('onchange-js', '');
			$property_icon_after_submit->setAttribute('onkeyup-js', '');
			$property_icon_after_submit->setAttribute('onkeydown-js', '');
			$this->propertyManager()->saveProperty($property_icon_after_submit);
		} else {
			$this->writeLine('Property with tag icon_after_submit already exists');
			$this->setDataKey('property_icon_after_submit_existed', true);
		}

		// page type: Formulár (formular)
		$page_type_formular = $this->pageTypesManager()->pageTypeExistsByTag('formular');
		if ($page_type_formular === false) {
			$page_type_formular = new PageType();
			$page_type_formular->setTag('formular');
			$page_type_formular->setName('Formulár');
			$page_type_formular->setPageClassId('1');
			$page_type_formular->setDefaultTemplateId('2');
			$page_type_formular->setDeleteTrigger('');
			$page_type_formular->setIncludeInSync('0');
			$page_type_formular->setPageDetailsLayout('');
			$page_type_formular->setPageSortTypeTag('sort_date_time');
			$page_type_formular->setPageTypeOrder(NULL);
			$page_type_formular->setPostmoveTrigger('');
			$page_type_formular->setPostsubmitTrigger('');
			$page_type_formular->setPresubmitTrigger('');
			$page_type_formular->setParent(NULL);
		} else {
			$this->writeLine('Page type with tag formular already exists');
			$this->setDataKey('page_type_formular_existed', true);
		}
		$property = $this->propertyManager()->getPropertyByTag('title');
		$property_id = $property->getId();
		$tmp = $page_type_formular->getPropertyItemForPropertyId($property_id);
		if ($tmp === null) {
			$tmp = new PageTypePropertyItem($property);
			$tmp->setOrder('1');
			$tmp->setRequired(false);
			$page_type_formular->addPropertyItem($tmp);
		}
		$property = $this->propertyManager()->getPropertyByTag('text');
		$property_id = $property->getId();
		$tmp = $page_type_formular->getPropertyItemForPropertyId($property_id);
		if ($tmp === null) {
			$tmp = new PageTypePropertyItem($property);
			$tmp->setOrder('2');
			$tmp->setRequired(false);
			$page_type_formular->addPropertyItem($tmp);
		}
		$property = $this->propertyManager()->getPropertyByTag('text_after_submit');
		$property_id = $property->getId();
		$tmp = $page_type_formular->getPropertyItemForPropertyId($property_id);
		if ($tmp === null) {
			$tmp = new PageTypePropertyItem($property);
			$tmp->setOrder('3');
			$tmp->setRequired(false);
			$page_type_formular->addPropertyItem($tmp);
		}
		$property = $this->propertyManager()->getPropertyByTag('icon_after_submit');
		$property_id = $property->getId();
		$tmp = $page_type_formular->getPropertyItemForPropertyId($property_id);
		if ($tmp === null) {
			$tmp = new PageTypePropertyItem($property);
			$tmp->setOrder('4');
			$tmp->setRequired(false);
			$page_type_formular->addPropertyItem($tmp);
		}
		$this->pageTypesManager()->savePageType($page_type_formular);


		// regenerate property constants
		$this->propertyManager()->generateConstants();

		// regenerate page types constants
		$this->pageTypesManager()->generateConstants();

	}

	public function down()
	{
		// remove page type: Formulár (formular)
		$page_type_formular = $this->pageTypesManager()->pageTypeExistsByTag('formular');
		if (($page_type_formular != false) && (is_null($this->getDataKey('page_type_formular_existed')))) {
			$this->pageTypesManager()->removePageType($page_type_formular);
		}

		// remove property: Ikona po odoslaní(icon_after_submit)
		$property_icon_after_submit = $this->propertyManager()->propertyExistsByTag('icon_after_submit');
		if ($property_icon_after_submit != false) {
			$supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_icon_after_submit);
			if ((is_null($this->getDataKey('property_icon_after_submit_existed'))) && (count($supported_page_types) == 0)) {
				$this->propertyManager()->removeProperty($property_icon_after_submit);
			}
		}

		// remove property: Text po odoslaní(text_after_submit)
		$property_text_after_submit = $this->propertyManager()->propertyExistsByTag('text_after_submit');
		if ($property_text_after_submit != false) {
			$supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_text_after_submit);
			if ((is_null($this->getDataKey('property_text_after_submit_existed'))) && (count($supported_page_types) == 0)) {
				$this->propertyManager()->removeProperty($property_text_after_submit);
			}
		}

		// remove property: Text(text)
		$property_text = $this->propertyManager()->propertyExistsByTag('text');
		if ($property_text != false) {
			$supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_text);
			if ((is_null($this->getDataKey('property_text_existed'))) && (count($supported_page_types) == 0)) {
				$this->propertyManager()->removeProperty($property_text);
			}
		}

		// remove property: Titulok(title)
		$property_title = $this->propertyManager()->propertyExistsByTag('title');
		if ($property_title != false) {
			$supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_title);
			if ((is_null($this->getDataKey('property_title_existed'))) && (count($supported_page_types) == 0)) {
				$this->propertyManager()->removeProperty($property_title);
			}
		}

		// regenerate property constants
		$this->propertyManager()->generateConstants();

		// regenerate page types constants
		$this->pageTypesManager()->generateConstants();

	}

}
