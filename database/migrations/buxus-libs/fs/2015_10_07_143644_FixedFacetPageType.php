<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;
use Buxus\PageType\PageType;
use Buxus\PageType\PageTypePropertyItem;

class FixedFacetPageType extends AbstractMigration
{
    public function up()
    {
        // property: Titulok(title)
        $property_title = $this->propertyManager()->propertyExistsByTag('title');
        if ($property_title === false) {
            $property_title = new Property();
            $property_title->setTag('title');
            $property_title->setDescription('Štandardný názov stránky. Text, ktorý sa zobrazí v záložke prehliadača. Podľa SEO odporúčaní max. 65 znakov.');
            $property_title->setExtendedDescription('');
            $property_title->setName('Titulok');
            $property_title->setClassId(4);
            $property_title->setShowType(NULL);
            $property_title->setShowTypeTag('text');
            $property_title->setValueType('oneline_text');
            $property_title->setDefaultValue('');
            $property_title->setMultiOperations(false);
            $property_title->setInputString(NULL);
            $property_title->setAttribute('tab', '');
            $property_title->setAttribute('size', '60');
            $property_title->setAttribute('maxlength', '');
            $property_title->setAttribute('readonly', 'F');
            $property_title->setAttribute('pattern', '');
            $property_title->setAttribute('inherit_value', 'F');
            $property_title->setAttribute('onchange-js', '');
            $property_title->setAttribute('onkeyup-js', '');
            $property_title->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($property_title);
        } else {
            $this->writeLine('Property with tag title already exists');
            $this->setDataKey('property_title_existed', true);
        }

        // property: Inštancia fazetu(fs_instance)
        $property_fs_instance = $this->propertyManager()->propertyExistsByTag('fs_instance');
        if ($property_fs_instance === false) {
            $property_fs_instance = new Property();
            $property_fs_instance->setTag('fs_instance');
            $property_fs_instance->setDescription('Tag inštancia fazetu podľa konfiguračného súboru');
            $property_fs_instance->setExtendedDescription('');
            $property_fs_instance->setName('Inštancia fazetu');
            $property_fs_instance->setClassId(4);
            $property_fs_instance->setShowType(NULL);
            $property_fs_instance->setShowTypeTag('text');
            $property_fs_instance->setValueType('oneline_text');
            $property_fs_instance->setDefaultValue('');
            $property_fs_instance->setMultiOperations(false);
            $property_fs_instance->setInputString('');
            $property_fs_instance->setAttribute('tab', '');
            $property_fs_instance->setAttribute('size', 60);
            $property_fs_instance->setAttribute('maxlength', '');
            $property_fs_instance->setAttribute('readonly', '0');
            $property_fs_instance->setAttribute('pattern', '');
            $property_fs_instance->setAttribute('inherit_value', '0');
            $property_fs_instance->setAttribute('onchange-js', '');
            $property_fs_instance->setAttribute('onkeyup-js', '');
            $property_fs_instance->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($property_fs_instance);
        } else {
            $this->writeLine('Property with tag fs_instance already exists');
            $this->setDataKey('property_fs_instance_existed', true);
        }

        // property: Fixovaná hodnota(fixed_value)
        $property_fixed_value = $this->propertyManager()->propertyExistsByTag('fixed_value');
        if ($property_fixed_value === false) {
            $property_fixed_value = new Property();
            $property_fixed_value->setTag('fixed_value');
            $property_fixed_value->setDescription('Hodnota nastavenia fazetu');
            $property_fixed_value->setExtendedDescription('');
            $property_fixed_value->setName('Fixovaná hodnota');
            $property_fixed_value->setClassId(4);
            $property_fixed_value->setShowType(NULL);
            $property_fixed_value->setShowTypeTag('text');
            $property_fixed_value->setValueType('oneline_text');
            $property_fixed_value->setDefaultValue('');
            $property_fixed_value->setMultiOperations(false);
            $property_fixed_value->setInputString('');
            $property_fixed_value->setAttribute('tab', '');
            $property_fixed_value->setAttribute('size', 60);
            $property_fixed_value->setAttribute('maxlength', '');
            $property_fixed_value->setAttribute('readonly', '0');
            $property_fixed_value->setAttribute('pattern', '');
            $property_fixed_value->setAttribute('inherit_value', '0');
            $property_fixed_value->setAttribute('onchange-js', '');
            $property_fixed_value->setAttribute('onkeyup-js', '');
            $property_fixed_value->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($property_fixed_value);
        } else {
            $this->writeLine('Property with tag fixed_value already exists');
            $this->setDataKey('property_fixed_value_existed', true);
        }

        // property: Fixovaná vlastnosť fazetu(fixed_tag)
        $property_fixed_tag = $this->propertyManager()->propertyExistsByTag('fixed_tag');
        if ($property_fixed_tag === false) {
            $property_fixed_tag = new Property();
            $property_fixed_tag->setTag('fixed_tag');
            $property_fixed_tag->setDescription('Fixovaná vlastnosť fazet');
            $property_fixed_tag->setExtendedDescription('');
            $property_fixed_tag->setName('Fixovaná vlastnosť fazetu');
            $property_fixed_tag->setClassId(4);
            $property_fixed_tag->setShowType(NULL);
            $property_fixed_tag->setShowTypeTag('text');
            $property_fixed_tag->setValueType('oneline_text');
            $property_fixed_tag->setDefaultValue('');
            $property_fixed_tag->setMultiOperations(false);
            $property_fixed_tag->setInputString('');
            $property_fixed_tag->setAttribute('tab', '');
            $property_fixed_tag->setAttribute('size', 60);
            $property_fixed_tag->setAttribute('maxlength', '');
            $property_fixed_tag->setAttribute('readonly', '0');
            $property_fixed_tag->setAttribute('pattern', '');
            $property_fixed_tag->setAttribute('inherit_value', '0');
            $property_fixed_tag->setAttribute('onchange-js', '');
            $property_fixed_tag->setAttribute('onkeyup-js', '');
            $property_fixed_tag->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($property_fixed_tag);
        } else {
            $this->writeLine('Property with tag fixed_tag already exists');
            $this->setDataKey('property_fixed_tag_existed', true);
        }

        // property: FS cesta(fixed_path)
        $property_fixed_path = $this->propertyManager()->propertyExistsByTag('fixed_path');
        if ($property_fixed_path === false) {
            $property_fixed_path = new Property();
            $property_fixed_path->setTag('fixed_path');
            $property_fixed_path->setDescription('Unikátna cesta definijúca fixnovaný fazet');
            $property_fixed_path->setExtendedDescription('');
            $property_fixed_path->setName('FS cesta');
            $property_fixed_path->setClassId(4);
            $property_fixed_path->setShowType(NULL);
            $property_fixed_path->setShowTypeTag('text');
            $property_fixed_path->setValueType('oneline_text');
            $property_fixed_path->setDefaultValue('');
            $property_fixed_path->setMultiOperations(false);
            $property_fixed_path->setInputString('');
            $property_fixed_path->setAttribute('tab', '');
            $property_fixed_path->setAttribute('size', 60);
            $property_fixed_path->setAttribute('maxlength', '');
            $property_fixed_path->setAttribute('readonly', '0');
            $property_fixed_path->setAttribute('pattern', '');
            $property_fixed_path->setAttribute('inherit_value', '0');
            $property_fixed_path->setAttribute('onchange-js', '');
            $property_fixed_path->setAttribute('onkeyup-js', '');
            $property_fixed_path->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($property_fixed_path);
        } else {
            $this->writeLine('Property with tag fixed_path already exists');
            $this->setDataKey('property_fixed_path_existed', true);
        }

        // property: Text(text)
        $property_text = $this->propertyManager()->propertyExistsByTag('text');
        if ($property_text === false) {
            $property_text = new Property();
            $property_text->setTag('text');
            $property_text->setDescription('Štandardný text stránky.');
            $property_text->setExtendedDescription('');
            $property_text->setName('Text');
            $property_text->setClassId(4);
            $property_text->setShowType(NULL);
            $property_text->setShowTypeTag('textarea');
            $property_text->setValueType('multiline_text');
            $property_text->setDefaultValue('');
            $property_text->setMultiOperations(false);
            $property_text->setInputString(NULL);
            $property_text->setAttribute('tab', '');
            $property_text->setAttribute('cols', '60');
            $property_text->setAttribute('rows', '');
            $property_text->setAttribute('dhtml-edit', '1');
            $property_text->setAttribute('dhtml-configuration', 'full');
            $property_text->setAttribute('import-word', '0');
            $property_text->setAttribute('auto', '1');
            $property_text->setAttribute('inherit_value', 'F');
            $property_text->setAttribute('onchange-js', '');
            $property_text->setAttribute('onkeyup-js', '');
            $property_text->setAttribute('onkeydown-js', '');
            $property_text->setAttribute('pattern', '');
            $this->propertyManager()->saveProperty($property_text);
        } else {
            $this->writeLine('Property with tag text already exists');
            $this->setDataKey('property_text_existed', true);
        }

        // property: SEO URL name(seo_url_name)
        $property_seo_url_name = $this->propertyManager()->propertyExistsByTag('seo_url_name');
        if ($property_seo_url_name === false) {
            $property_seo_url_name = new Property();
            $property_seo_url_name->setTag('seo_url_name');
            $property_seo_url_name->setDescription('Podľa SEO odporúčaní max. 35 znakov.');
            $property_seo_url_name->setExtendedDescription('');
            $property_seo_url_name->setName('SEO URL name');
            $property_seo_url_name->setClassId(4);
            $property_seo_url_name->setShowType(NULL);
            $property_seo_url_name->setShowTypeTag('seo_url_name');
            $property_seo_url_name->setValueType('seo_url_name');
            $property_seo_url_name->setDefaultValue('');
            $property_seo_url_name->setMultiOperations(false);
            $property_seo_url_name->setInputString(NULL);
            $property_seo_url_name->setAttribute('tab', 'SEO');
            $property_seo_url_name->setAttribute('size', '80');
            $property_seo_url_name->setAttribute('onchange-js', '');
            $property_seo_url_name->setAttribute('onkeyup-js', '');
            $property_seo_url_name->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($property_seo_url_name);
        } else {
            $this->writeLine('Property with tag seo_url_name already exists');
            $this->setDataKey('property_seo_url_name_existed', true);
        }

        // property: Fixovaná hodnota textová(fixed_value_text)
        $property_fixed_value_text = $this->propertyManager()->propertyExistsByTag('fixed_value_text');
        if ($property_fixed_value_text === false) {
            $property_fixed_value_text = new Property();
            $property_fixed_value_text->setTag('fixed_value_text');
            $property_fixed_value_text->setDescription('Čitateľná verzia fixovanej hodnoty.');
            $property_fixed_value_text->setExtendedDescription('');
            $property_fixed_value_text->setName('Fixovaná hodnota textová');
            $property_fixed_value_text->setClassId(4);
            $property_fixed_value_text->setShowType(NULL);
            $property_fixed_value_text->setShowTypeTag('text');
            $property_fixed_value_text->setValueType('oneline_text');
            $property_fixed_value_text->setDefaultValue('');
            $property_fixed_value_text->setMultiOperations(false);
            $property_fixed_value_text->setInputString('');
            $property_fixed_value_text->setAttribute('tab', '');
            $property_fixed_value_text->setAttribute('size', '60');
            $property_fixed_value_text->setAttribute('maxlength', '');
            $property_fixed_value_text->setAttribute('readonly', 'F');
            $property_fixed_value_text->setAttribute('pattern', '');
            $property_fixed_value_text->setAttribute('inherit_value', 'F');
            $property_fixed_value_text->setAttribute('onchange-js', '');
            $property_fixed_value_text->setAttribute('onkeyup-js', '');
            $property_fixed_value_text->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($property_fixed_value_text);
        } else {
            $this->writeLine('Property with tag fixed_value_text already exists');
            $this->setDataKey('property_fixed_value_text_existed', true);
        }

        // page type: Fixovaný fazet (fixed_facet)
        $page_type_fixed_facet = $this->pageTypesManager()->pageTypeExistsByTag('fixed_facet');
        if ($page_type_fixed_facet === false) {
            $page_type_fixed_facet = new PageType();
            $page_type_fixed_facet->setTag('fixed_facet');
            $page_type_fixed_facet->setName('Fixovaný fazet');
            $page_type_fixed_facet->setPageClassId(1);
            $page_type_fixed_facet->setDefaultTemplateId(2);
            $page_type_fixed_facet->setDeleteTrigger(NULL);
            $page_type_fixed_facet->setIncludeInSync(NULL);
            $page_type_fixed_facet->setPageDetailsLayout(NULL);
            $page_type_fixed_facet->setPageSortTypeTag(NULL);
            $page_type_fixed_facet->setPageTypeOrder(NULL);
            $page_type_fixed_facet->setPostmoveTrigger(NULL);
            $page_type_fixed_facet->setPostsubmitTrigger(NULL);
            $page_type_fixed_facet->setPresubmitTrigger(NULL);
            $page_type_fixed_facet->setParent(NULL);
        } else {
            $this->writeLine('Page type with tag fixed_facet already exists');
            $this->setDataKey('page_type_fixed_facet_existed', true);
        }
        $property = $this->propertyManager()->getPropertyByTag('title');
        $property_id = $property->getId();
        $tmp = $page_type_fixed_facet->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder(1);
            $tmp->setRequired(false);
            $page_type_fixed_facet->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('fs_instance');
        $property_id = $property->getId();
        $tmp = $page_type_fixed_facet->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder(2);
            $tmp->setRequired(false);
            $page_type_fixed_facet->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('fixed_value');
        $property_id = $property->getId();
        $tmp = $page_type_fixed_facet->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder(3);
            $tmp->setRequired(false);
            $page_type_fixed_facet->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('fixed_tag');
        $property_id = $property->getId();
        $tmp = $page_type_fixed_facet->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder(4);
            $tmp->setRequired(false);
            $page_type_fixed_facet->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('fixed_path');
        $property_id = $property->getId();
        $tmp = $page_type_fixed_facet->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder(5);
            $tmp->setRequired(false);
            $page_type_fixed_facet->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('text');
        $property_id = $property->getId();
        $tmp = $page_type_fixed_facet->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder(6);
            $tmp->setRequired(false);
            $page_type_fixed_facet->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('seo_url_name');
        $property_id = $property->getId();
        $tmp = $page_type_fixed_facet->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder(7);
            $tmp->setRequired(false);
            $page_type_fixed_facet->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('fixed_value_text');
        $property_id = $property->getId();
        $tmp = $page_type_fixed_facet->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder(8);
            $tmp->setRequired(false);
            $page_type_fixed_facet->addPropertyItem($tmp);
        }
        $this->pageTypesManager()->savePageType($page_type_fixed_facet);

        if ($this->pageTypeExists('section')) {
            $this->addPageTypeSuperiorPageType('fixed_facet', 'section');
        }

        // regenerate property constants
        $this->propertyManager()->generateConstants();

        // regenerate page types constants
        $this->pageTypesManager()->generateConstants();

    }

    public function down()
    {
        // remove page type: Fixovaný fazet (fixed_facet)
        $page_type_fixed_facet = $this->pageTypesManager()->pageTypeExistsByTag('fixed_facet');
        if (($page_type_fixed_facet != false) && (is_null($this->getDataKey('page_type_fixed_facet_existed')))) {
            $this->pageTypesManager()->removePageType($page_type_fixed_facet);
        }

        // remove property: Fixovaná hodnota textová(fixed_value_text)
        $property_fixed_value_text = $this->propertyManager()->propertyExistsByTag('fixed_value_text');
        if ($property_fixed_value_text != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_fixed_value_text);
            if ((is_null($this->getDataKey('property_fixed_value_text_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_fixed_value_text);
            }
        }

        // remove property: SEO URL name(seo_url_name)
        $property_seo_url_name = $this->propertyManager()->propertyExistsByTag('seo_url_name');
        if ($property_seo_url_name != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_seo_url_name);
            if ((is_null($this->getDataKey('property_seo_url_name_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_seo_url_name);
            }
        }

        // remove property: Text(text)
        $property_text = $this->propertyManager()->propertyExistsByTag('text');
        if ($property_text != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_text);
            if ((is_null($this->getDataKey('property_text_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_text);
            }
        }

        // remove property: FS cesta(fixed_path)
        $property_fixed_path = $this->propertyManager()->propertyExistsByTag('fixed_path');
        if ($property_fixed_path != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_fixed_path);
            if ((is_null($this->getDataKey('property_fixed_path_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_fixed_path);
            }
        }

        // remove property: Fixovaná vlastnosť fazetu(fixed_tag)
        $property_fixed_tag = $this->propertyManager()->propertyExistsByTag('fixed_tag');
        if ($property_fixed_tag != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_fixed_tag);
            if ((is_null($this->getDataKey('property_fixed_tag_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_fixed_tag);
            }
        }

        // remove property: Fixovaná hodnota(fixed_value)
        $property_fixed_value = $this->propertyManager()->propertyExistsByTag('fixed_value');
        if ($property_fixed_value != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_fixed_value);
            if ((is_null($this->getDataKey('property_fixed_value_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_fixed_value);
            }
        }

        // remove property: Inštancia fazetu(fs_instance)
        $property_fs_instance = $this->propertyManager()->propertyExistsByTag('fs_instance');
        if ($property_fs_instance != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_fs_instance);
            if ((is_null($this->getDataKey('property_fs_instance_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_fs_instance);
            }
        }

        // remove property: Titulok(title)
        $property_title = $this->propertyManager()->propertyExistsByTag('title');
        if ($property_title != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_title);
            if ((is_null($this->getDataKey('property_title_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_title);
            }
        }

        // regenerate property constants
        $this->propertyManager()->generateConstants();

        // regenerate page types constants
        $this->pageTypesManager()->generateConstants();

    }
}
