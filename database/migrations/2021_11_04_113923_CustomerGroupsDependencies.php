<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;
use Buxus\PageType\PageType;
use Buxus\PageType\PageTypePropertyItem;

/**
 * Automatic generation from (rinoparts) at 2021-11-04 11:39:22
 * PageType generator: page_type=customer_groups_settings
 * Page generator: page_id=371615
 * Property generator: property=competition_charge,end_customer_charge,small_vendor_charge,car_service_charge
 */
class CustomerGroupsDependencies extends AbstractMigration
{
    public function up()
    {
        // property: Autoservis - prirážka [%](car_service_charge)
        $propertyCarServiceCharge = $this->propertyManager()->propertyExistsByTag('car_service_charge');
        if ($propertyCarServiceCharge === false) {
            $propertyCarServiceCharge = new Property();
            $propertyCarServiceCharge->setTag('car_service_charge');
            $propertyCarServiceCharge->setDescription('');
            $propertyCarServiceCharge->setExtendedDescription('');
            $propertyCarServiceCharge->setName('Autoservis - prir<PERSON><PERSON>ka [%]');
            $propertyCarServiceCharge->setClassId(4);
            $propertyCarServiceCharge->setShowType(null);
            $propertyCarServiceCharge->setShowTypeTag('number');
            $propertyCarServiceCharge->setValueType('number');
            $propertyCarServiceCharge->setDefaultValue('0');
            $propertyCarServiceCharge->setMultiOperations(false);
            $propertyCarServiceCharge->setInputString('');
            $propertyCarServiceCharge->setAttribute('tab', '');
            $propertyCarServiceCharge->setAttribute('size', '10');
            $propertyCarServiceCharge->setAttribute('min', '0');
            $propertyCarServiceCharge->setAttribute('max', '');
            $propertyCarServiceCharge->setAttribute('step', '0,1');
            $propertyCarServiceCharge->setAttribute('inherit_value', 'F');
            $propertyCarServiceCharge->setAttribute('onchange-js', '');
            $propertyCarServiceCharge->setAttribute('onkeyup-js', '');
            $propertyCarServiceCharge->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyCarServiceCharge);
        } else {
            $this->writeLine('Property with tag car_service_charge already exists');
            $this->setDataKey('property_car_service_charge_existed', true);
        }
        if ($this->pageTypeExists('customer_groups_settings')) {
            $this->addPropertyToPageType('car_service_charge', 'customer_groups_settings', false);
        }

        // property: Drobný predajca - prirážka [%](small_vendor_charge)
        $propertySmallVendorCharge = $this->propertyManager()->propertyExistsByTag('small_vendor_charge');
        if ($propertySmallVendorCharge === false) {
            $propertySmallVendorCharge = new Property();
            $propertySmallVendorCharge->setTag('small_vendor_charge');
            $propertySmallVendorCharge->setDescription('');
            $propertySmallVendorCharge->setExtendedDescription('');
            $propertySmallVendorCharge->setName('Drobný predajca - prirážka [%]');
            $propertySmallVendorCharge->setClassId(4);
            $propertySmallVendorCharge->setShowType(null);
            $propertySmallVendorCharge->setShowTypeTag('number');
            $propertySmallVendorCharge->setValueType('number');
            $propertySmallVendorCharge->setDefaultValue('0');
            $propertySmallVendorCharge->setMultiOperations(false);
            $propertySmallVendorCharge->setInputString('');
            $propertySmallVendorCharge->setAttribute('tab', '');
            $propertySmallVendorCharge->setAttribute('size', '10');
            $propertySmallVendorCharge->setAttribute('min', '0');
            $propertySmallVendorCharge->setAttribute('max', '');
            $propertySmallVendorCharge->setAttribute('step', '0,1');
            $propertySmallVendorCharge->setAttribute('inherit_value', 'F');
            $propertySmallVendorCharge->setAttribute('onchange-js', '');
            $propertySmallVendorCharge->setAttribute('onkeyup-js', '');
            $propertySmallVendorCharge->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertySmallVendorCharge);
        } else {
            $this->writeLine('Property with tag small_vendor_charge already exists');
            $this->setDataKey('property_small_vendor_charge_existed', true);
        }
        if ($this->pageTypeExists('customer_groups_settings')) {
            $this->addPropertyToPageType('small_vendor_charge', 'customer_groups_settings', false);
        }

        // property: Koncový zákazník - prirážka [%](end_customer_charge)
        $propertyEndCustomerCharge = $this->propertyManager()->propertyExistsByTag('end_customer_charge');
        if ($propertyEndCustomerCharge === false) {
            $propertyEndCustomerCharge = new Property();
            $propertyEndCustomerCharge->setTag('end_customer_charge');
            $propertyEndCustomerCharge->setDescription('');
            $propertyEndCustomerCharge->setExtendedDescription('');
            $propertyEndCustomerCharge->setName('Koncový zákazník - prirážka [%]');
            $propertyEndCustomerCharge->setClassId(4);
            $propertyEndCustomerCharge->setShowType(null);
            $propertyEndCustomerCharge->setShowTypeTag('number');
            $propertyEndCustomerCharge->setValueType('number');
            $propertyEndCustomerCharge->setDefaultValue('0');
            $propertyEndCustomerCharge->setMultiOperations(false);
            $propertyEndCustomerCharge->setInputString('');
            $propertyEndCustomerCharge->setAttribute('tab', '');
            $propertyEndCustomerCharge->setAttribute('size', '10');
            $propertyEndCustomerCharge->setAttribute('min', '0');
            $propertyEndCustomerCharge->setAttribute('max', '');
            $propertyEndCustomerCharge->setAttribute('step', '0,1');
            $propertyEndCustomerCharge->setAttribute('inherit_value', 'F');
            $propertyEndCustomerCharge->setAttribute('onchange-js', '');
            $propertyEndCustomerCharge->setAttribute('onkeyup-js', '');
            $propertyEndCustomerCharge->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyEndCustomerCharge);
        } else {
            $this->writeLine('Property with tag end_customer_charge already exists');
            $this->setDataKey('property_end_customer_charge_existed', true);
        }
        if ($this->pageTypeExists('customer_groups_settings')) {
            $this->addPropertyToPageType('end_customer_charge', 'customer_groups_settings', false);
        }

        // property: Konkurencia - prirážka [%](competition_charge)
        $propertyCompetitionCharge = $this->propertyManager()->propertyExistsByTag('competition_charge');
        if ($propertyCompetitionCharge === false) {
            $propertyCompetitionCharge = new Property();
            $propertyCompetitionCharge->setTag('competition_charge');
            $propertyCompetitionCharge->setDescription('');
            $propertyCompetitionCharge->setExtendedDescription('');
            $propertyCompetitionCharge->setName('Konkurencia - prirážka [%]');
            $propertyCompetitionCharge->setClassId(4);
            $propertyCompetitionCharge->setShowType(null);
            $propertyCompetitionCharge->setShowTypeTag('number');
            $propertyCompetitionCharge->setValueType('number');
            $propertyCompetitionCharge->setDefaultValue('0');
            $propertyCompetitionCharge->setMultiOperations(false);
            $propertyCompetitionCharge->setInputString('');
            $propertyCompetitionCharge->setAttribute('tab', '');
            $propertyCompetitionCharge->setAttribute('size', '10');
            $propertyCompetitionCharge->setAttribute('min', '0');
            $propertyCompetitionCharge->setAttribute('max', '');
            $propertyCompetitionCharge->setAttribute('step', '0,1');
            $propertyCompetitionCharge->setAttribute('inherit_value', 'F');
            $propertyCompetitionCharge->setAttribute('onchange-js', '');
            $propertyCompetitionCharge->setAttribute('onkeyup-js', '');
            $propertyCompetitionCharge->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyCompetitionCharge);
        } else {
            $this->writeLine('Property with tag competition_charge already exists');
            $this->setDataKey('property_competition_charge_existed', true);
        }
        if ($this->pageTypeExists('customer_groups_settings')) {
            $this->addPropertyToPageType('competition_charge', 'customer_groups_settings', false);
        }

        // page type: Nastavenia zákazníckých skupín (customer_groups_settings)
        $pageTypeCustomerGroupsSettings = $this->pageTypesManager()->pageTypeExistsByTag('customer_groups_settings');
        if ($pageTypeCustomerGroupsSettings === false) {
            $pageTypeCustomerGroupsSettings = new PageType();
            $pageTypeCustomerGroupsSettings->setTag('customer_groups_settings');
            $pageTypeCustomerGroupsSettings->setName('Nastavenia zákazníckých skupín');
            $pageTypeCustomerGroupsSettings->setPageClassId(1);
            $pageTypeCustomerGroupsSettings->setDefaultTemplateId(1);
            $pageTypeCustomerGroupsSettings->setDeleteTrigger(null);
            $pageTypeCustomerGroupsSettings->setIncludeInSync(null);
            $pageTypeCustomerGroupsSettings->setPageDetailsLayout('');
            $pageTypeCustomerGroupsSettings->setPageSortTypeTag('sort_date_time');
            $pageTypeCustomerGroupsSettings->setPageTypeOrder(0);
            $pageTypeCustomerGroupsSettings->setPostmoveTrigger(null);
            $pageTypeCustomerGroupsSettings->setPostsubmitTrigger(null);
            $pageTypeCustomerGroupsSettings->setPresubmitTrigger(null);
            $pageTypeCustomerGroupsSettings->setParent(null);
        } else {
            $this->writeLine('Page type with tag customer_groups_settings already exists');
            $this->setDataKey('page_type_customer_groups_settings_existed', true);
        }
        $property = $this->propertyManager()->getPropertyByTag('car_service_charge');
        $propertyId = $property->getId();
        $tmp = $pageTypeCustomerGroupsSettings->getPropertyItemForPropertyId($propertyId);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder(1);
            $tmp->setRequired(false);
            $pageTypeCustomerGroupsSettings->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('small_vendor_charge');
        $propertyId = $property->getId();
        $tmp = $pageTypeCustomerGroupsSettings->getPropertyItemForPropertyId($propertyId);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder(2);
            $tmp->setRequired(false);
            $pageTypeCustomerGroupsSettings->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('end_customer_charge');
        $propertyId = $property->getId();
        $tmp = $pageTypeCustomerGroupsSettings->getPropertyItemForPropertyId($propertyId);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder(3);
            $tmp->setRequired(false);
            $pageTypeCustomerGroupsSettings->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('competition_charge');
        $propertyId = $property->getId();
        $tmp = $pageTypeCustomerGroupsSettings->getPropertyItemForPropertyId($propertyId);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder(4);
            $tmp->setRequired(false);
            $pageTypeCustomerGroupsSettings->addPropertyItem($tmp);
        }
        $this->pageTypesManager()->savePageType($pageTypeCustomerGroupsSettings);

        if ($this->pageTypeExists('settings')) {
            $this->addPageTypeSuperiorPageType('customer_groups_settings', 'settings');
        }

        // page: Nastavenia prirážok(ID: 371615 TAG: customer_groups_charges)
        $pageId = $this->getPageIdByTag('customer_groups_charges');
        if ($pageId === null) {
            $pageType = $this->pageTypesManager()->getPageTypeByTag('customer_groups_settings');
            $page371615 = \PageFactory::create($this->getPageIdByTag('Nastavenia e-shopu'), $pageType->getId());
        } else {
            $page371615 = \PageFactory::get($pageId);
        }
        $page371615->setPageName('Nastavenia prirážok');
        $page371615->setPageTag('customer_groups_charges');
        $page371615->setPageStateId('2');
        $page371615->setPageClassId(1);
        $page371615->setValue('car_service_charge', '1');
        $page371615->setValue('small_vendor_charge', '1');
        $page371615->setValue('end_customer_charge', '1');
        $page371615->setValue('competition_charge', '1');
        $page371615->save();
        // regenerate property constants
        $this->propertyManager()->generateConstants();

        // regenerate page types constants
        $this->pageTypesManager()->generateConstants();
    }

    public function down()
    {
        // remove page: Nastavenia prirážok (customer_groups_charges)
        $pageId = $this->getPageIdByTag('customer_groups_charges');
        if ($pageId !== null) {
            \PageFactory::get($pageId)->delete();
        }

        // remove page type: Nastavenia zákazníckých skupín (customer_groups_settings)
        $pageTypeCustomerGroupsSettings = $this->pageTypesManager()->pageTypeExistsByTag('customer_groups_settings');
        if (($pageTypeCustomerGroupsSettings != false) && (is_null($this->getDataKey('page_type_customer_groups_settings_existed')))) {
            $this->pageTypesManager()->removePageType($pageTypeCustomerGroupsSettings);
        }

        // remove property: Konkurencia - prirážka [%](competition_charge)
        $propertyCompetitionCharge = $this->propertyManager()->propertyExistsByTag('competition_charge');
        if (($propertyCompetitionCharge !== false) && ($this->getDataKey('property_competition_charge_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyCompetitionCharge);
        }

        // remove property: Koncový zákazník - prirážka [%](end_customer_charge)
        $propertyEndCustomerCharge = $this->propertyManager()->propertyExistsByTag('end_customer_charge');
        if (($propertyEndCustomerCharge !== false) && ($this->getDataKey('property_end_customer_charge_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyEndCustomerCharge);
        }

        // remove property: Drobný predajca - prirážka [%](small_vendor_charge)
        $propertySmallVendorCharge = $this->propertyManager()->propertyExistsByTag('small_vendor_charge');
        if (($propertySmallVendorCharge !== false) && ($this->getDataKey('property_small_vendor_charge_existed') === null)) {
            $this->propertyManager()->removeProperty($propertySmallVendorCharge);
        }

        // remove property: Autoservis - prirážka [%](car_service_charge)
        $propertyCarServiceCharge = $this->propertyManager()->propertyExistsByTag('car_service_charge');
        if (($propertyCarServiceCharge !== false) && ($this->getDataKey('property_car_service_charge_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyCarServiceCharge);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();

        // regenerate page types constants
        $this->pageTypesManager()->generateConstants();
    }
}
