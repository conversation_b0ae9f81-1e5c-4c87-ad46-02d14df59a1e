<?php

use Buxus\Migration\AbstractMigration;

/**
 * Automatic generation from (rinoparts) at 2022-12-14 13:39:14
 */
class OnixNsNumberFixesMigration extends AbstractMigration
{
    public function up()
    {
        $pageIds = ['642233', '795806', '795810'];

        foreach ($pageIds as $pageId) {
            $page = \PageFactory::get($pageId);
            if ($page instanceof \Buxus\Page\PageInterface) {
                if ($page->hasProperty(\Buxus\Util\PropertyTag::ONIX_NS_NUMBER_TAG())) {
                    $page->setValue(\Buxus\Util\PropertyTag::ONIX_NS_NUMBER_TAG(), null);
                    $page->save();
                }
            }
        }
    }

    public function down()
    {
    }
}
