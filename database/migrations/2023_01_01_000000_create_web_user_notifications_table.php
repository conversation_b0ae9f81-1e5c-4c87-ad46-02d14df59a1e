<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateWebUserNotificationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tblWebUserNotifications', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('webuser_id');
            $table->integer('notification_type');
            $table->unsignedBigInteger('notification_item_id');
            $table->timestamps();

            $table->index(['webuser_id', 'notification_type']);
            $table->index(['webuser_id', 'notification_item_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tblWebUserNotifications');
    }
}
