<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;

/**
 * Automatic generation from (rinoparts) at 2024-08-21 13:06:26
 * Property generator: property=meat_doria_supplier_code,meat_doria_price_without_vat,meat_doria_stock_balance,meat_doria_oe_number,meat_doria_oe_numbers,meat_doria_latest_import
 */
class MeatDoriaPropertiesMigration extends AbstractMigration
{
    public function up()
    {
        // property: MeatDoria - Dodávateľský kód(meat_doria_supplier_code)
        $propertyMeatDoriaSupplierCode = $this->propertyManager()->propertyExistsByTag('meat_doria_supplier_code');
        if ($propertyMeatDoriaSupplierCode === false) {
            $propertyMeatDoriaSupplierCode = new Property();
            $propertyMeatDoriaSupplierCode->setTag('meat_doria_supplier_code');
            $propertyMeatDoriaSupplierCode->setDescription('');
            $propertyMeatDoriaSupplierCode->setExtendedDescription('');
            $propertyMeatDoriaSupplierCode->setName('MeatDoria - Dodávateľský kód');
            $propertyMeatDoriaSupplierCode->setClassId(4);
            $propertyMeatDoriaSupplierCode->setShowType(null);
            $propertyMeatDoriaSupplierCode->setShowTypeTag('text');
            $propertyMeatDoriaSupplierCode->setValueType('oneline_text');
            $propertyMeatDoriaSupplierCode->setDefaultValue('');
            $propertyMeatDoriaSupplierCode->setMultiOperations(false);
            $propertyMeatDoriaSupplierCode->setInputString('');
            $propertyMeatDoriaSupplierCode->setAttribute('tab', 'MeatDoria');
            $propertyMeatDoriaSupplierCode->setAttribute('size', '60');
            $propertyMeatDoriaSupplierCode->setAttribute('maxlength', '');
            $propertyMeatDoriaSupplierCode->setAttribute('readonly', 'F');
            $propertyMeatDoriaSupplierCode->setAttribute('pattern', '');
            $propertyMeatDoriaSupplierCode->setAttribute('inherit_value', 'F');
            $propertyMeatDoriaSupplierCode->setAttribute('onchange-js', '');
            $propertyMeatDoriaSupplierCode->setAttribute('onkeyup-js', '');
            $propertyMeatDoriaSupplierCode->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyMeatDoriaSupplierCode);
        } else {
            $this->writeLine('Property with tag meat_doria_supplier_code already exists');
            $this->setDataKey('property_meat_doria_supplier_code_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('meat_doria_supplier_code', 'eshop_product', false);
        }

        // property: MeatDoria - Cena bez DPH(meat_doria_price_without_vat)
        $propertyMeatDoriaPriceWithoutVat = $this->propertyManager()->propertyExistsByTag('meat_doria_price_without_vat');
        if ($propertyMeatDoriaPriceWithoutVat === false) {
            $propertyMeatDoriaPriceWithoutVat = new Property();
            $propertyMeatDoriaPriceWithoutVat->setTag('meat_doria_price_without_vat');
            $propertyMeatDoriaPriceWithoutVat->setDescription('');
            $propertyMeatDoriaPriceWithoutVat->setExtendedDescription('');
            $propertyMeatDoriaPriceWithoutVat->setName('MeatDoria - Cena bez DPH');
            $propertyMeatDoriaPriceWithoutVat->setClassId(4);
            $propertyMeatDoriaPriceWithoutVat->setShowType(null);
            $propertyMeatDoriaPriceWithoutVat->setShowTypeTag('text');
            $propertyMeatDoriaPriceWithoutVat->setValueType('oneline_text');
            $propertyMeatDoriaPriceWithoutVat->setDefaultValue('');
            $propertyMeatDoriaPriceWithoutVat->setMultiOperations(false);
            $propertyMeatDoriaPriceWithoutVat->setInputString('');
            $propertyMeatDoriaPriceWithoutVat->setAttribute('tab', 'MeatDoria');
            $propertyMeatDoriaPriceWithoutVat->setAttribute('size', '60');
            $propertyMeatDoriaPriceWithoutVat->setAttribute('maxlength', '');
            $propertyMeatDoriaPriceWithoutVat->setAttribute('readonly', 'F');
            $propertyMeatDoriaPriceWithoutVat->setAttribute('pattern', '');
            $propertyMeatDoriaPriceWithoutVat->setAttribute('inherit_value', 'F');
            $propertyMeatDoriaPriceWithoutVat->setAttribute('onchange-js', '');
            $propertyMeatDoriaPriceWithoutVat->setAttribute('onkeyup-js', '');
            $propertyMeatDoriaPriceWithoutVat->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyMeatDoriaPriceWithoutVat);
        } else {
            $this->writeLine('Property with tag meat_doria_price_without_vat already exists');
            $this->setDataKey('property_meat_doria_price_without_vat_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('meat_doria_price_without_vat', 'eshop_product', false);
        }

        // property: MeatDoria - Skladová zásoba(meat_doria_stock_balance)
        $propertyMeatDoriaStockBalance = $this->propertyManager()->propertyExistsByTag('meat_doria_stock_balance');
        if ($propertyMeatDoriaStockBalance === false) {
            $propertyMeatDoriaStockBalance = new Property();
            $propertyMeatDoriaStockBalance->setTag('meat_doria_stock_balance');
            $propertyMeatDoriaStockBalance->setDescription('');
            $propertyMeatDoriaStockBalance->setExtendedDescription('');
            $propertyMeatDoriaStockBalance->setName('MeatDoria - Skladová zásoba');
            $propertyMeatDoriaStockBalance->setClassId(4);
            $propertyMeatDoriaStockBalance->setShowType(null);
            $propertyMeatDoriaStockBalance->setShowTypeTag('text');
            $propertyMeatDoriaStockBalance->setValueType('oneline_text');
            $propertyMeatDoriaStockBalance->setDefaultValue('');
            $propertyMeatDoriaStockBalance->setMultiOperations(false);
            $propertyMeatDoriaStockBalance->setInputString('');
            $propertyMeatDoriaStockBalance->setAttribute('tab', 'MeatDoria');
            $propertyMeatDoriaStockBalance->setAttribute('size', '60');
            $propertyMeatDoriaStockBalance->setAttribute('maxlength', '');
            $propertyMeatDoriaStockBalance->setAttribute('readonly', 'F');
            $propertyMeatDoriaStockBalance->setAttribute('pattern', '');
            $propertyMeatDoriaStockBalance->setAttribute('inherit_value', 'F');
            $propertyMeatDoriaStockBalance->setAttribute('onchange-js', '');
            $propertyMeatDoriaStockBalance->setAttribute('onkeyup-js', '');
            $propertyMeatDoriaStockBalance->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyMeatDoriaStockBalance);
        } else {
            $this->writeLine('Property with tag meat_doria_stock_balance already exists');
            $this->setDataKey('property_meat_doria_stock_balance_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('meat_doria_stock_balance', 'eshop_product', false);
        }

        // property: MeatDoria - OE number(meat_doria_oe_number)
        $propertyMeatDoriaOeNumber = $this->propertyManager()->propertyExistsByTag('meat_doria_oe_number');
        if ($propertyMeatDoriaOeNumber === false) {
            $propertyMeatDoriaOeNumber = new Property();
            $propertyMeatDoriaOeNumber->setTag('meat_doria_oe_number');
            $propertyMeatDoriaOeNumber->setDescription('');
            $propertyMeatDoriaOeNumber->setExtendedDescription('');
            $propertyMeatDoriaOeNumber->setName('MeatDoria - OE number');
            $propertyMeatDoriaOeNumber->setClassId(4);
            $propertyMeatDoriaOeNumber->setShowType(null);
            $propertyMeatDoriaOeNumber->setShowTypeTag('text');
            $propertyMeatDoriaOeNumber->setValueType('oneline_text');
            $propertyMeatDoriaOeNumber->setDefaultValue('');
            $propertyMeatDoriaOeNumber->setMultiOperations(false);
            $propertyMeatDoriaOeNumber->setInputString('');
            $propertyMeatDoriaOeNumber->setAttribute('tab', 'MeatDoria');
            $propertyMeatDoriaOeNumber->setAttribute('size', '60');
            $propertyMeatDoriaOeNumber->setAttribute('maxlength', '');
            $propertyMeatDoriaOeNumber->setAttribute('readonly', 'F');
            $propertyMeatDoriaOeNumber->setAttribute('pattern', '');
            $propertyMeatDoriaOeNumber->setAttribute('inherit_value', 'F');
            $propertyMeatDoriaOeNumber->setAttribute('onchange-js', '');
            $propertyMeatDoriaOeNumber->setAttribute('onkeyup-js', '');
            $propertyMeatDoriaOeNumber->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyMeatDoriaOeNumber);
        } else {
            $this->writeLine('Property with tag meat_doria_oe_number already exists');
            $this->setDataKey('property_meat_doria_oe_number_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('meat_doria_oe_number', 'eshop_product', false);
        }

        // property: MeatDoria - OE numbers(meat_doria_oe_numbers)
        $propertyMeatDoriaOeNumbers = $this->propertyManager()->propertyExistsByTag('meat_doria_oe_numbers');
        if ($propertyMeatDoriaOeNumbers === false) {
            $propertyMeatDoriaOeNumbers = new Property();
            $propertyMeatDoriaOeNumbers->setTag('meat_doria_oe_numbers');
            $propertyMeatDoriaOeNumbers->setDescription('');
            $propertyMeatDoriaOeNumbers->setExtendedDescription('');
            $propertyMeatDoriaOeNumbers->setName('MeatDoria - OE numbers');
            $propertyMeatDoriaOeNumbers->setClassId(4);
            $propertyMeatDoriaOeNumbers->setShowType(null);
            $propertyMeatDoriaOeNumbers->setShowTypeTag('text');
            $propertyMeatDoriaOeNumbers->setValueType('oneline_text');
            $propertyMeatDoriaOeNumbers->setDefaultValue('');
            $propertyMeatDoriaOeNumbers->setMultiOperations(false);
            $propertyMeatDoriaOeNumbers->setInputString('');
            $propertyMeatDoriaOeNumbers->setAttribute('tab', 'MeatDoria');
            $propertyMeatDoriaOeNumbers->setAttribute('size', '60');
            $propertyMeatDoriaOeNumbers->setAttribute('maxlength', '');
            $propertyMeatDoriaOeNumbers->setAttribute('readonly', 'F');
            $propertyMeatDoriaOeNumbers->setAttribute('pattern', '');
            $propertyMeatDoriaOeNumbers->setAttribute('inherit_value', 'F');
            $propertyMeatDoriaOeNumbers->setAttribute('onchange-js', '');
            $propertyMeatDoriaOeNumbers->setAttribute('onkeyup-js', '');
            $propertyMeatDoriaOeNumbers->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyMeatDoriaOeNumbers);
        } else {
            $this->writeLine('Property with tag meat_doria_oe_numbers already exists');
            $this->setDataKey('property_meat_doria_oe_numbers_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('meat_doria_oe_numbers', 'eshop_product', false);
        }

        // property: MeatDoria - Posledný import(meat_doria_latest_import)
        $propertyMeatDoriaLatestImport = $this->propertyManager()->propertyExistsByTag('meat_doria_latest_import');
        if ($propertyMeatDoriaLatestImport === false) {
            $propertyMeatDoriaLatestImport = new Property();
            $propertyMeatDoriaLatestImport->setTag('meat_doria_latest_import');
            $propertyMeatDoriaLatestImport->setDescription('');
            $propertyMeatDoriaLatestImport->setExtendedDescription('');
            $propertyMeatDoriaLatestImport->setName('MeatDoria - Posledný import');
            $propertyMeatDoriaLatestImport->setClassId(4);
            $propertyMeatDoriaLatestImport->setShowType(null);
            $propertyMeatDoriaLatestImport->setShowTypeTag('text');
            $propertyMeatDoriaLatestImport->setValueType('oneline_text');
            $propertyMeatDoriaLatestImport->setDefaultValue('');
            $propertyMeatDoriaLatestImport->setMultiOperations(false);
            $propertyMeatDoriaLatestImport->setInputString('');
            $propertyMeatDoriaLatestImport->setAttribute('tab', 'MeatDoria');
            $propertyMeatDoriaLatestImport->setAttribute('size', '60');
            $propertyMeatDoriaLatestImport->setAttribute('maxlength', '');
            $propertyMeatDoriaLatestImport->setAttribute('readonly', 'F');
            $propertyMeatDoriaLatestImport->setAttribute('pattern', '');
            $propertyMeatDoriaLatestImport->setAttribute('inherit_value', 'F');
            $propertyMeatDoriaLatestImport->setAttribute('onchange-js', '');
            $propertyMeatDoriaLatestImport->setAttribute('onkeyup-js', '');
            $propertyMeatDoriaLatestImport->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyMeatDoriaLatestImport);
        } else {
            $this->writeLine('Property with tag meat_doria_latest_import already exists');
            $this->setDataKey('property_meat_doria_latest_import_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('meat_doria_latest_import', 'eshop_product', false);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }

    public function down()
    {
        // remove property: MeatDoria - Posledný import(meat_doria_latest_import)
        $propertyMeatDoriaLatestImport = $this->propertyManager()->propertyExistsByTag('meat_doria_latest_import');
        if (($propertyMeatDoriaLatestImport !== false) && ($this->getDataKey('property_meat_doria_latest_import_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyMeatDoriaLatestImport);
        }

        // remove property: MeatDoria - OE numbers(meat_doria_oe_numbers)
        $propertyMeatDoriaOeNumbers = $this->propertyManager()->propertyExistsByTag('meat_doria_oe_numbers');
        if (($propertyMeatDoriaOeNumbers !== false) && ($this->getDataKey('property_meat_doria_oe_numbers_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyMeatDoriaOeNumbers);
        }

        // remove property: MeatDoria - OE number(meat_doria_oe_number)
        $propertyMeatDoriaOeNumber = $this->propertyManager()->propertyExistsByTag('meat_doria_oe_number');
        if (($propertyMeatDoriaOeNumber !== false) && ($this->getDataKey('property_meat_doria_oe_number_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyMeatDoriaOeNumber);
        }

        // remove property: MeatDoria - Skladová zásoba(meat_doria_stock_balance)
        $propertyMeatDoriaStockBalance = $this->propertyManager()->propertyExistsByTag('meat_doria_stock_balance');
        if (($propertyMeatDoriaStockBalance !== false) && ($this->getDataKey('property_meat_doria_stock_balance_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyMeatDoriaStockBalance);
        }

        // remove property: MeatDoria - Cena bez DPH(meat_doria_price_without_vat)
        $propertyMeatDoriaPriceWithoutVat = $this->propertyManager()->propertyExistsByTag('meat_doria_price_without_vat');
        if (($propertyMeatDoriaPriceWithoutVat !== false) && ($this->getDataKey('property_meat_doria_price_without_vat_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyMeatDoriaPriceWithoutVat);
        }

        // remove property: MeatDoria - Dodávateľský kód(meat_doria_supplier_code)
        $propertyMeatDoriaSupplierCode = $this->propertyManager()->propertyExistsByTag('meat_doria_supplier_code');
        if (($propertyMeatDoriaSupplierCode !== false) && ($this->getDataKey('property_meat_doria_supplier_code_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyMeatDoriaSupplierCode);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }
}
