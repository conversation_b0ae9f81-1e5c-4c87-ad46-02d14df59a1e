<?php

use Buxus\Migration\AbstractMigration;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;

/**
 * Automatic generation from (rinoparts) at 2022-05-09 13:39:40
 */
class MakeFullnameNullableOnDeliveryAddressTableMigration extends AbstractMigration
{
    public function up()
    {
        // update table tblDeliveryAddresses
        Schema::table('tblDeliveryAddresses', function (Blueprint $table) {
            $table->string('fullname')->nullable()->change();
        });
    }

    public function down()
    {
        // revert changes to table tblDeliveryAddresses
        Schema::table('tblDeliveryAddresses', function (Blueprint $table) {
            $table->string('fullname')->nullable(false)->change();
        });
    }
}
