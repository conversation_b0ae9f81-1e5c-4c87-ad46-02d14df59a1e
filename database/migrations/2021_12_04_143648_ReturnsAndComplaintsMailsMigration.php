<?php

use Buxus\Migration\AbstractMigration;

/**
 * Automatic generation from (rinoparts) at 2021-12-04 14:36:48
 * Page generator: page_id=376198,376197
 */
class ReturnsAndComplaintsMailsMigration extends AbstractMigration
{
    public function up()
    {
        // page: Vrátenie tovaru - email(ID: 376198 TAG: returns_email_html)
        $pageId = $this->getPageIdByTag('returns_email_html');
        if ($pageId === null) {
            $pageType = $this->pageTypesManager()->getPageTypeByTag('basic_email');
            $page376198 = \PageFactory::create($this->getPageIdByTag('E-maily shop modulu'), $pageType->getId());
        } else {
            $page376198 = \PageFactory::get($pageId);
        }
        $page376198->setPageName('Vrátenie tovaru - email');
        $page376198->setPageTag('returns_email_html');
        $page376198->setPageStateId('2');
        $page376198->setPageClassId(1);
        $page376198->setValue('email_sender', '<EMAIL>');
        $page376198->setValue('email_recipients', '<EMAIL>');
        $page376198->setValue('email_subject', 'Vrátenie tovaru {{INVOICE_NUMBER}}');
        $page376198->setValue('mail_embed_images', 'F');
        $page376198->setValue('email_body_html', base64_decode('PHAgc3R5bGU9InRleHQtYWxpZ246IGNlbnRlcjsiPjxzcGFuIHN0eWxlPSJmb250LXNpemU6IDE4cHQ7Ij48c3Ryb25nPlZyw6F0ZW5pZSB0b3ZhcnU8L3N0cm9uZz48L3NwYW4+PC9wPg0KPHA+xIzDrXNsbyBmYWt0w7pyeToge3tJTlZPSUNFX05VTUJFUn19PC9wPg0KPHA+SW50ZXJuw6kgxI3DrXNsbyBkaWVsdSBSaW5vcGFydHM6IHt7SU5URVJOQUxfTlVNQkVSfX08L3A+DQo8cD5PcmlnaW7DoWxuZSDEjcOtc2xvIGRpZWx1IElWRUNPOiB7e09SSUdJTkFMX05VTUJFUn19PC9wPg0KPHA+UG/EjWV0IGt1c292IG5hIHZyw6F0ZW5pZToge3tDT1VOVH19PC9wPg0KPHA+RMO0dm9kIHZyw6F0ZW5pYSB0b3ZhcnU6IHt7UkVBU09OfX08L3A+DQo8cD5UZWxlZsOzbjoge3tQSE9ORX19PC9wPg0KPHA+RW1haWxvdsOhIGFkcmVzYToge3tFTUFJTH19PC9wPg0KPHA+PC9wPg=='));
        $page376198->setValue('rendered_email_bottom_text', '');
        $page376198->setValue('attachment_list', []);
        // set template on MAIN PAGE index::error404
        $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('basic_email'), 'index', 'error404');
        $page376198->save();

        // page: Reklamačný mail(ID: 376197 TAG: complaints_email_html)
        $pageId = $this->getPageIdByTag('complaints_email_html');
        if ($pageId === null) {
            $pageType = $this->pageTypesManager()->getPageTypeByTag('basic_email');
            $page376197 = \PageFactory::create($this->getPageIdByTag('E-maily shop modulu'), $pageType->getId());
        } else {
            $page376197 = \PageFactory::get($pageId);
        }
        $page376197->setPageName('Reklamačný mail');
        $page376197->setPageTag('complaints_email_html');
        $page376197->setPageStateId('2');
        $page376197->setPageClassId(1);
        $page376197->setValue('email_sender', '<EMAIL>');
        $page376197->setValue('email_recipients', '<EMAIL>');
        $page376197->setValue('email_subject', 'Reklamácia {{INVOICE_NUMBER}}');
        $page376197->setValue('mail_embed_images', 'F');
        $page376197->setValue('email_body_html', base64_decode('PHAgc3R5bGU9InRleHQtYWxpZ246IGNlbnRlcjsiPjxzcGFuIHN0eWxlPSJmb250LXNpemU6IDE4cHQ7Ij48c3Ryb25nPlJla2xhbcOhY2lhPC9zdHJvbmc+PC9zcGFuPjwvcD4NCjxwPsSMw61zbG8gZmFrdMO6cnk6IHt7SU5WT0lDRV9OVU1CRVJ9fTwvcD4NCjxwPkludGVybsOpIMSNw61zbG8gZGllbHUgUmlub3BhcnRzOiB7e0lOVEVSTkFMX05VTUJFUn19PC9wPg0KPHA+T3JpZ2luw6FsbmUgxI3DrXNsbyBkaWVsdSBJVkVDTzoge3tPUklHSU5BTF9OVU1CRVJ9fTwvcD4NCjxwPlBvxI1ldCBrdXNvdiBuYSB2csOhdGVuaWU6IHt7Q09VTlR9fTwvcD4NCjxwPkTDoXR1bSBtb250w6HFvmUgZGllbHU6IHt7TU9OVEFHRV9EQVRFfX08L3A+DQo8cD5WSU4gxI3DrXNsbyBwb2R2b3prdToge3tWSU5fTlVNQkVSfX08L3A+DQo8cD5Qb3BpcyB6w6F2YWR5OiB7e0RFRkVDVF9ERVNDUklQVElPTn19PC9wPg0KPHA+VGVsZWbDs246IHt7UEhPTkV9fTwvcD4NCjxwPkVtYWlsb3bDoSBhZHJlc2E6IHt7RU1BSUx9fTwvcD4NCjxwPjwvcD4='));
        $page376197->setValue('rendered_email_bottom_text', '');
        $page376197->setValue('attachment_list', []);
        // set template on MAIN PAGE index::error404
        $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('basic_email'), 'index', 'error404');
        $page376197->save();
    }

    public function down()
    {
        // remove page: Reklamačný mail (complaints_email_html)
        $pageId = $this->getPageIdByTag('complaints_email_html');
        if ($pageId !== null) {
            \PageFactory::get($pageId)->delete();
        }

        // remove page: Vrátenie tovaru - email (returns_email_html)
        $pageId = $this->getPageIdByTag('returns_email_html');
        if ($pageId !== null) {
            \PageFactory::get($pageId)->delete();
        }
    }
}
