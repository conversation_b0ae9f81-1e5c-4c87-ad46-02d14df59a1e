<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;

/**
 * Automatic generation from (rinoparts) at 2024-11-14 11:15:07
 * Property generator: property=manually_set_dimensions_and_weight,weight_dimensions_latest_import
 */
class WeightAndDimensionsImportPropertiesMigration extends AbstractMigration
{
    public function up()
    {
        // property: Manuálne nastavené rozmery a váha(manually_set_dimensions_and_weight)
        $propertyManuallySetDimensionsAndWeight = $this->propertyManager()->propertyExistsByTag('manually_set_dimensions_and_weight');
        if ($propertyManuallySetDimensionsAndWeight === false) {
            $propertyManuallySetDimensionsAndWeight = new Property();
            $propertyManuallySetDimensionsAndWeight->setTag('manually_set_dimensions_and_weight');
            $propertyManuallySetDimensionsAndWeight->setDescription('');
            $propertyManuallySetDimensionsAndWeight->setExtendedDescription('');
            $propertyManuallySetDimensionsAndWeight->setName('Manuálne nastavené rozmery a váha');
            $propertyManuallySetDimensionsAndWeight->setClassId(4);
            $propertyManuallySetDimensionsAndWeight->setShowType(null);
            $propertyManuallySetDimensionsAndWeight->setShowTypeTag('checkbox');
            $propertyManuallySetDimensionsAndWeight->setValueType('logical_value');
            $propertyManuallySetDimensionsAndWeight->setDefaultValue('');
            $propertyManuallySetDimensionsAndWeight->setMultiOperations(false);
            $propertyManuallySetDimensionsAndWeight->setInputString('');
            $propertyManuallySetDimensionsAndWeight->setAttribute('tab', 'Rozmery a v&aacute;ha');
            $propertyManuallySetDimensionsAndWeight->setAttribute('on_value', 'T');
            $propertyManuallySetDimensionsAndWeight->setAttribute('off_value', 'F');
            $propertyManuallySetDimensionsAndWeight->setAttribute('onclick-js', '');
            $propertyManuallySetDimensionsAndWeight->setAttribute('inherit_value', 'F');
            $propertyManuallySetDimensionsAndWeight->setAttribute('readonly', 'F');
            $this->propertyManager()->saveProperty($propertyManuallySetDimensionsAndWeight);
        } else {
            $this->writeLine('Property with tag manually_set_dimensions_and_weight already exists');
            $this->setDataKey('property_manually_set_dimensions_and_weight_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('manually_set_dimensions_and_weight', 'eshop_product', false);
        }

        // property: Posledný import váh a rozmerov(weight_dimensions_latest_import)
        $propertyWeightDimensionsLatestImport = $this->propertyManager()->propertyExistsByTag('weight_dimensions_latest_import');
        if ($propertyWeightDimensionsLatestImport === false) {
            $propertyWeightDimensionsLatestImport = new Property();
            $propertyWeightDimensionsLatestImport->setTag('weight_dimensions_latest_import');
            $propertyWeightDimensionsLatestImport->setDescription('');
            $propertyWeightDimensionsLatestImport->setExtendedDescription('');
            $propertyWeightDimensionsLatestImport->setName('Posledný import váh a rozmerov');
            $propertyWeightDimensionsLatestImport->setClassId(4);
            $propertyWeightDimensionsLatestImport->setShowType(null);
            $propertyWeightDimensionsLatestImport->setShowTypeTag('text');
            $propertyWeightDimensionsLatestImport->setValueType('oneline_text');
            $propertyWeightDimensionsLatestImport->setDefaultValue('');
            $propertyWeightDimensionsLatestImport->setMultiOperations(false);
            $propertyWeightDimensionsLatestImport->setInputString('');
            $propertyWeightDimensionsLatestImport->setAttribute('tab', 'Rozmery a v&aacute;ha');
            $propertyWeightDimensionsLatestImport->setAttribute('size', '60');
            $propertyWeightDimensionsLatestImport->setAttribute('maxlength', '');
            $propertyWeightDimensionsLatestImport->setAttribute('readonly', 'F');
            $propertyWeightDimensionsLatestImport->setAttribute('pattern', '');
            $propertyWeightDimensionsLatestImport->setAttribute('inherit_value', 'F');
            $propertyWeightDimensionsLatestImport->setAttribute('onchange-js', '');
            $propertyWeightDimensionsLatestImport->setAttribute('onkeyup-js', '');
            $propertyWeightDimensionsLatestImport->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyWeightDimensionsLatestImport);
        } else {
            $this->writeLine('Property with tag weight_dimensions_latest_import already exists');
            $this->setDataKey('property_weight_dimensions_latest_import_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('weight_dimensions_latest_import', 'eshop_product', false);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }

    public function down()
    {
        // remove property: Posledný import váh a rozmerov(weight_dimensions_latest_import)
        $propertyWeightDimensionsLatestImport = $this->propertyManager()->propertyExistsByTag('weight_dimensions_latest_import');
        if (($propertyWeightDimensionsLatestImport !== false) && ($this->getDataKey('property_weight_dimensions_latest_import_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyWeightDimensionsLatestImport);
        }

        // remove property: Manuálne nastavené rozmery a váha(manually_set_dimensions_and_weight)
        $propertyManuallySetDimensionsAndWeight = $this->propertyManager()->propertyExistsByTag('manually_set_dimensions_and_weight');
        if (($propertyManuallySetDimensionsAndWeight !== false) && ($this->getDataKey('property_manually_set_dimensions_and_weight_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyManuallySetDimensionsAndWeight);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }
}
