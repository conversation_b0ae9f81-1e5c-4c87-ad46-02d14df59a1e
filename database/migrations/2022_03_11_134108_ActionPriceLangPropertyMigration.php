<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;

/**
 * Automatic generation from (rinoparts) at 2022-03-11 13:41:08
 * Property generator: property=eshop_eur_action_price_without_vat_en
 */
class ActionPriceLangPropertyMigration extends AbstractMigration
{
    public function up()
    {
        // property: Akciová cena EUR bez DPH [EN](eshop_eur_action_price_without_vat_en)
        $propertyEshopEurActionPriceWithoutVatEn = $this->propertyManager()->propertyExistsByTag('eshop_eur_action_price_without_vat_en');
        if ($propertyEshopEurActionPriceWithoutVatEn === false) {
            $propertyEshopEurActionPriceWithoutVatEn = new Property();
            $propertyEshopEurActionPriceWithoutVatEn->setTag('eshop_eur_action_price_without_vat_en');
            $propertyEshopEurActionPriceWithoutVatEn->setDescription('<PERSON>kciová cena bez DPH');
            $propertyEshopEurActionPriceWithoutVatEn->setExtendedDescription('');
            $propertyEshopEurActionPriceWithoutVatEn->setName('Akciová cena EUR bez DPH [EN]');
            $propertyEshopEurActionPriceWithoutVatEn->setClassId(4);
            $propertyEshopEurActionPriceWithoutVatEn->setShowType(null);
            $propertyEshopEurActionPriceWithoutVatEn->setShowTypeTag('text');
            $propertyEshopEurActionPriceWithoutVatEn->setValueType('oneline_text');
            $propertyEshopEurActionPriceWithoutVatEn->setDefaultValue('');
            $propertyEshopEurActionPriceWithoutVatEn->setMultiOperations(false);
            $propertyEshopEurActionPriceWithoutVatEn->setInputString('');
            $propertyEshopEurActionPriceWithoutVatEn->setAttribute('tab', 'Cena');
            $propertyEshopEurActionPriceWithoutVatEn->setAttribute('size', '10');
            $propertyEshopEurActionPriceWithoutVatEn->setAttribute('maxlength', '');
            $propertyEshopEurActionPriceWithoutVatEn->setAttribute('readonly', 'F');
            $propertyEshopEurActionPriceWithoutVatEn->setAttribute('pattern', '');
            $propertyEshopEurActionPriceWithoutVatEn->setAttribute('inherit_value', 'F');
            $propertyEshopEurActionPriceWithoutVatEn->setAttribute('onchange-js', '');
            $propertyEshopEurActionPriceWithoutVatEn->setAttribute('onkeyup-js', '');
            $propertyEshopEurActionPriceWithoutVatEn->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyEshopEurActionPriceWithoutVatEn);
        } else {
            $this->writeLine('Property with tag eshop_eur_action_price_without_vat_en already exists');
            $this->setDataKey('property_eshop_eur_action_price_without_vat_en_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('eshop_eur_action_price_without_vat_en', 'eshop_product', false);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }

    public function down()
    {
        // remove property: Akciová cena EUR bez DPH [EN](eshop_eur_action_price_without_vat_en)
        $propertyEshopEurActionPriceWithoutVatEn = $this->propertyManager()->propertyExistsByTag('eshop_eur_action_price_without_vat_en');
        if (($propertyEshopEurActionPriceWithoutVatEn !== false) && ($this->getDataKey('property_eshop_eur_action_price_without_vat_en_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyEshopEurActionPriceWithoutVatEn);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }
}
