<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;

/**
 * Automatic generation from (rinoparts) at 2022-02-10 11:28:08
 * Property generator: property=promo_btn_text,promo_text
 */
class PromoboxLanguagePropertiesMigration extends AbstractMigration
{
    public function up()
    {
        // property: Text tlačidla promobox(promo_btn_text)
        $propertyPromoBtnText = $this->propertyManager()->propertyExistsByTag('promo_btn_text');
        if ($propertyPromoBtnText === false) {
            $propertyPromoBtnText = new Property();
            $propertyPromoBtnText->setTag('promo_btn_text');
            $propertyPromoBtnText->setDescription('Text, ktorý sa zobrazí v tlačidle v promoboxe. Doporučuje sa používať max. 15 znakov pre správna zobrazenie na rôznych zariadeniach.');
            $propertyPromoBtnText->setExtendedDescription('');
            $propertyPromoBtnText->setName('Text tlačidla promobox');
            $propertyPromoBtnText->setClassId(4);
            $propertyPromoBtnText->setShowType(null);
            $propertyPromoBtnText->setShowTypeTag('text');
            $propertyPromoBtnText->setValueType('oneline_text');
            $propertyPromoBtnText->setDefaultValue('');
            $propertyPromoBtnText->setMultiOperations(false);
            $propertyPromoBtnText->setInputString(null);
            $propertyPromoBtnText->setAttribute('tab', '');
            $propertyPromoBtnText->setAttribute('size', '60');
            $propertyPromoBtnText->setAttribute('maxlength', '30');
            $propertyPromoBtnText->setAttribute('readonly', 'F');
            $propertyPromoBtnText->setAttribute('pattern', '');
            $propertyPromoBtnText->setAttribute('inherit_value', 'F');
            $propertyPromoBtnText->setAttribute('onchange-js', '');
            $propertyPromoBtnText->setAttribute('onkeyup-js', '');
            $propertyPromoBtnText->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyPromoBtnText);
        } else {
            $this->writeLine('Property with tag promo_btn_text already exists');
            $this->setDataKey('property_promo_btn_text_existed', true);
        }
        if ($this->pageTypeExists('layout_element_promobox')) {
            $this->addPropertyToPageType('promo_btn_text', 'layout_element_promobox', false);
        }

        // property: Promobox text(promo_text)
        $propertyPromoText = $this->propertyManager()->propertyExistsByTag('promo_text');
        if ($propertyPromoText === false) {
            $propertyPromoText = new Property();
            $propertyPromoText->setTag('promo_text');
            $propertyPromoText->setDescription('Text, ktorý sa zobrazí v promoboxe.');
            $propertyPromoText->setExtendedDescription('');
            $propertyPromoText->setName('Promobox text');
            $propertyPromoText->setClassId(4);
            $propertyPromoText->setShowType(null);
            $propertyPromoText->setShowTypeTag('text');
            $propertyPromoText->setValueType('oneline_text');
            $propertyPromoText->setDefaultValue('');
            $propertyPromoText->setMultiOperations(false);
            $propertyPromoText->setInputString(null);
            $propertyPromoText->setAttribute('tab', '');
            $propertyPromoText->setAttribute('size', '60');
            $propertyPromoText->setAttribute('maxlength', '');
            $propertyPromoText->setAttribute('readonly', 'F');
            $propertyPromoText->setAttribute('pattern', '');
            $propertyPromoText->setAttribute('inherit_value', 'F');
            $propertyPromoText->setAttribute('onchange-js', '');
            $propertyPromoText->setAttribute('onkeyup-js', '');
            $propertyPromoText->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyPromoText);
        } else {
            $this->writeLine('Property with tag promo_text already exists');
            $this->setDataKey('property_promo_text_existed', true);
        }
        if ($this->pageTypeExists('layout_element_promobox')) {
            $this->addPropertyToPageType('promo_text', 'layout_element_promobox', false);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }

    public function down()
    {
        // remove property: Promobox text(promo_text)
        $propertyPromoText = $this->propertyManager()->propertyExistsByTag('promo_text');
        if (($propertyPromoText !== false) && ($this->getDataKey('property_promo_text_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyPromoText);
        }

        // remove property: Text tlačidla promobox(promo_btn_text)
        $propertyPromoBtnText = $this->propertyManager()->propertyExistsByTag('promo_btn_text');
        if (($propertyPromoBtnText !== false) && ($this->getDataKey('property_promo_btn_text_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyPromoBtnText);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }
}
