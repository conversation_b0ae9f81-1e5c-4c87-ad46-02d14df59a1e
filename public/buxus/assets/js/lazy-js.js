define(['jquery'], function (jQuery) {
    var watchElement = function (element, callback) {
        if ('IntersectionObserver' in window && 'IntersectionObserverEntry' in window && "intersectionRatio" in window.IntersectionObserverEntry.prototype) {
            var mediaObserver = new window.IntersectionObserver(function (entries, observer) {
                entries.forEach(function (entry) {
                    if (entry.isIntersecting) {
                        callback(element);
                        mediaObserver.unobserve(entry.target);
                    }
                });
            });
            mediaObserver.observe(element);
            return;
        }

        // If IntersectionObserver isn't available, we'll run the callback
        callback(element);
    }

    jQuery.fn.lazyJS = function () {
        var $input_object = this;

        if (typeof $input_object.get(0) === 'undefined') {
            return;
        }

        if (typeof arguments[0] === 'function') {
            var callerFunction = arguments[0];
            $input_object.each(function () {
                var caller = this;
                watchElement(caller, function () {
                    callerFunction.apply(caller, [caller]);
                });
            });
            return;
        }

        // give up on unsupported browsers
        if (typeof Proxy !== 'function') {
            return $input_object;
        }

        return new Proxy($input_object, {
            get: function (target, name) {
                if (typeof target[name] === 'function') {
                    return function () {
                        var callerArguments = arguments;
                        var commandChain = [];

                        var chainer = new Proxy({}, {
                            get: function (target, name) {
                                return function () {
                                    commandChain.push({
                                        func: name,
                                        args: arguments
                                    });
                                    return chainer;
                                }
                            }
                        });

                        var executeCallback = function (element) {
                            var jqueryElement = jQuery(element);
                            var result = target[name].apply(jqueryElement, callerArguments);

                            for (var i = 0; i < commandChain.length; i++) {
                                result = result[commandChain[i].func].apply(result, commandChain[i].args);
                            }
                        };

                        $input_object.each(function () {
                            watchElement(this, executeCallback);
                        });

                        return chainer;
                    };
                }
                return target[name];
            }
        });
    };

    return jQuery.fn.lazyJS;
});
