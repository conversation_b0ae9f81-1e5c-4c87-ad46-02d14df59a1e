define(['jquery'], function($) {

    var module = {
        /**
         *
         * Raz (roku 2050) tu budeme moct pouzit:
         *    position: sticky;
         *    @link https://developer.mozilla.org/en-US/docs/Web/CSS/position#Browser_compatibility
         *
         *
         * @type {{STICKY_CLASS: string, sticky_items: Array, init: Function, stick: Function}}
         */
        sticky: {

            STICKY_CLASS: 'sticky',
            sticky_items: [],

            init: function () {
                var that = this;

                $('.is_sticky').each(function () {
                    that.sticky_items.push($(this));
                });

                $.each(this.sticky_items, function (key, item) {
                    item.data('offset', item.offset().top);
                    item.data('width', item.width());

                    $(window).on('scroll', function () {
                        that.stick(item);
                    });

                    // na zaciatok to pustime
                    that.stick(item);
                });
            },
            stick: function (item) {
                if (item.data('offset') < $(window).scrollTop()) {
                    item.addClass(this.STICKY_CLASS);
                    item.css('width', item.data("width"));
                } else {
                    item.removeClass(this.STICKY_CLASS);
                }
            }
        }
    };

    $(function () {
        module.sticky.init();
    });

    return module;

});