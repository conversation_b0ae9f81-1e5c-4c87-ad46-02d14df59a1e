define(['jquery'], function ($) {
    let csrf = {
        placeholderSelector: '.js-csrf-placeholder',

        init: function () {
            let token = csrf.getXsrfCookie();
            let headerData = {
                'X-XSRF-TOKEN': token
            };

            if (token.length <= 40) {
                headerData = {
                    'X-CSRF-TOKEN': token
                };
            }

            $.ajaxSetup({
                headers: headerData
            });

            $(function () {
                csrf.setPlaceholders(token);
            });
        },

        setPlaceholders: function (token) {
            let placeholders = $(csrf.placeholderSelector);

            if (placeholders.length) {
                $(csrf.placeholderSelector).val(token);
            }
        },

        readCookie: function (name) {
            const nameEQ = name + "=";
            const ca = document.cookie.split(';');
            for (let i = 0; i < ca.length; i++) {
                let c = ca[i];
                while (c.charAt(0) === ' ') {
                    c = c.substring(1, c.length);
                }
                if (c.indexOf(nameEQ) === 0) {
                    return c.substring(nameEQ.length, c.length);
                }
            }
            return null;
        },

        getXsrfCookie: function () {
            let xsrfCookie = csrf.readCookie('XSRF-TOKEN');

            if (xsrfCookie === null) {
                $.ajax({
                    url: '/csrf',
                    method: 'OPTIONS',
                    async: false
                }).done(function () {
                    xsrfCookie = csrf.readCookie('XSRF-TOKEN')
                });
            }

            return decodeURIComponent(xsrfCookie);
        }
    };

    csrf.init();

    return csrf;
});
