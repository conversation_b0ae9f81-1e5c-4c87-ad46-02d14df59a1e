.form-tool {
  color: #151515;

  input, select, textarea {
    border: 1px solid #e5e6e7 !important;
    background-color: white !important;
    background-repeat: no-repeat !important;
  }

  td, th {
    padding: 5px 20px !important;
  }

  th {
    font-weight: 700;
  }

  table {
    margin: 15px 0 !important;
  }

  input.default {
    height: auto !important;
    border-radius: 0 !important;
  }

  form.response-form {
    min-height: 250px;
  }

  textarea#response, select#state, textarea#reason {
    width: 100%;
  }

  textarea.form-control:read-only {
    color: rgba(17, 22, 46, .75) !important;
  }

  span.clickable {
    cursor: pointer;
  }

  span.cross {
    color: red !important;
    font-size: 24px !important;
  }

  span.cross-file {
    color: red !important;
    font-size: 16px !important;
  }

  span.plus {
    color: green !important;
    font-size: 24px !important;
  }

  .table-responsive::-webkit-scrollbar {
    -webkit-appearance: none !important;
  }

  tr, th, td, table {
    border: none !important;
    background: none;
  }

  tr:hover {
    td {
      background: none;
    }

    background: none;
  }

  span.dot {
    height: 25px;
    width: 25px;
    border-radius: 50%;
    display: inline-block;
  }

  .table {
    display: block !important;
    overflow-x: auto !important;
    width: 100% !important;
  }

  .complaint-image {
    width: 100px;
    height: 100px;
    object-fit: contain;
  }
}
