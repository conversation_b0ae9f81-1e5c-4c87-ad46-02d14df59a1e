<?php

namespace App\Exports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use PriceViewer;

class ExtendedSalesStatisticsExport implements FromCollection, WithHeadings
{
    protected $collection;

    public function __construct(Collection $collection)
    {
        $this->collection = $collection;
    }

    public function headings(): array
    {
        return [
            'Men<PERSON>',
            '<PERSON>rma',
            'Č<PERSON><PERSON> obje<PERSON>ky',
            'Variabilný symbol',
            'Suma objedn<PERSON>vky',
            '<PERSON><PERSON><PERSON> objednávky',
            'Zákaznícka skupina',
        ];
    }

    public function collection()
    {
        return $this->collection->map(function ($log) {
            $log['order_price'] = PriceViewer::formatRawPrice($log['order_price'], '€');

            return collect($log)->only([
                'webuser_name',
                'order_company',
                'order_id',
                'order_vs',
                'order_price',
                'order_datetime',
                'ordered_for_category'
            ]);
        });
    }
}