<?php

namespace App\Persistence;

use Persistence\Adapter\DatabasePersistenceAdapter;
use Persistence\PersistenceItemModel;

class RinopartsDatabasePersistenceAdapter extends DatabasePersistenceAdapter
{
    public function saveData(PersistenceItemModel $persistenceItemModel): void
    {
        \Buxus::log('PERSISTENCE ADAPTER');
        if ((int)$persistenceItemModel->getUserId() > 0) {
            \DB::table(self::TABLE_NAME)
                ->updateOrInsert(
                    [
                        'user_id' => $persistenceItemModel->getUserId(),
                    ],
                    [
                        'id_hash' => $persistenceItemModel->getIdHash(),
                        'data' => @serialize($persistenceItemModel->getData()),
                        'expires' => \DB::raw('DATE_ADD(NOW(), INTERVAL 30 DAY)')
                    ]
                );
        }
    }


}
