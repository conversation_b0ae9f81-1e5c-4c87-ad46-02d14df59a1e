<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class TooManySearchNotification extends Mailable
{
    use Queueable, SerializesModels;

    protected $searchLimits;

    public function __construct($searchLimits)
    {
        $this->searchLimits = $searchLimits;
    }

    public function build()
    {
        return $this->view('email.too-many-searches')
            ->with('searchLimits', $this->searchLimits)
            ->subject('Upozornenie na prekročenie limitu vyhľadávaní');
    }
}
