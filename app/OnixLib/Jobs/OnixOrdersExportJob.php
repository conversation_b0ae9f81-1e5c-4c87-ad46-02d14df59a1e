<?php
namespace App\OnixLib\Jobs;

use App\Onix\Export\OnixOrdersExport;
use App\Onix\OnixHighPriority;
use App\OnixLib\Loggers\OnixOrdersExportLogger;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;

class OnixOrdersExportJob implements ShouldQueue
{
    use Queueable;


    /**
     * @var OnixOrdersExportLogger
     */
    protected $logger;

    /**
     * OnixOrdersExportJob constructor.
     * @param OnixOrdersExportLogger $logger
     */
    public function __construct(OnixOrdersExportLogger $logger)
    {
        $this->logger = $logger;

        $this->onQueue(OnixHighPriority::getQueueName() . env('DB_DATABASE'));
    }

    public function handle()
    {
        $export = new OnixOrdersExport($this->logger);
        try {
            $export->processExport();
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
            echo $e->getMessage();
            exit;
        }
    }
}
