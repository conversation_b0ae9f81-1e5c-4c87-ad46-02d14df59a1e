<?php
namespace App\OnixLib\Commands;

use App\Onix\Import\OnixInvoicesImport;
use App\OnixLib\Loggers\OnixInvoicesImportLogger;
use Illuminate\Console\Command;

class OnixInvoicesImportCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'onix:import-invoices';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import delivery notes from Onix API.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $logger = new OnixInvoicesImportLogger();
        $import = new OnixInvoicesImport($logger);
        $import->run();
    }
}
