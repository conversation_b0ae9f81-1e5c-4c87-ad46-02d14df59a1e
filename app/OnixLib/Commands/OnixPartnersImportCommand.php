<?php
namespace App\OnixLib\Commands;

use App\Onix\Import\OnixPartnersImport;
use App\OnixLib\Loggers\OnixPartnersImportLogger;
use Illuminate\Console\Command;

class OnixPartnersImportCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'onix:import-partners {csv?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import Partners from Onix API.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $csvFile = $this->argument('csv');

        $logger = new OnixPartnersImportLogger();
        $import = new OnixPartnersImport($logger, $csvFile);
        $import->run();
    }
}
