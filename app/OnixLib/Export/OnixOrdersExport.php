<?php

namespace App\OnixLib\Export;

use App\Onix\Onix;
use App\Onix\OnixHighPriority;
use App\Onix\OnixOrderItemsGenerator;
use App\OnixLib\Jobs\OnixOrdersExportJob;
use App\OnixLib\Libs\Reservations;
use App\OnixLib\Loggers\OnixOrdersExportLogger;
use Illuminate\Support\Facades\DB;

class OnixOrdersExport
{
    /** @var OnixOrdersExportLogger */
    protected $logger;

    /**
     * OnixOrdersExport constructor.
     * @param OnixOrdersExportLogger $logger
     */
    public function __construct(OnixOrdersExportLogger $logger)
    {
        $this->logger = $logger;
    }

    public function run()
    {
        try {
            $this->logger->info('Starting export');
            $job = new OnixOrdersExportJob($this->logger);

            if (Onix::isSync()) {
                $job->handle();
            } else {
                dispatch($job);
            }
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
            echo $e->getMessage();
        }
    }

    static public function getOrderStatesForProcessing()
    {
        return [];
    }

    static public function getOrderStateWaitingForOnix()
    {
        return null;
    }

    static public function getOrderStateNew()
    {
        return null;
    }

    static public function getOrderStateResendToOnix()
    {
        return null;
    }


    static public function getOrderIdsForProcessing($failed = false)
    {
        $builder = DB::table('tblShopOrders')
            ->leftJoin('tblShopOrderOptions', function ($join) {
                $join->on('tblShopOrders.order_id', '=', 'tblShopOrderOptions.order_id')
                    ->where('tblShopOrderOptions.order_tag', '=', 'onix_order_id');
            })
            ->whereNull('tblShopOrderOptions.order_value')
            ->whereIn('tblShopOrders.order_state', static::getOrderStatesForProcessing());

        if ($failed) {
            $builder->where('order_datetime', '<=', date('Y-m-d H:i:s', time() - 60 * 20)); // alert after 20 mins
            $builder->where('order_datetime', '>=', date('Y-m-d H:i:s', time() - 3600 * 24 * 3));
        } else {
            $builder->where('order_datetime', '<=', date('Y-m-d H:i:s', time() - 30)); // send after 30 seconds
        }

        $order_ids = $builder->pluck('tblShopOrders.order_id');

        return $order_ids;
    }

    /**
     * @param int $buxusOrderId
     * @return void
     */
    private function sendRequestExists($buxusOrderId)
    {
        if (DB::table('onix_requests')
            ->where('order_id', '=', $buxusOrderId)
            ->first()) {
            return true;
        };
        return false;
    }

    public function getEmaiForOrder($order, $webUser)
    {
        return trim($order->getEmail());
    }

    protected function customUpdate($buxusOrder, $onixPartnerId)
    {
        return false;
    }

    protected function getTransportTypeName($order, $webUser = null)
    {
        return $order->getTransportTypeName();
    }

    protected function getCountryName($order)
    {
        return '';
    }

    protected function getCustomColumns($order, $onixPartnerId)
    {
        return [];
    }


    public function processExport()
    {
        $onix = new OnixHighPriority();
        $order_ids = static::getOrderIdsForProcessing();

        foreach ($order_ids as $order_id) {
            $order = \OrderFactory::getById($order_id);

            $onix_partner_id = null;
            $user_id = $order->getUserId();
            if ($user_id) {
                $webUser = \WebUserFactory::getById($user_id);
                if ($webUser && !empty($webUser->getCustomOption('onix_partner_id'))) {
                    $onix_partner_id = $webUser->getCustomOption('onix_partner_id');
                }
            }

            if (!config('onix.orders_export.enable_export_orders_without_partner_id')
                && empty($onix_partner_id)) {
                $this->logger->info('Order skipped: Onix_partner_id is not set for order_id=' . $order_id . '.');
                continue;
            }


            if ((int)$order->getData('onix_order_id')) {
                $this->logger->info('Order ' . $order_id . ' alredy send.');
                continue;
            }

            if (!in_array($order->getOrderState(), static::getOrderStatesForProcessing())) {
                continue;
            }
            if ($order->getOrderState() === static::getOrderStateWaitingForOnix()) {
                continue;
            }


            if ($order->getOrderState() == static::getOrderStateNew()
                && $this->sendRequestExists($order->getOrderId())) {
//                continue;
            }


            $order->setOrderState(static::getOrderStateWaitingForOnix());
            $order->save();

            $itemsGenerator = new OnixOrderItemsGenerator();
            foreach ($order->getItems() as $orderItem) {
                $itemsGenerator->addBuxusProduct($orderItem);
            }
            $order_items = $itemsGenerator->generateOnixOrderItems();
            ray($order->getOrderId(), $order_items);

//            $vatRate = config('buxus_eshop.vat_rate');
//            $transport_price_with_vat = $order->getTransportPriceValue() + $order->getPaymentPriceValue();
//            $transport_price_without_vat = $transport_price_with_vat / (1.0 + ($vatRate / 100));


//            if ($transport_price_without_vat > 0) {
//                $order_items[] = [
//                    "Item_Name" => "DOPRAVA",
//                    "Stock_Items_Name" => "DOPRAVA",
//                    "Item_Type" => 2,
//                    "Unit_Price_Base" => $transport_price_without_vat
//                ];
//            }

            $order_data = [
                'Guid_Ext' => $order->getOrderId(),
                'Date_Document' => str_replace(' ', 'T', $order->getOrderDatetime()),
                'External_Number' => $order->getVariableSymbol(),
                'Curr' => $order->getCurrency(),
                'Transport_Type' => $this->getTransportTypeName($order, $webUser),
                'Payment_Type' => $order->getPaymentTypeName(), // 'Dobierka', 'Prevodný príkaz'
                'Items' => $order_items,
                'Variable_Symbol' => $order->getVariableSymbol(),

                'Partner_Da_Name' => $order->getDeliveryCompanyName() ?: $order->getDeliveryName(),
                'Partner_Da_Street' => $order->getDeliveryStreet(),
                'Partner_Da_Street_No' => '',
                'Partner_Da_Postcode' => $order->getDeliveryZip(),
                'Partner_Da_City' => $order->getDeliveryCity(),
                'Partner_Da_State' => $this->getCountryName($order),


                'Partner_Email' => $this->getEmaiForOrder($order, $webUser),
                'Partner_Telephone_Number' => $order->getDeliveryPhone(),
                'Delivery_Text' => $order->getNote(),

                'CustomColumns' => $this->getCustomColumns($order, $onix_partner_id),
            ];
            if ($onix_partner_id) {
                $order_data['Ns_Number_Partner'] = $onix_partner_id;
            }

            $order_data = $this->customUpdateData($order, $order_data);
            if ($this->customUpdate($order, $onix_partner_id)) {
                $order->save();
            }

            $result = $onix->sendOrder($onix_partner_id, $order_data);
            $this->afterSendOrder($order, $order_data, $result);
            Reservations::confirmReservationsForOrder($order);
        }

        $this->logger->info('DONE.');
    }

    protected function afterSendOrder($buxusOrder, $onixOrderData, $result)
    {
        if (!is_object($result) || !isset($result->Result)) {
            return;
        }
        if ($result->Result == 3) {
            if (!is_array($result->Errors)) {
                return;
            }
            foreach ($result->Errors as $error) {
                if (isset($error->Message) && mb_substr($error->Message, 0, 35) === 'Zadaný číselný rad sa práve používa') {
                    $buxusOrder->setOrderState(static::getOrderStateResendToOnix());
                    $buxusOrder->save();
                    $this->logger->info("Set resendToOnix state for buxus_order_id={$buxusOrder->getOrderId()}. Onix ERROR: " . $result->Errors[0]->Message);
                }
            }
        }
    }

    protected function customUpdateData($order, $order_data)
    {
        return $order_data;
    }

}
