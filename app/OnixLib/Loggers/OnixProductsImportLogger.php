<?php
namespace App\OnixLib\Loggers;


class OnixProductsImportLogger extends \Buxus\Logger\Logger
{
    /** @var int */
    protected $updatedProductsCount = 0;
    /** @var int */
    protected $createdProductsCount = 0;
    /** @var int */
    protected $deletedProductsCount = 0;

    public function __construct()
    {
        parent::__construct('onix/import-import-products');
    }

    /**
     * @return int
     */
    public function getUpdatedProductsCount()
    {
        return $this->updatedProductsCount;
    }
    /**
     * @return int
     */
    public function getCreatedProductsCount()
    {
        return $this->createdProductsCount;
    }
    /**
     * @return int
     */
    public function getDeletedProductsCount()
    {
        return $this->deletedProductsCount;
    }


    /**
     * @param int $createdProductsCount
     */
    public function setCreatedProductsCount($createdProductsCount)
    {
        $this->createdProductsCount = $createdProductsCount;
    }

    /**
     * @param int $deletedProductsCount
     */
    public function setDeletedProductsCount($deletedProductsCount)
    {
        $this->deletedProductsCount = $deletedProductsCount;
    }

    /**
     * @param int $updatedProductsCount
     */
    public function setUpdatedProductsCount($updatedProductsCount)
    {
        $this->updatedProductsCount = $updatedProductsCount;
    }


    public function incrementUpdatedProductCount()
    {
        $this->setUpdatedProductsCount($this->getUpdatedProductsCount() + 1);
    }
    public function incrementCreatedProductCount()
    {
        $this->setCreatedProductsCount($this->getCreatedProductsCount() + 1);
    }
    public function incrementDeletedProductCount()
    {
        $this->setDeletedProductsCount($this->getDeletedProductsCount() + 1);
    }
}
