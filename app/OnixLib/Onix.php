<?php

namespace App\OnixLib;

use <PERSON>uxus\Error\ErrorReporter;
use Buxus\Eshop\Order\OrderItem;
use Buxus\Logger\Logger;
use Buxus\Util\PageIds;
use Buxus\Util\PageTypeID;
use Buxus\Util\PropertyID;
use Buxus\Util\PropertyTag;
use Carbon\Carbon;
use GuzzleHttp\Client;
use GuzzleHttp\RequestOptions;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class Onix extends Client
{

    public const DELIVERY_NOTE_TYPE_ID = 3;
    public const CREDIT_NOTE_TYPE_ID = 2;
    public const INVOICE_TYPE_ID = 1;
    /**
     * @var Logger
     */
    protected $logger;

    function __construct(array $config = [])
    {
        if (isset($config['logger'])) {
            $this->logger = $config['logger'];
            unset($config['logger']);
        } else {
            $this->logger = new Logger('onix');
        }

        if (!isset($config['base_uri'])) {
            $config['base_uri'] = $this->getConfigValue('api_url');
        }

        if (!isset($config['headers'])) {
            $config['headers'] = [];
        }

        $config['headers']['Authorization'] = 'Bearer ' . $this->getConfigValue('bearer_token');
        $config['headers']['DatabasePath'] = $this->getConfigValue('database_path');
        $config['headers']['Accept'] = 'application/json';

        $config['debug'] = true;
        $config['http_errors'] = false;
        $config['timeout'] = $this->getConfigValue('timeout');

        Client::__construct($config);
    }

    function getConfigValue($tag)
    {
        return config('onix.' . $tag);
    }

    public function getOnixUsername()
    {
        return $this->getConfigValue('onix_user');
    }

    public function isOnline()
    {
        return true;
        if (in_array(app()->environment(), ['local', 'test']) && $this->getConfigValue('api_url') === null) {
            return false;
        }

        return true;
    }

    static function isSync()
    {
        if (app()->environment() === 'local' && config('onix.api_url') === null) {
            return true;
        }
        if (app()->environment() === 'test' && !config('onix.test_server_enable_queues')) {
            return true;
        }
        return false;
    }

    public function onixGet($url, $cache_to_json = null)
    {
        return $this->onixRequest('GET', $url, null, $cache_to_json);
    }

    public function onixPost($url, $options = [])
    {
        return $this->onixRequest('POST', $url, $options, null);
    }

    public function onixRequest($method, $url, $options = null, $cache_to_json = null)
    {
        $cache_to_json_path = 'onix/json/' . $cache_to_json;

        if ($cache_to_json && !$this->isOnline()) {
            if (Storage::exists($cache_to_json_path)) {
                $this->logger->info("OFFLINE: " . $cache_to_json_path . '[' . $url . ']');
                echo "OFFLINE: " . $cache_to_json_path . '[' . $url . ']' . "\n";
                $data = json_decode(Storage::get($cache_to_json_path));
                $this->logger->info('Data count: ' . count($data));
                return $data;
            }
        }

        $start_time = microtime(true);
        $values = [
            'method' => $method,
            'request' => $url,
            'request_data' => json_encode($options),
            'created_at' => date('Y-m-d H:i:s'),
            'onix_user' => $this->getOnixUsername()
        ];
        if ($method === 'POST' && $url === (config('onix.api_path') . config('onix.paths.orders'))) {
            $values['order_id'] = $options[RequestOptions::JSON]['Guid_Ext'];
        }
        $request_id = DB::table('onix_requests')->insertGetId($values);

        if (!$this->isOnline()) {
            dd('offline');
        }

        switch ($method) {
            case 'GET':
                $this->logger->info('ONIX GET request:' . $url);
                $response = $this->get($url);
                break;
            case 'POST':
                $this->logger->info('ONIX POST request:' . $url . ' data: ' . json_encode($options));
                $response = $this->request('POST', $url, $options);
                $cache_to_json = null;
                break;
            default:
                $this->logger->info('Unknown http method ' . $method);
                throw new \Exception('Unknown http method ' . $method);
        }
        $end_time = microtime(true);

        $log = [
            'response_time' => $end_time - $start_time,
            'http_status_code' => $response->getStatusCode(),
            'content_length' => (int)($response->getHeader('Content-length')[0]),
        ];

        $body = '' . $response->getBody();

        if ((int)$log['content_length'] < 1000) {
            $log['result_data'] = $body;
        }

        $result_data = json_decode($body);
        if (isset($result_data->Result)) {
            $log['result_code'] = $result_data->Result;
        }
        if (isset($result_data->Errors) && is_array($result_data->Errors) && count($result_data->Errors)) {
            $log['errors'] = json_encode($result_data->Errors);
        }
        if (isset($result_data->Warnings) && is_array($result_data->Warnings) && count($result_data->Warnings)) {
            $log['warnings'] = json_encode($result_data->Warnings);
        }

        DB::table('onix_requests')->where('id', '=', $request_id)->update($log);

        $this->logger->info(json_encode($log));

        if ($log['http_status_code'] !== 200) {
            return false;
        }

        if ($cache_to_json) {
            Storage::put($cache_to_json_path, $body);
        }

        return $result_data;
    }

    function getStocks()
    {
        return $this->onixGet(config('onix.api_path') . 'stocks', 'cached_getStocks.json');
    }

    function updatePartners()
    {
        $url = config('onix.api_path') . 'partners';
        // todo:
    }

    function getPartners($onixPartnerId = null)
    {
        $url = config('onix.api_path') . 'partners?tables=ALL';

        if ($onixPartnerId) {
            $url .= '&Ns_Number=' . $onixPartnerId;
        }

        $data = $this->onixGet($url, 'cached_getPartners.json');

        if ($onixPartnerId) {
            foreach ($data as $k => $v) {
                if ($v->Ns_Number != $onixPartnerId) {
                    unset($data[$k]);
                }
            }
        }

        return $data;
    }

    function getStockItems()
    {
        $url = config('onix.api_path') . 'stockitems?tables=PARAMS&tables=PROPERTIES&tables=CODES&tables=GROUPS&tables=PARTNERS&StockCode=' . implode('&StockCode=', config('onix.stock_codes'));

        $data = $this->onixGet($url, 'getStockItems-' . Carbon::now()->toDateString() . '.json');

        if (is_array($data)) {
            $this->logger->info('Products count: ' . count($data));
        }
        return $data;
    }

    function getOrders($onix_partner_id, $todayOnly = false)
    {
        $url = config('onix.api_path') . config('onix.paths.orders') . '?tables=ALL&Ns_Number_Partner=' . $onix_partner_id;
        if ($todayOnly) {
            $date = date('Y-m-d');
            $url .= '&Date_Changed=' . $date;
        }
        return $this->onixGet($url, 'getOrders' . $onix_partner_id . '.json');
    }

    /**
     * @param $onix_partner_id
     * @param $days
     * @return array
     */
    function getInvoices($onix_partner_id = null, $days = 'today')
    {
        return $this->getDocuments('invoices', $onix_partner_id, $days);
    }

    /**
     * @param $onix_partner_id
     * @param $days
     * @return array
     */
    function getDeliveryNotes($onix_partner_id = null, $days = 'today')
    {
        return $this->getDocuments('delivery_notes', $onix_partner_id, $days);
    }

    function getCreditNotes($onix_partner_id = null, $days = 'today')
    {
        return $this->getDocuments('credit_notes', $onix_partner_id, $days);
    }

    function getDocuments($onixPathConfigKey, $onixPartnerId, $days = 'today')
    {
        $url = config('onix.api_path') . config('onix.paths.' . $onixPathConfigKey) . '?tables=ALL';
        if ($onixPartnerId !== null) {
            $url .= '&Ns_Number_Partner=' . $onixPartnerId;
        }
        if ($days !== null) {
            if ($days === 'today') {
                $t = time();
                $days = [date('Y-m-d', $t), date('Y-m-d', $t - 3600 * 24)];
            }
            if (is_array($days)) {
                $documents = [];
                foreach ($days as $day) {
                    $tmp = $this->getDocuments($onixPathConfigKey, $onixPartnerId, $day);
                    foreach ((array)$tmp as $v) {
                        $documents[] = $v;
                    }
                }
                return $documents;
            }
            $days = date('Y-m-d', strtotime($days));
            $url .= '&Date_Changed=' . $days;
        }

        return $this->onixGet($url, 'get' . $onixPathConfigKey . '_' . $onixPartnerId . '.json');
    }

    function getEnclosure($idRecord)
    {
        $url = config('onix.api_path') . config('onix.paths.enclosures') . '?idRecord=' . $idRecord;

        return $this->onixGet($url, null);
    }

    function getEnclosureContent($idRecord)
    {
        $enclosure = $this->getEnclosure($idRecord);
        return (base64_decode($enclosure->Enclosure));
    }

    function getOrdersForDay($date = null, $onix_partner_id = null)
    {
        if ($date === null) {
            $t = time();
            $date = date('Y-m-d', $t);
            if ((int)date('G') < 6) {
                $orders = array_merge(
                    $this->getOrdersForDay($date, $onix_partner_id),  // this day
                    $this->getOrdersForDay(date('Y-m-d', strtotime($date) - 12 * 3600), $onix_partner_id) // previous day
                );
                return $orders;
            }
        }
        $url = config('onix.api_path') . config('onix.paths.orders') . '?tables=ALL&Date_Changed=' . $date;
        if ($onix_partner_id !== null) {
            $url .= '&Ns_Number_Partner=' . $onix_partner_id;
        }
        return $this->onixGet($url, 'getOrders-' . $date . '.json');
    }

    function sendOrder($onix_partner_id, $data)
    {
        $url = config('onix.api_path') . config('onix.paths.orders');

        $data['Ns_Number_Partner'] = $onix_partner_id;

        return $this->onixPost($url, [RequestOptions::JSON => $data]);
    }

    /**
     * @param \Buxus\Eshop\Order\OrderInterface $buxus_order
     * @param $onix_order
     */
    function updateBuxusOrder($buxus_order, $onix_order, Logger $logger)
    {
//        if(isset($onix_order->Date_Changed) && strlen($onix_order->Date_Changed)
//            && $onix_order->Date_Changed === $buxus_order->getData('onix_date_changed')) {
//            if($buxus_order->getOrderState() === $this->onixStateToBuxusStateChange($onix_order->Document_State)) {
//                echo ' skip order (' . $buxus_order->getData('onix_date_changed') . ' / ' . $onix_order->Date_Changed . ')' . "\n";
//                return;
//            }
//        }

        $buxus_order->setData('onix_order_id', $onix_order->IdRecord);
        $buxus_order->setData('onix_ns_number', $onix_order->Ns_Number);
        $buxus_order->setData('onix_vs', $onix_order->Variable_Symbol);
        $buxus_order->setData('onix_partner_id', $onix_order->Ns_Number_Partner);
        $buxus_order->setData('onix_date_changed', $onix_order->Date_Changed);

        $this->updateBuxusOrderCustomData($buxus_order, $onix_order);


        // apply order state, if defined
        $new_buxus_state = $this->onixStateToBuxusStateChange($onix_order->Document_State);
        if ($new_buxus_state !== null) {
            $this->updateBuxusOrderState($buxus_order, $new_buxus_state, $onix_order);
        } else {
            $logger->info('Unknown onix order state: ' . $onix_order->Document_State);
        }

        // fakturacne udaje
        $buxus_order->setIco($onix_order->Partner_Reg);
        $buxus_order->setDic($onix_order->Partner_Tax);
        $buxus_order->setInvoiceCompanyName($onix_order->Partner_Name);
        $buxus_order->setInvoiceStreet($onix_order->Partner_Street . ' ' . $onix_order->Partner_Street_No);
        $buxus_order->setInvoiceCity($onix_order->Partner_City);
        $buxus_order->setInvoiceZip($onix_order->Partner_Postcode);
        $buxus_order->setInvoiceCountry($onix_order->Partner_State);
        $buxus_order->setPaidPrice($onix_order->Sum_Vat);
        $buxus_order->save();

        $this->updateBuxusOrderItems($buxus_order, $onix_order);
    }

    /**
     * @param \Buxus\Eshop\Order\OrderInterface $buxus_order
     * @param $new_buxus_state
     * @param $onix_order
     */
    protected function updateBuxusOrderState($buxus_order, $new_buxus_state, $onix_order)
    {
        $buxus_order->setOrderState($new_buxus_state);
    }

    /**
     * @param \Buxus\Eshop\Order\OrderInterface $buxus_order
     * @param $onix_order
     */
    protected function updateBuxusOrderCustomData($buxus_order, $onix_order)
    {

    }

    function onixStateToBuxusStateChange($onix_state)
    {
        return NULL;
    }

    protected function getLabelOfTransportOnixItem()
    {
        return 'DOPRAVA';
    }

    /**
     * @param \Buxus\Eshop\Order\OrderInterface $buxus_order
     * @param $onix_order
     */
    protected function updateBuxusOrderItems($buxus_order, $onix_order)
    {
        $buxus_items = $buxus_order->getItems();
        $unprocessed_buxus_items_keys = array_flip(array_keys($buxus_items));
        foreach ($onix_order->Items as $onix_item) {
            if ($onix_item->Item_Name === $this->getLabelOfTransportOnixItem()) {
                continue;
            }

            $item_name = $this->getFullOnixItemName($onix_item, $buxus_order->getSite());

            $key = $this->findBuxusOrderItemKey($onix_item, $buxus_items);
            if ($key !== null) {
                if (!$buxus_items[$key]->getOption('onix_order_item_id')) {
                    $buxus_items[$key]->setOption('onix_order_item_id', $onix_item->IdRecord);
                }

                $buxus_items[$key]->setOrderItemName($item_name);
                $buxus_items[$key]->setItemPriceType(OrderItem::PRICE_TYPE_INCLUDING_VAT);
                if ($onix_item->Unit_Price) {
                    $buxus_items[$key]->setVatRatePercent(($onix_item->Unit_Price_Vat / $onix_item->Unit_Price) * 100 - 100);
                }
                $buxus_items[$key]->setAmount($onix_item->User_Amount);
                $buxus_items[$key]->setItemPrice($onix_item->Unit_Price_Vat);
                $customColumns = $this->getCustomColumnsFromOnixOrderItem($onix_item);
                if (isset($customColumns['state'])) {
                    $buxus_items[$key]->setOption('item_state', $customColumns['state']);
                }
                unset($unprocessed_buxus_items_keys[$key]);
            } else {
                // new items
                $product = null;
                if ($onix_item->Stock_Items_Ns_Number) {
                    $product = \PageFactory::builder()
                        ->wherePageType($this->getProductPageTypes())
                        ->wherePropertyValue(PropertyID::ONIX_NS_NUMBER_ID(), '=', $onix_item->Stock_Items_Ns_Number)
                        ->first();
                }
                if (!$product) {
                    $product = \PageFactory::builder()
                        ->wherePageType($this->getProductPageTypes())
                        ->wherePropertyValue(PropertyID::TITLE_ID(), '=', $onix_item->Stock_Items_Name)
                        ->first();
                }
                if (!$product) {
                    $product = \PageFactory::builder()
                        ->wherePageType($this->getProductPageTypes())
                        ->wherePropertyValue(PropertyID::TITLE_ID(), '=', $item_name)
                        ->first();
                }
                if (!$product) {
                    $product_id = $this->getUndefinedProductPageId();
                } else {
                    $product_id = $product->getPageId();
                }

                $new_item = $this->newItemObject();
                $data = [
                    'page_id' => $product_id,
                    'order_item_name' => $item_name,
                    'nr_of_items' => $onix_item->User_Amount,
                    'price_per_item' => $onix_item->Unit_Price_Vat,
                ];

                try {
                    $vatPerItem = ($onix_item->Unit_Price_Vat / $onix_item->Unit_Price) * 100 - 100;
                } catch (\Throwable $e) {
                    $vatPerItem = 0;
                }

                $data['vat_per_item'] = $vatPerItem;

                $new_item->hydrateData($data);
                if (isset($customColumns['state'])) {
                    $new_item->setOption('item_state', $customColumns['state']);
                }
                $new_item->setOption('onix_order_item_id', $onix_item->IdRecord);

                $buxus_items[] = $new_item;
            }
        }


        if (count($unprocessed_buxus_items_keys)) {
            // delete items
            foreach ($unprocessed_buxus_items_keys as $key => $v) {
                unset($buxus_items[$key]);
            }
        }

        $buxus_order->setItems($buxus_items);
        $buxus_order->save();
    }

    protected function newItemObject()
    {
        return new OrderItem();
    }

    protected function getProductPageTypes()
    {
        return [PageTypeID::ESHOP_PRODUCT_ID()];
    }

    protected function getUndefinedProductPageId()
    {
        return null;
    }

    protected function getCustomColumnsFromOnixOrderItem($onix_item)
    {
        $customColumns = [];
        foreach ($onix_item->CustomColumns as $custom_column) {
            $customColumns[$custom_column->Name] = $custom_column->Value;
        }
        return $customColumns;
    }

    protected function getFullOnixItemName($onix_item, $buxusOrderSite)
    {
        return $onix_item->Stock_Items_Name;
    }

    /**
     * @param $onix_order
     * @param OrderItem[] $buxus_items
     */
    private function findBuxusOrderItemKey($onix_order_item, $buxus_items)
    {
        foreach ($buxus_items as $key => $buxus_item) {
            $onix_order_item_id = $buxus_item->getOption('onix_order_item_id');
            if (!$onix_order_item_id) {
                continue;
            }
            if ($onix_order_item_id == $onix_order_item->IdRecord) {
                return $key;
            }
        }

        foreach ($buxus_items as $key => $buxus_item) {
            try {
                $onixNsNumber = $this->getOnixNsNumber($buxus_item);
            } catch (\Throwable $e) {
                $this->logger->error('Failed: ' . $e->getMessage() . ' ' . $buxus_item->getOrderItemId());
                ErrorReporter::reportSilent($e);
                throw $e;
            }
            $basePrice = round($buxus_item->getItemPriceWithoutVAT(), 2);
            if (empty($onixNsNumber) || !$basePrice) {
                continue;
            }
            if ($onixNsNumber === $onix_order_item->Stock_Items_Ns_Number
                && $basePrice === round($onix_order_item->Unit_Price_Base, 2)
            ) {
                return $key;
            }
        }

        return null;
    }

    protected function getOnixNsNumber($buxus_item)
    {
        $page = $buxus_item->getPage();
        if (!$page) {
            return null;
        }
        if (!empty($page->getValue(PropertyTag::ONIX_NS_NUMBER_TAG()))) {
            return $page->getValue(PropertyTag::ONIX_NS_NUMBER_TAG());
        }
        return null;
    }

}
