<?php

namespace App\Form\Events;

use App\Form\Manager\FormManager;

class FormUpdatedEventHandler
{
    /** @var FormManager $manager */
    protected $manager;

    public function __construct()
    {
        $this->manager = new FormManager();
    }

    public function handle(FormUpdatedEvent $event)
    {
        $form = $event->getForm();

        $this->manager->sendUpdateMail($form);
    }
}
