<?php

namespace App\Form\Events;

use Buxus\Event\Event;

class FormUpdatedEvent extends Event
{
    protected $form;

    public function __construct($form)
    {
        $this->form = $form;
    }

    /**
     * @return mixed
     */
    public function getForm()
    {
        return $this->form;
    }

    /**
     * @param mixed $form
     */
    public function setForm($form): void
    {
        $this->form = $form;
    }
}
