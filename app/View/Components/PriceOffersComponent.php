<?php

namespace App\View\Components;

use App\PriceOffers\PriceOfferManager;
use Illuminate\View\Component;

class PriceOffersComponent extends Component
{
    protected $priceOffers;

    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct()
    {
        $manager = new PriceOfferManager();
        $this->priceOffers = $manager->getPriceOffersForUser();
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\Contracts\View\View|\Closure|string
     */
    public function render()
    {
        if (empty($this->priceOffers)) {
            return '';
        }

        return view('components.price-offers-component', [
            'priceOffers' => $this->priceOffers,
        ]);
    }
}
