<?php

namespace App\Export;


use Buxus\Util\PropertyTag;
use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithProperties;

class AugustinGroupExport implements FromArray, WithChunkReading
{
    protected $items;

    public function __construct($items)
    {
        $user = \WebUserFactory::getById(config('exports.augustin_group.user_id'));

        foreach ($items as $item) {
            /** @var \App\Eshop\Product $item * */
            $page = $item->getPage();

            $onixNsNumber = $page->getValue(PropertyTag::ONIX_NS_NUMBER_TAG());
            $productCode = $item->getProductCode();
            $productTitle = $item->getProductNameEnWithoutCode();
            $producer = $item->getProducer();
            $availability = $item->getAvailabilityForAugustinGroup();
            $price = $item->getFinalPriceWithoutVatForWebUser($user);

            if (!empty($price)) {
                $this->items[] = [
                    $onixNsNumber,
                    $productCode,
                    $productTitle,
                    $producer,
                    $availability,
                    $price,

                ];
            }
        }
    }

    public function array(): array
    {
        return $this->items;
    }

    public function chunkSize(): int
    {
        return 1000;
    }
}
