<?php
namespace App\Page;

use Buxus\Page\PagesQueryBuilder;
use Buxus\Property\PropertyInterface;

class AppPagesQueryBuilder extends PagesQueryBuilder
{

    /**
     * @param PropertyInterface|int|string $property
     *
     * @return PagesQueryBuilder
     */
    public function whereNotEmptyPropertyValue($property): PagesQueryBuilder
    {
        $propertyId = $this->normalizeProperty($property);
        $propertyAlias = "property_{$propertyId}";

        $this->leftJoinProperty($property);
        $this->whereNotNull("{$propertyAlias}.property_value");
        $this->where("{$propertyAlias}.property_value", '<>', '');

        return $this;
    }


    /**
     * @param PropertyInterface|int|string $property
     * @param null $operator
     * @param mixed $value
     *
     * @param string $boolean
     *
     * @return PagesQueryBuilder
     *
     * @example wherePropertyValue('title', 'Buxus')
     * @example wherePropertyValue('title', '<>', 'Buxus')
     * @example wherePropertyValue('price', '>=', 4)
     */
    public function wherePropertyValue($property, $operator = null, $value = null, $boolean = 'and', $parentBuilder = null, $join = 'inner'): PagesQueryBuilder
    {
        // Here we will make some assumptions about the operator. If only 2 values are
        // passed to the method, we will assume that the operator is an equals sign
        // and keep going. Otherwise, we'll require the operator to be passed in.
        list($value, $operator) = $this->prepareValueAndOperator(
            $value,
            $operator,
            func_num_args() === 2
        );

        $propertyId = $this->normalizeProperty($property);
        $propertyAlias = "property_{$propertyId}";

        /**
         * Include pages where property_value is non-present in `tblPagePropertyValues`
         */
        if (in_array($operator, ['<>', '!='])) {
            $this->leftJoinProperty($property, $parentBuilder);

            return $this->where(static function (PagesQueryBuilder $query) use (
                $propertyAlias,
                $operator,
                $value,
                $boolean
            ) {
                $query->where("{$propertyAlias}.property_value", $operator, $value, $boolean);

                if ($value !== null) {
                    $query->orWhereNull("{$propertyAlias}.property_value");
                }
            });
        }

        if ($join == 'left') {
            $this->leftJoinProperty($property, $parentBuilder);
        } else {
            $this->joinProperty($property, $parentBuilder);
        }

        $this->where("{$propertyAlias}.property_value", $operator, $value, $boolean);

        return $this;
    }

    /**
     * @param PropertyInterface|int|string $property
     * @param null $operator
     * @param mixed $value
     *
     * @param string $boolean
     *
     * @return PagesQueryBuilder
     *
     * @example wherePropertyValue('title', 'Buxus')
     * @example wherePropertyValue('title', '<>', 'Buxus')
     * @example wherePropertyValue('price', '>=', 4)
     */
    public function orWherePropertyValue($property, $operator = null, $value = null, $boolean = 'and', $parentBuilder = null): PagesQueryBuilder
    {
        // Here we will make some assumptions about the operator. If only 2 values are
        // passed to the method, we will assume that the operator is an equals sign
        // and keep going. Otherwise, we'll require the operator to be passed in.
        list($value, $operator) = $this->prepareValueAndOperator(
            $value,
            $operator,
            func_num_args() === 2
        );

        $propertyId = $this->normalizeProperty($property);
        $propertyAlias = "property_{$propertyId}";

        /**
         * Include pages where property_value is non-present in `tblPagePropertyValues`
         */
        if (in_array($operator, ['<>', '!='])) {
            $this->leftJoinProperty($property, $parentBuilder);

            return $this->orWhere(static function (PagesQueryBuilder $query) use (
                $propertyAlias,
                $operator,
                $value,
                $boolean
            ) {
                $query->where("{$propertyAlias}.property_value", $operator, $value, $boolean);

                if ($value !== null) {
                    $query->orWhereNull("{$propertyAlias}.property_value");
                }
            });
        }

        $this->leftJoinProperty($property, $parentBuilder);

        $this->orWhere("{$propertyAlias}.property_value", $operator, $value, $boolean);

        return $this;
    }



    /**
     * @param PropertyInterface|int|string $property
     *
     * @return PagesQueryBuilder
     */
    public function whereEmptyPropertyValue($property, $parentBuilder = null): PagesQueryBuilder
    {
        $propertyId = $this->normalizeProperty($property);
        $propertyAlias = "property_{$propertyId}";

        $this->leftJoinProperty($property, $parentBuilder);

        return $this->where(static function ($query) use ($propertyAlias) {
            $query->where("{$propertyAlias}.property_value", '=', '')
                ->orWhereNull("{$propertyAlias}.property_value");
        });
    }


    /**
     * @param PropertyInterface|int|string $property
     * @param array $values
     * @param string $boolean
     * @param bool $not
     *
     * @return PagesQueryBuilder
     */
    public function wherePropertyValueIn($property, array $values, $boolean = 'and', $not = false, $parentBuilder = null): PagesQueryBuilder
    {
        $propertyId = $this->normalizeProperty($property);
        $propertyAlias = "property_{$propertyId}";

        $this->joinProperty($property, $parentBuilder);

        return $this->whereIn("{$propertyAlias}.property_value", $values, $boolean, $not);
    }


    protected function joinProperty($property, $parentBuilder = null): void
    {
        $propertyId = $this->normalizeProperty($property);

        if (!isset($this->propertyJoinCache[$propertyId])) {
            $propertyAlias = "property_{$propertyId}";

            $builder = $this;
            if ($parentBuilder) {
                $builder = $parentBuilder;
            }

            $builder->join(
                "tblPagePropertyValues as {$propertyAlias}",
                static function ($join) use ($propertyId, $propertyAlias) {
                    $join->on('page.page_id', '=', "{$propertyAlias}.page_id")
                        ->where("{$propertyAlias}.property_id", '=', $propertyId);
                }
            );

            $this->propertyJoinCache[$propertyId] = true;
        }

    }

    protected function leftJoinProperty($property, $parentBuilder = null): void
    {
        $propertyId = $this->normalizeProperty($property);

        if (!isset($this->propertyJoinCache[$propertyId])) {
            $propertyAlias = "property_{$propertyId}";

            $builder = $this;
            if ($parentBuilder) {
                $builder = $parentBuilder;
            }

            $builder->leftJoin(
                "tblPagePropertyValues as {$propertyAlias}",
                static function ($join) use ($propertyId, $propertyAlias) {
                    $join->on('page.page_id', '=', "{$propertyAlias}.page_id")
                        ->where("{$propertyAlias}.property_id", '=', $propertyId);
                }
            );

            $this->propertyJoinCache[$propertyId] = true;
        }
    }

}
