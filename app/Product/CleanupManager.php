<?php

namespace App\Product;

use Buxus\Logger\Logger;
use Buxus\Page\PageInterface;
use Buxus\Util\PageTypeID;
use Buxus\Util\PropertyTag;
use Exception;
use Page;

class CleanupManager
{
    /**
     * @param PageInterface|int $page
     * @return null|PageInterface
     */
    public static function removeProductPairing($page, Logger $logger = null): ?PageInterface
    {
        if (is_numeric($page)) {
            $page = \PageFactory::get($page);
        }

        if (!$page instanceof PageInterface) {
            return null;
        }
        if(empty($page->getValue(PropertyTag::ONIX_NS_NUMBER_TAG()))) {
            return $page;
        }

        if (!empty($page->getValue(PropertyTag::AUGUSTIN_GROUP_NUMBER_TAG()))) {
            $logger->info('Cleanup pairing for AG page ' . $page->getPageId());
            $childPage = \PageFactory::create($page->getParentPageId(), PageTypeID::ESHOP_PRODUCT_ID);
            $childPage->setPageName($page->getPageName());
            $childPage->setValue(PropertyTag::TITLE_TAG(), $page->getValue(PropertyTag::TITLE_TAG()));
            $childPage->setValue(PropertyTag::AUGUSTIN_GROUP_NUMBER_TAG(), $page->getValue(PropertyTag::AUGUSTIN_GROUP_NUMBER_TAG()));
            $childPage->setValue(PropertyTag::AUGUSTIN_GROUP_OE_NUMBER_TAG(), $page->getValue(PropertyTag::AUGUSTIN_GROUP_OE_NUMBER_TAG()));
            $childPage->setValue(PropertyTag::AUGUSTIN_GROUP_OE_NUMBERS_TAG(), $page->getValue(PropertyTag::AUGUSTIN_GROUP_OE_NUMBERS_TAG()));
            $childPage->setValue(PropertyTag::AUGUSTIN_GROUP_LATEST_IMPORT_TAG(), $page->getValue(PropertyTag::AUGUSTIN_GROUP_LATEST_IMPORT_TAG()));
            $childPage->setValue(PropertyTag::AUGUSTIN_GROUP_PRICE_WITHOUT_VAT_TAG(), $page->getValue(PropertyTag::AUGUSTIN_GROUP_PRICE_WITHOUT_VAT_TAG()));
            $childPage->setValue(PropertyTag::AUGUSTIN_GROUP_STOCK_BALANCE_TAG(), $page->getValue(PropertyTag::AUGUSTIN_GROUP_STOCK_BALANCE_TAG()));
            $childPage->save(false);
            $logger->info('Created new AG page ' . $childPage->getPageId() . ' for original page ' . $page->getPageId() . '.');

            $page->setValue(PropertyTag::AUGUSTIN_GROUP_NUMBER_TAG(), null);
            $page->setValue(PropertyTag::AUGUSTIN_GROUP_OE_NUMBER_TAG(), null);
            $page->setValue(PropertyTag::AUGUSTIN_GROUP_OE_NUMBERS_TAG(), null);
            $page->setValue(PropertyTag::AUGUSTIN_GROUP_LATEST_IMPORT_TAG(), null);
            $page->setValue(PropertyTag::AUGUSTIN_GROUP_PRICE_WITHOUT_VAT_TAG(), null);
            $page->setValue(PropertyTag::AUGUSTIN_GROUP_STOCK_BALANCE_TAG(), null);
            $page->save(false);

            return $page;
        }

        return $page;
    }

    /**
     * @param int[] $pageIds
     * @return array
     * @throws Exception
     */
    public static function removeProductsPairing($pageIds, Logger $logger = null): array
    {
        $pages = [];

        foreach ($pageIds as $pageId) {
            $pages[] = self::removeProductPairing($pageId, $logger);
        }

        if (count($pages) > 1) {
            $onixNSNumbers = array_unique(array_map(function (PageInterface $page) {
                return $page->getValue(PropertyTag::ONIX_NS_NUMBER_TAG());
            }, $pages));

            if(count($onixNSNumbers) > 1) {
                throw new Exception('More than one ONIX NS number was found (' . implode(',', $onixNSNumbers) . ') page_ids=' . implode(',', $pageIds));
            }
        }

        return $pages;
    }
}
