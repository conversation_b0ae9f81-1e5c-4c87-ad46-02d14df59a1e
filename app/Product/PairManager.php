<?php

namespace App\Product;

use Buxus\Page\PageInterface;
use Buxus\Util\PropertyTag;
use BuxusDB;

class PairManager
{
    public function getProductPageByMultipleCodesAndProducer($codes, $producer)
    {
        foreach ((array)$codes as $code) {
            $pages = $this->getProductPageByCode($code);
            foreach ($pages as $page) {
                if ($page->getValue(PropertyTag::ESHOP_ROLLER_PRODUCER_TAG()) == $producer) {
                    return $page;
                }
            }
        }

        return;
    }

    public function getProductPageByCode($code)
    {
        $pages = \PageFactory::builder()
            ->wherePropertyValue(PropertyTag::ONIX_MAIN_CODE_TAG(), $code)
            ->get()->toArray();

        $pagesSql = $this->searchByOnixCodes($code);
        foreach ($pagesSql as $pageSql) {
            $pages[] = \PageFactory::get($pageSql['page_id']);
        }

        return $pages;
    }

    public function searchByOnixCodes($code)
    {
        $sql = "SELECT * FROM
             tblPagePropertyValues WHERE property_id = 129 AND CONCAT(', ', property_value, ',') like '%, {$code},%'";

        $result = BuxusDB::get()->fetchAll($sql);

        return $result;
    }
}
