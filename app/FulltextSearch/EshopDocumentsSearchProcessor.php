<?php

namespace App\FulltextSearch;

use FullTextSearch\Provider\SolrSuggestProvider;
use Solarium\QueryType\Select\Query\Query as SelectQuery;

class EshopDocumentsSearchProcessor extends SolrSuggestProvider
{
    protected function processQuery(SelectQuery $query, $originalTerm, $options = null)
    {
        $query->addFields([
            'id',
            'page_id',
            'page_type_id',
            'score',

            'enclosure_record_id_fi',
            'inserted_fd',
            'onix_date_changed_fd',
            'onix_date_document_fd',
            'partner_id_fi',
            'enclosure_type_id_fi',
            'path_ft',
            'code_ft',
            'vs_ft',
            'sum_ff',
            'curr_ft',
            'extension_ft',
            'commission_ft',
            'doc_name_ft',
            'due_date_fd',
            'payment_remaining_ff',
            'payment_status_ff',
            'sum_vat_ff',
            'external_number_ft',
            'item_names_ftm',
            'item_codes_ftm',
            'buxus_order_vs_ftm',
            'buxus_order_ids_ftm',
            'invalid_products_ftm',
            'ignored_external_codes_ftm',
        ]);

        $params = $this->getBasicQueryParts($query);

        if(!empty($options['onix_partner_id'])) {
            $params[] = 'partner_id_fi:' . (int)$options['onix_partner_id'];
        }
        else {
            $user = \WebUserAuthentication::getUser();
            $partnerId = $user->getOnixPartnerId();
            $params[] = 'partner_id_fi:' . (int)$partnerId;
        }

        if (!empty($options['enclosure_type_id'])) {
            $params[] = 'enclosure_type_id_fi:' . $options['enclosure_type_id'];
        }
        if (!empty($options['after_due_date'])) {
            $params[] = 'due_date_fd:[* TO NOW}';
            $params[] = 'payment_remaining_ff:{0 TO *]';
        }
        if (!empty($options['vs_search'])) {
            $params[] = 'vs_ft:*' . $options['vs_search'].'*';
        }
        if (!empty($options['product_search'])) {
            //$params[] = '(item_names_ftm:*' . $options['product_search'].'* OR item_codes_ftm:*' . $options['product_search'].'*)';
            $params[] = '(item_codes_ftm:*' . $options['product_search'].'*)';
        }

        if (!empty($options['page']) && !empty($options['per_page'])) {
            $query->setStart($options['page'] * $options['per_page'] - $options['per_page']);
            $query->setRows($options['per_page']);
        }

        $query->addSort('onix_date_document_fd', $query::SORT_DESC);
//dd($params);
        $query->createFilterQuery('basicParts')->setQuery(implode(' AND ', $params));
    }

}
