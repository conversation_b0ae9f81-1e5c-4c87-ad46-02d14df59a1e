<?php

namespace App\FulltextSearch\Filters;

use App\Eshop\Product;
use Arr;
use Countable;

class AugustinGroup
{
    public function filter($products): array
    {
        $this->products = $products;
        $this->initFilterableProductData();
        $products = $this->products;
        $productsByKeyAndProducer = $this->sortProductsByFilterableData($products);

        $pageIdsToUnset = [];

        foreach ($productsByKeyAndProducer as $i => $products) {
            usort($products, function ($a, $b) {
                return $b->is_augustin_group <=> $a->is_augustin_group;
            });

            $combinations = new \drupol\phpermutations\Generators\Combinations($products, 2);

            foreach ($combinations->generator() as $combination) {
                if (count($combination) !== 2) {
                    continue;
                }
                $pageIdsToUnset[] = $this->getPageIdsToUnset($combination);
            }
        }

        $pageIdsToUnset = array_unique(Arr::flatten($pageIdsToUnset));

        foreach ($productsByKeyAndProducer as $i => $products) {
            foreach ($products as $j => $product) {
                if (in_array($product->getPageId(), $pageIdsToUnset)) {
                    unset($products[$j]);
                }
            }

            $productsByKeyAndProducer[$i] = $products;
        }

        return Arr::flatten($productsByKeyAndProducer);
    }

    protected function sortProductsByFilterableData($products)
    {
        $productsByFilterableData = [];
        /** @var Product $product */
        foreach ($products as $product) {
            $productsByFilterableData[$product->getProductCode() . '_' . $product->getProducer()][] = $product;
        }

        return $productsByFilterableData;
    }

    protected function initFilterableProductData()
    {
        /** @var Product $product */
        foreach ($this->products as $product) {
            $product->price = $product->getFinalPriceWithoutVatValue();
            $product->availability = $this->getProductAvailabilityInfo($product);
            $product->is_augustin_group = $product->isAugustinGroup();
            $product->is_onix = $product->isOnixProduct();
        }
    }

    protected function getProductAvailabilityInfo($product): bool
    {
        return $product->getStockBalance() > 0;
    }

    protected function getPageIdsToUnset($combination)
    {
        $firstProduct = $combination[array_key_first($combination)];
        $secondProduct = $combination[array_key_last($combination)];

        if ($firstProduct->is_augustin_group && $secondProduct->is_augustin_group) {
            return [];
        }

        if ($firstProduct->is_augustin_group) {
            if ($firstProduct->price > $secondProduct->price) {
                return [$firstProduct->getPageId()];
            }
        }

        if ($secondProduct->is_augustin_group) {
            if ($secondProduct->price > $firstProduct->price) {
                return [$secondProduct->getPageId()];
            }
        }

        return [];
    }
}
