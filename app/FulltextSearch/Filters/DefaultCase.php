<?php

namespace App\FulltextSearch\Filters;

use App\Eshop\Catalog\Product\ProductGroup;
use App\Eshop\Product;
use Buxus\Util\PropertyTag;

class DefaultCase
{
    protected $products;
    protected $productsWithoutSupplierCode;
    protected $productsWithSupplierCode;
    protected $hiddenProducts;
    protected $resultProducts;
    protected $unpairedProducts;
    protected $onixProducts;
    protected $onixMinimumPrice;

    public function __construct($products)
    {
        $this->products = $products;
        $this->hiddenProducts = new ProductGroup();
        $this->unpairedProducts = new ProductGroup();
        $this->resultProducts = new ProductGroup();
        $this->onixProducts = new ProductGroup();
    }

    public function filter(): ProductGroup
    {
        $this->initItems();

        return $this->resultProducts;
    }

    protected function initItems()
    {
        $this->productsWithoutSupplierCode = new ProductGroup();
        $this->productsWithSupplierCode = new ProductGroup();

        foreach ($this->getProducts() as $product) {
            if ($product->isOnixProduct()) {
                $this->onixMinimumPrice = $this->onixMinimumPrice == null || $this->onixMinimumPrice > $product->getFinalPriceWithoutVatValue()
                    ? $product->getFinalPriceWithoutVatValue()
                    : $this->onixMinimumPrice;
            }

            if (empty($product->getSupplierCodes())) {
                $this->unpairedProducts->addProduct($product);
            } else {
                $this->productsWithSupplierCode->addProduct($product);
            }
        }


        $buffer = [];

        foreach ($this->productsWithSupplierCode->getProducts() as $product) {
            $buffer[$product->getPageId()] = [
                'is_onix' => $product->isOnixProduct(),
                'supplier_codes' => $product->getSupplierCodes(),
                'price' => $product->getFinalPriceWithoutVatValue(),
                'stock' => $product->getFullStockBalance(),
                'product' => $product,
            ];

            if ($product->hasCeiSupplierCode()) {
                $supplierCodes = $buffer[$product->getPageId()]['supplier_codes'];
                $supplierCodesWithAndWithoutDot = [];

                foreach ($supplierCodes as $supplierCode) {
                    $supplierCodesWithAndWithoutDot[] = $supplierCode;
                    $supplierCodesWithAndWithoutDot[] = str_replace('.', '', $supplierCode);
                }

                $buffer[$product->getPageId()]['supplier_codes'] = array_filter(array_unique($supplierCodesWithAndWithoutDot));
            }
        }

        $productsBySupplierCode = [];

        foreach ($buffer as $item) {
            foreach ($item['supplier_codes'] as $supplierCodes) {
                $supplierCodes = explode(',', $supplierCodes);
                foreach ($supplierCodes as $supplierCode) {
                    $productsBySupplierCode[$supplierCode] = $this->getProductsBySupplierCode($buffer, $supplierCode);
                }
            }
        }

        uasort($productsBySupplierCode, function ($a, $b) {
            return count($b) <=> count($a);
        });

        foreach ($productsBySupplierCode as $k => $products) {
            if (count($products) <= 1) {
                foreach ($products as $product) {
                    if (!$this->shouldBeProcessedAsPair($product, $productsBySupplierCode)) {
                        $this->unpairedProducts->addProduct($product['product']);
                        unset($productsBySupplierCode[$k]);
                        continue;
                    }
                }
            } else {
                $this->processPairedProducts($products);
            }
        }

        $this->processUnpairedProducts();
    }


    protected function processUnpairedProducts()
    {
        $anyOnixAndIveco = false;

        foreach ($this->resultProducts->getProducts() as $product) {
            if ($product->isOnixProduct() && $product->isIvecoOriginal()) {
                $anyOnixAndIveco = true;
            }
        }

        foreach ($this->unpairedProducts->getProducts() as $product) {
            if ($product->isOnixProduct() && $product->isIvecoOriginal()) {
                $anyOnixAndIveco = true;
            }
        }

        if ($anyOnixAndIveco) {
            foreach ($this->unpairedProducts->getProducts() as $product) {
                if ($product->isOnixProduct()) {
                    $this->resultProducts->addProduct($product);
                    $this->unpairedProducts->removeProduct($product);
                }
            }

            foreach ($this->unpairedProducts->getProducts() as $product) {
                if ($product->isIvecoBigDb() && $product->isIvecoOriginal()) {
                    $this->hiddenProducts->addProduct($product);
                    $this->unpairedProducts->removeProduct($product);
                }
            }
        } else {
            foreach ($this->unpairedProducts->getProducts() as $product) {
                if ($product->isIvecoBigDb() && $product->isIvecoOriginal()) {
                    $this->resultProducts->addProduct($product);
                    $this->unpairedProducts->removeProduct($product);
                }
            }
        }

        foreach ($this->unpairedProducts->getProducts() as $product) {
            $this->resultProducts->addProduct($product);
        }
    }

    protected function getProducts()
    {
        if ($this->products === null) {
            foreach ($this->getItems() as $item) {
                $this->products[] = \ProductFactory::get($item->getPageId());
            }
        }

        return $this->products;
    }

    protected function getProductsBySupplierCode($products, $supplierCodeSearch)
    {
        $result = [];
        foreach ($products as $product) {
            foreach ($product['supplier_codes'] as $supplierCodes) {
                $supplierCodes = explode(',', $supplierCodes);
                foreach ($supplierCodes as $supplierCode) {
                    if ($supplierCode == $supplierCodeSearch) {
                        $result[$product['product']->getPage()->getPageId()] = $product;
                    }
                }
            }
        }


        return $result;
    }

    protected function processPairedProducts($products)
    {
        $ivecoProducts = [];
        $onixProducts = [];
        $nonOnixProducts = [];
        $nonOnixProductsInStock = [];
        $nonOnixProductsNotInStock = [];
        $anyOnixInStock = false;
        $onixMinimumPrice = 1000000000;
        $nonOnixMinimumPrice = 1000000000;
        $ivecoMinimumPrice = 1000000000;

        //if there is a product with fixed price, always show all products
        foreach ($products as $product) {
            if (!empty($product['product']->getPage()->getValue(PropertyTag::ESHOP_EUR_FIXED_PRICE_WITHOUT_VAT_TAG()))
                || !empty($product['product']->getPage()->getValue(PropertyTag::ESHOP_CZK_FIXED_PRICE_WITHOUT_VAT_TAG()))
            ) {
                foreach ($products as $productInner) {
                    $this->resultProducts->addProduct($productInner['product']);
                }
                return;
            }
        }

        //get minimum prices of different types of products, to be used later
        foreach ($products as $product) {
            if ($product['product']->isIvecoOriginal() && $product['is_onix']) {
                $ivecoProducts[] = $product;
                if ($product['price'] > 0) {
                    $ivecoMinimumPrice = min($ivecoMinimumPrice, $product['price']);
                }
            } else if ($product['is_onix']) {
                $onixProducts[] = $product;
                if ($product['price'] > 0) {
                    $onixMinimumPrice = min($onixMinimumPrice, $product['price']);
                }
            } else {
                $nonOnixProducts[] = $product;
                if ($product['price'] > 0) {
                    $nonOnixMinimumPrice = min($nonOnixMinimumPrice, $product['price']);
                }
            }
        }

        foreach ($nonOnixProducts as $key => $nonOnixProduct) {
            $pairedOnixProduct = $nonOnixProduct['product']->getPairedOnixProduct();

            if (!$pairedOnixProduct instanceof Product) {
                continue;
            }

            if (!$pairedOnixProduct->isInStock()) {
                continue;
            }

            if ($pairedOnixProduct->getFinalPriceWithoutVatValue() > $nonOnixProduct['price']) {
                $this->hiddenProducts->addProduct($nonOnixProduct['product']);
                unset($nonOnixProducts[$key]);
            }
        }

        //if there are iveco products, and should be shown, show them, hide onix products
        if (!empty($ivecoProducts)) {
            foreach ($ivecoProducts as $ivecoProduct) {
                if ($ivecoProduct['price'] < $nonOnixMinimumPrice && $ivecoProduct['price'] > 0) {
                    $shouldIvecoBeShown = true;
                } else {
                    $shouldIvecoBeShown = true;
                    foreach ($nonOnixProducts as $nonOnixProduct) {
                        if ($ivecoProduct['stock'] <= 0 && ($nonOnixProduct['price'] < $ivecoProduct['price'] || $ivecoProduct['price'] <= 0) && $nonOnixProduct['stock'] > 0) {
                            $shouldIvecoBeShown = false;
                        }
                    }
                }
            }

            if ($shouldIvecoBeShown) {
                foreach ($ivecoProducts as $ivecoProduct) {
                    $this->resultProducts->addProduct($ivecoProduct['product']);
                }
                foreach ($onixProducts as $onixProduct) {
                    $this->hiddenProducts->addProduct($onixProduct['product']);
                }
                foreach ($nonOnixProducts as $nonOnixProduct) {
                    if ($nonOnixProduct['stock'] > 0 && $nonOnixProduct['price'] < $ivecoMinimumPrice) {
                        $this->resultProducts->addProduct($nonOnixProduct['product']);
                    } else {
                        $this->hiddenProducts->addProduct($nonOnixProduct['product']);
                    }
                }
            } else {
                foreach ($ivecoProducts as $ivecoProduct) {
                    $this->resultProducts->addProduct($ivecoProduct['product']);
                }
                foreach ($onixProducts as $onixProduct) {
                    $this->hiddenProducts->addProduct($onixProduct['product']);
                }
                foreach ($nonOnixProducts as $nonOnixProduct) {
                    if ($nonOnixProduct['stock'] > 0 && $nonOnixProduct['price'] < $ivecoMinimumPrice) {
                        $this->resultProducts->addProduct($nonOnixProduct['product']);
                    } else {
                        $this->hiddenProducts->addProduct($nonOnixProduct['product']);
                    }
                }
            }

            return;
        }

        //check if there are any onix products in stock
        foreach ($onixProducts as $onixProduct) {
            if ($onixProduct['stock'] > 0) {
                $anyOnixInStock = true;
            }
        }

        //if there are no onix products in stock
        if (!$anyOnixInStock) {
            //if there is only one non onix product, show it
            //hide onix products
            if (count($nonOnixProducts) == 1) {
                foreach ($nonOnixProducts as $nonOnixProduct) {
                    $this->resultProducts->addProduct($nonOnixProduct['product']);
                }
                foreach ($onixProducts as $onixProduct) {
                    $this->hiddenProducts->addProduct($onixProduct['product']);
                }
            }

            //sort into two arrays - in stock and not in stock
            foreach ($nonOnixProducts as $nonOnixProduct) {
                if ($nonOnixProduct['stock'] > 0) {
                    $nonOnixProductsInStock[] = $nonOnixProduct;
                } else {
                    $nonOnixProductsNotInStock[] = $nonOnixProduct;
                }
            }

            //sort by price and time of delivery, and add to result
            if (count($nonOnixProductsInStock) > 0) {
                $this->sortByPriceAndTimeOfDelivery($nonOnixProductsInStock);
            } else if (count($nonOnixProductsNotInStock) > 0) {
                $this->sortByPriceAndTimeOfDelivery($nonOnixProductsNotInStock);
            }

            //hide onix products
            foreach ($onixProducts as $onixProduct) {
                $this->hiddenProducts->addProduct($onixProduct['product']);
            }

            return;
        }


        //if there are no onix products, show all products
        if (count($onixProducts) <= 0) {
            foreach ($products as $product) {
                $this->resultProducts->addProduct($product['product']);
            }

            return;
        }

        //adds non onix products to result,
        //hides non onix products if it's price is higher than onix minimum price,
        //hides non onix products if it's stock is 0
        foreach ($nonOnixProducts as $nonOnixProduct) {
            if ($nonOnixProduct > 0 && $nonOnixProduct['price'] < $onixMinimumPrice && $nonOnixProduct['product']->getExternalStockBalance() > 0) {
                $this->resultProducts->addProduct($nonOnixProduct['product']);
            } else {
                $this->hiddenProducts->addProduct($nonOnixProduct['product']);
            }
        }

        //adds onix products to result
        foreach ($onixProducts as $onixProduct) {
            if (!(count($nonOnixProducts) > 0 && $onixProduct['stock'] <= 0)) {
                $this->resultProducts->addProduct($onixProduct['product']);
            }
        }
    }

    protected function sortByPriceAndTimeOfDelivery($products)
    {
        $productsByTimeOfDelivery = [];

        foreach ($products as $product) {
            $productsByTimeOfDelivery[$product['product']->getDeliveryTime()][] = $product;
        }

        foreach ($productsByTimeOfDelivery as $products) {
            usort($products, function ($a, $b) {
                return $a['price'] <=> $b['price'];
            });
        }

        foreach ($productsByTimeOfDelivery as $products) {
            foreach ($products as $k => $product) {
                if ($k == array_key_first($product)) {
                    $this->resultProducts->addProduct($product['product']);
                } else {
                    $this->hiddenProducts->addProduct($product['product']);
                }
            }
        }

        return;
    }

    public function getHiddenProducts(): ProductGroup
    {
        return $this->hiddenProducts;
    }

    protected function shouldBeProcessedAsPair($product, $productsBySupplierCode)
    {
        foreach ($productsBySupplierCode as $products) {
            if (count($products) > 1) {
                foreach ($products as $p) {
                    if ($p['product']->getPage()->getPageId() == $product['product']->getPage()->getPageId()) {
                        return true;
                    }
                }
            }

            return false;
        }

        return false;
    }
}
