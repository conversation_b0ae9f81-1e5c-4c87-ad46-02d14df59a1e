<?php

namespace App\FulltextSearch;

use App\Eshop\Catalog\Product\ProductGroup;
use App\Eshop\Product;
use App\FulltextSearch\Cases\NothingInStock;
use App\FulltextSearch\Filters\DefaultCase;
use Arr;
use FullTextSearch\SearchResultItem;

class FulltextSearch
{
    protected $products;

    public function __construct($products)
    {
        $this->products = $products;
    }

    public function processItems(): array
    {
        $filter = new DefaultCase($this->products);

        $result = $filter->filter();

        return [
            'result' => $result,
            'hidden' => $filter->getHiddenProducts(),
        ];
    }
}
