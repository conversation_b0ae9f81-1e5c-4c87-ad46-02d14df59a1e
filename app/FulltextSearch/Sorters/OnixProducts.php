<?php

namespace App\FulltextSearch\Sorters;

use App\Eshop\Product;

class OnixProducts extends AbstractSorter
{
    public function getSortedProducts()
    {
        $onixAndIvecoProducts = $this->getOnixAndIvecoProducts($this->pageList);
        $onixAndIvecoProducts = $this->sortOnixProducts($onixAndIvecoProducts);

        return $onixAndIvecoProducts;
    }

    protected function getOnixAndIvecoProducts($pageList)
    {
        $onixProducts = [];

        foreach ($pageList as $pageId => $page) {
            if ($page['product']->isOnixProduct()) {
                $onixProducts[$pageId] = $page;
            }
            if ($page['product']->isIvecoOriginal() && $page['product']->getAvailability() != Product::AVAILABILITY_NOT_AVAILABLE) {
                $onixProducts[$pageId] = $page;
            }
        }

        return $onixProducts;
    }

    protected function sortOnixProducts($pageList)
    {
        $ivecoOriginalOnixProducts = [];
        $ivecoOriginalProducts = [];
        $fiatOriginalProducts = [];
        $onixProducts = [];

        foreach ($pageList as $pageId => $page) {
            if ($page['product']->isIvecoOriginal() && $page['product']->isOnixProduct()) {
                $ivecoOriginalOnixProducts[$pageId] = $page;
            } elseif ($page['product']->isIvecoOriginal()) {
                $ivecoOriginalProducts[$pageId] = $page;
            } elseif ($page['product']->isFiatOriginal()) {
                $fiatOriginalProducts[$pageId] = $page;
            } else {
                $onixProducts[$pageId] = $page;
            }
        }

        $ivecoOriginalOnixProducts = $this->getSortedProductsInternal($ivecoOriginalOnixProducts);
        $ivecoOriginalProducts = $this->getSortedProductsInternal($ivecoOriginalProducts);
        $fiatOriginalProducts = $this->getSortedProductsInternal($fiatOriginalProducts);
        $onixProducts = $this->getSortedProductsInternal($onixProducts);

        return array_merge($ivecoOriginalOnixProducts, $ivecoOriginalProducts, $fiatOriginalProducts, $onixProducts);
    }
}
