<?php

namespace App\FulltextSearch\Sorters;

class IvecoProducts extends AbstractSorter
{
    public function getSortedProducts()
    {
        $ivecoProducts = $this->getIvecoProducts($this->pageList);
        $ivecoProducts = $this->getSortedProductsInternal($ivecoProducts);
        return $ivecoProducts;
    }

    public function getIvecoProducts($pageList)
    {
        $ivecoProducts = [];

        foreach ($pageList as $pageId => $page) {
            if ($page['product']->isIvecoOriginal()) {
                $ivecoProducts[$pageId] = $page;
            }
        }

        return $ivecoProducts;
    }
}
