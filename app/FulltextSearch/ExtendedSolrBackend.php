<?php

namespace App\FulltextSearch;

use FullTextSearch\Backend\SolrBackend;
use FullTextSearch\SearchIndex;

class ExtendedSolrBackend extends SolrBackend
{
    protected function getFieldTypesMap()
    {
        return [
            ExtendedSearchIndex::FIELD_TYPE_SYSTEM => '',
            ExtendedSearchIndex::FIELD_TYPE_TEXT => '_ft',
            ExtendedSearchIndex::FIELD_TYPE_DATETIME => '_fd',
            ExtendedSearchIndex::FIELD_TYPE_INTEGER => '_fi',
            ExtendedSearchIndex::FIELD_TYPE_LITERAL => '_fl',
            ExtendedSearchIndex::FIELD_TYPE_FLOAT => '_ff',
            ExtendedSearchIndex::FIELD_TYPE_BOOLEAN => '_fb',
            ExtendedSearchIndex::FIELD_TYPE_MULTIVALUED => '_ftm',
        ];
    }
}
