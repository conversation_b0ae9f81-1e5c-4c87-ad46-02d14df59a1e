<?php

namespace App\FulltextSearch\Event;

use Buxus\Event\Event;
use Buxus\WebUser\Contracts\WebUser;

class WebUserSearchEvent extends Event
{
    /** @var WebUser $user */
    protected $user;

    /**
     * Term the user searched for
     * @var $term
     */
    protected $term;

    public function __construct(WebUser $user, $term)
    {
        $this->user = $user;
        $this->term = $term;
    }

    public function getUser(): WebUser
    {
        return $this->user;
    }

    public function getTerm(): string
    {
        return $this->term;
    }
}
