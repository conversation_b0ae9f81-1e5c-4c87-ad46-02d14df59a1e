<?php

namespace App\FulltextSearch;

use App\FulltextSearch\ExtendedSearchIndex;
use App\Models\EnclosureTransaction;
use Buxus\Core\Constants;
use Buxus\Eshop\Order\OrderInterface;
use Buxus\Page\PageInterface;
use FullTextSearch\SearchDocument;

class EshopDocumentsSearchIndex extends ExtendedSearchIndex
{
    protected function getAllIds()
    {
        return \DB::table('onix_enclosures')
            //->limit(100)
                ->orderBy('enclosure_record_id', 'DESC')
            ->pluck('doc_record_id')
            ->toArray();
    }

    public function rebuild($customId = null, $skipClean = true)
    {
        $documentIds = [];
        if ($customId === null) {
            $documentIds = $this->getAllIds();
        } else {
            if (is_array($customId)) {
                $documentIds = $customId;
            } else {
                $documentIds = [$customId];
            }
        }

        if ($customId === null && !$skipClean) {
            $this->cleanBackends();
        }

        echo "Indexing " . count($documentIds) . " documents\n";
        $n = 0;
        foreach ($documentIds as $documentId) {
            $n++;
            if (!($n % 100)) {
                echo $n . "\n";
            }
           $this->indexEshopDocument($documentId, false);
        }
        echo "Indexing done\n";

        $this->commitBackends();
    }

    public function index($page_id, $instant_commit = true)
    {

    }

    public function indexEshopDocument($document_id, $instant_commit = true)
    {
        $doc = $this->prepareDoc($document_id);

        if ($doc !== null) {
            //echo "Indexing document $document_id\n";
            $this->indexDocToBackends($doc);
        } else {
            echo "Removing document $document_id\n";
            $this->removeDocFromBackends($doc);
        }

        if ($instant_commit) {
            $this->commitBackends();
        }
    }


    /**
     * @param int $document_id
     * @param int $page_type_id
     * @return SearchDocument
     */
    protected function prepareDoc($document_id, $page_type_id = null)
    {
        $enclosure_document = \DB::table('onix_enclosures')
            ->where('doc_record_id', $document_id)
            ->first();

        $doc = new SearchDocument($document_id, $this->tag);
        $doc->page_type_id = 0;

        foreach((array)$enclosure_document as $enclosure_document_property => $enclosure_document_value) {
            $doc->$enclosure_document_property = $enclosure_document_value;
        }

        if($enclosure_document->external_number) {
            $external_codes = explode(',', $enclosure_document->external_number);
            $item_names = [];
            $item_codes = [];
            $buxus_orders_vs = [];
            $buxus_orders_ids = [];
            $invalid_products = [];
            $ignored_external_codes = [];
            $code_property_tags = config('product.code_property_tags');

            foreach ($external_codes as $external_code) {
                $external_code = trim($external_code);
                if (empty($external_code)) {
                    continue;
                }

                $orderId = \DB::table('tblShopOrders')
                    ->where('variable_symbol', strtolower($external_code))
                    ->value('order_id');
                if(!$orderId) {
                    $ignored_external_codes[] = $external_code;
                    continue;
                }

                $order = \OrderFactory::getByVS(strtolower($external_code));
                if($order === null) {
                    $ignored_external_codes[] = $external_code;
                    continue;
                }
                if((int)$order->getData('onix_partner_id') != (int)$enclosure_document->partner_id) {
                    $ignored_external_codes[] = $external_code;
                    continue;
                }

                $buxus_orders_vs[] = $external_code;
                $buxus_orders_ids[] = $order->getOrderId();

                $items = $order->getItems();

                foreach ($items as $item) {
                    try {
                        $item_names[] = strtolower($item->getProductName());
                        $item_codes[] = strtolower($item->getProductCode());
                        foreach ($code_property_tags as $code_property_tag) {
                            $codes = explode(',', $item->getPage()->getValue($code_property_tag));
                            foreach ($codes as $code) {
                                $code = trim($code);
                                if (empty($code)) {
                                    continue;
                                }
                                $item_codes[] = strtolower($code);
                            }
                        }
                    } catch (\Exception $e) {
                        $invalid_products[] = $item->getPageId();
                    }
                }

            }

            $doc->item_names = $item_names;
            $doc->item_codes = array_unique($item_codes);
            $doc->buxus_order_vs = $buxus_orders_vs;
            $doc->buxus_order_ids = $buxus_orders_ids;
            $doc->invalid_products = $invalid_products;
            $doc->ignored_external_codes = $ignored_external_codes;
        }

        $doc->version = time();

        return $doc;
    }

    /**
     * prepare a search document from page, return NULL if document should be removed
     *
     * @param PageInterface|null $page
     * @return SearchDocument|null
     */
    public function generateDocForPage(PageInterface $page = null)
    {
        throw new \Exception('Not implemented');
    }

}
