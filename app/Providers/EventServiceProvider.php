<?php

namespace App\Providers;

use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array
     */
    protected $listen = [
        '\Buxus\Event\PagePreMoveEvent' => [
        ],

        '\Buxus\Event\PagePostMoveEvent' => [
        ],

        '\Buxus\Event\PagePreSubmitEvent' => [
        ],

        '\Buxus\Event\PagePostSubmitEvent' => [
        ],

        '\Buxus\Event\PageDeleteEvent' => [
        ],
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot()
    {
        parent::boot();
    }
}
