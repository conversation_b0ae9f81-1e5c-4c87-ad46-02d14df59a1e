<?php

namespace App\WebUser;

use App\WebUser\Event\WebUserEnabledLoginEvent;
use Buxus\Core\Constants;
use Buxus\WebUser\Event\WebUserCreatedEvent;
use Buxus\WebUser\Event\WebUserMailinglistDisabledEvent;
use Buxus\WebUser\Event\WebUserMailinglistEnabledEvent;
use Buxus\WebUser\Event\WebUserMailinglistSubscribedEvent;
use Buxus\WebUser\Event\WebUserMailinglistUnsubscribedEvent;
use Buxus\WebUser\Event\WebUserPreSubmitEvent;
use Buxus\WebUser\Event\WebUserUpdateEvent;
use BuxusEvent;
use GenericValueCache;

class WebUser extends \Buxus\WebUser\WebUser
{
    protected $blockedProducers;
    protected $original_enable_login;

    public const MORNING_DELIVERY = 'morning_delivery';
    public const NIGHT_DELIVERY = 'night_delivery';
    public const SALES_REPRESENTANT = 'is_sales_representant';
    public const SUPERUSER_EMAIL = 'superuser_email';

    const CUSTOMER_GROUPS = [
        'none' => [
            'id' => '0',
            'group' => 'Nepriradené',
        ],
        'competition' => [
            'id' => '1',
            'group' => 'Konkurencia',
        ],
        'end_customer' => [
            'id' => '2',
            'group' => 'Koncový zákazník',
        ],
        'small_vendor' => [
            'id' => '3',
            'group' => 'Drobný predajca'
        ],
        'car_service' => [
            'id' => '4',
            'group' => 'Autoservis'
        ],
    ];

    protected function load()
    {
        if (!is_null($this->user_id)) {
            $basic = \BuxusDB::get()->fetchRow(
                'SELECT * FROM tblWebUsers WHERE user_id = :user_id',
                [':user_id' => $this->user_id]
            );

            $this->original_enable_login = $basic['enable_login'];

            parent::load();
        }
    }

    public function isSK()
    {
        return $this->getCountry() == 'sk';
    }

    public function isCZ()
    {
        return $this->getCountry() == 'cz';
    }

    public function getCustomerGroup()
    {
        return $this->getCustomOption('customer_group');
    }

    public function setCustomerGroup($customerGroup)
    {
        $this->setCustomOption('customer_group', $customerGroup);
    }

    public function canUseIvecoSmallDb()
    {
        return ($this->getCustomOption('block_iveco_small_db') != Constants::C_True_Char);
    }

    public function canUseIvecoBigDb()
    {
        return ($this->getCustomOption('block_iveco_big_db') != Constants::C_True_Char);
    }

    public function canUseInvoice()
    {
        if ($this->getCustomOption('block_invoice') == Constants::C_True_Char) return false;
        if ($this->getCustomOption('block_invoice_automatic') == Constants::C_True_Char) return false;
        return true;
    }

    public function canUseMMTransport()
    {
        return $this->getSite() == 'cz'
            && $this->canUseInvoice()
            && ($this->getCustomOption('morning_delivery') == 'T'
                || $this->getCustomOption('night_delivery') == 'T');
    }

    public function getBlockedProducers(): ?array
    {
        if ($this->blockedProducers == null) {
            $this->blockedProducers = unserialize($this->getCustomOption('blocked_suppliers'));
        }

        if (is_array($this->blockedProducers)) {
            return $this->blockedProducers;
        }
        return null;
    }

    public function setCountry($country)
    {
        $this->setCustomOption('country', $country);
    }

    public function getCountry()
    {
        return $this->getCustomOption('country');
    }

    public function setPersonalDataConsent($personalDataConsent)
    {
        $this->setCustomOption('personal_data_consent', $personalDataConsent);
    }

    public function getPersonalDataConsent()
    {
        return $this->getCustomOption('personal_data_consent');
    }

    public function getDeliveryTime()
    {
        if ($this->getCustomOption(self::MORNING_DELIVERY) === Constants::C_True_Char) {
            return self::MORNING_DELIVERY;
        } else if ($this->getCustomOption(self::NIGHT_DELIVERY) === Constants::C_True_Char) {
            return self::NIGHT_DELIVERY;
        }

        return null;
    }

    public function getDeliveryTimeAsText()
    {
        $deliveryTime = $this->getDeliveryTime();
        if ($deliveryTime === self::MORNING_DELIVERY) {
            return 'Denný závoz';
        } else if ($deliveryTime === self::NIGHT_DELIVERY) {
            return 'Nočný závoz';
        }

        return '';
    }

    public function getDisplayName()
    {
        return ($this->getFirstName() && $this->getSurname())
            ? $this->getFirstName() . ' ' . $this->getSurname()
            : $this->getCompanyName();
    }

    public function getHeaderName()
    {
        $headerName = $this->getCompanyName();
        if(empty($headerName)) {
            $headerName = \Trans::str('user', 'Konto');
        }
        if(strlen($headerName) > 25) {
            $headerName = substr($headerName, 0, 25) . '...';
        }

        return $headerName;
    }

    public function getOnixPartnerId()
    {
        return $this->getCustomOption('onix_partner_id');
    }

    /**
     * @param bool $triggers
     * @return mixed
     * @throws \InvalidArgumentException
     */
    public function save($triggers = true)
    {
        if ($triggers) {
            // fire the pre submit event
            BuxusEvent::fire(new WebUserPreSubmitEvent($this->user_id, $this));
        }

        $password = null;

        if (!is_null($this->password)) {
            $password = $this->password;
        }

        $userId = $this->user_id;

        $result = EditUser(
            $userId,
            $this->username,
            $password,
            $password,
            $this->first_name,
            $this->surname,
            $this->nick_name,
            $this->email,
            ($this->enable_login ? 'T' : 'F'),        // Enable login
            ($this->enable_mailing_lists ? 'T' : 'F'),  // Enable mailinglist
            'F',                        // Mailing format (text: T, text without diacritic: D, HTML: F)
            $this->valid_till_time,     // Valid till time
            ($this->active ? 'T' : 'F'), // Active
            $this->customOptions,        // User options
            'F',                        // Generate password
            'F',                        // Send mail to user
            'F',                        // Send mail to provider
            '',                        // Provider email
            '',                        // Mail subject
            '',                        // Mail prefix
            '',                        // Mail suffix
            'F',                       // Enable blog
            $this->company_name,
            $this->site,
            $this->notes
        );

        $wasCreated = false;

        if (is_null($this->user_id)) {
            $this->user_id = $userId;
            $wasCreated = true;
        }

        if ($result != C_AUTH_ERR_OK && $result != C_AUTH_ERR_EXIST) {
            throw new \InvalidArgumentException('Error saving user: ' . $result);
        }

        if ($wasCreated && $triggers) {
            BuxusEvent::fire(new WebUserCreatedEvent($userId, $this->getAllData()));
        }

        if ($triggers) {
            BuxusEvent::fire(new WebUserUpdateEvent($userId, $this->getAllData()));
        }

        if (!is_null($this->original_enable_mailing_lists) &&
            $this->enable_mailing_lists !== $this->original_enable_mailing_lists
        ) {
            if ($triggers) {
                if ($this->original_enable_mailing_lists) {
                    BuxusEvent::fire(new WebUserMailinglistDisabledEvent($this));
                } else {
                    BuxusEvent::fire(new WebUserMailinglistEnabledEvent($this));
                }
            }

            $this->original_enable_mailing_lists = null;
        }

        if (!is_null($this->original_enable_login) &&
            $this->enable_login !== $this->original_enable_login &&
            $this->original_enable_login != Constants::C_True_Char
        ) {
            if ($triggers) {
                if ($this->enable_login == Constants::C_True_Char) {
                    BuxusEvent::fire(new WebUserEnabledLoginEvent($this->getUserId()));
                }
            }

            $this->original_enable_login = null;
        }

        if (!empty($this->added_mailinglists)) {
            if ($triggers) {
                BuxusEvent::fire(new WebUserMailinglistSubscribedEvent($this, array_keys($this->added_mailinglists)));
            }
            $this->added_mailinglists = [];
        }

        if (!empty($this->removed_mailinglists)) {
            if ($triggers) {
                BuxusEvent::fire(new WebUserMailinglistUnsubscribedEvent($this, array_keys($this->removed_mailinglists)));
            }
            $this->removed_mailinglists = [];
        }
    }

    public function getInvoiceAfterDueDate()
    {
        return $this->getCustomOption('invoice_after_due_date');
    }

    public function hasInvoiceAfterDueDate(): bool
    {
        return $this->getInvoiceAfterDueDate() == Constants::C_True_Char;
    }

    public function setInvoiceAfterDueDate($invoiceAfterDueDate)
    {
        $this->setCustomOption('invoice_after_due_date', $invoiceAfterDueDate);
    }

    public function isSalesRepresentant()
    {
        return $this->getCustomOption(self::SALES_REPRESENTANT) === Constants::C_True_Char;
    }

    public function getInvoiceEmailAddress()
    {
        if ($this->getCustomOption('email_invoice') == 1) {
            $address = filter_var($this->getCustomOption('email_invoice_address'), FILTER_VALIDATE_EMAIL);
        }

        if (empty($address)) {
            $address = filter_var($this->getEmail(), FILTER_VALIDATE_EMAIL);
        }

        return $address;
    }

    public function canUseAugustinGroup()
    {
        return ($this->getCustomOption('block_augustin_group') != Constants::C_True_Char);
    }

    public static function getCachedCompanyName($webuserId)
    {
        return GenericValueCache::get('webuser_company_name_' . $webuserId, function () use ($webuserId) {
            return \WebUserFactory::getById($webuserId)->getCompanyName();
        }, 90 * 60 * 24);
    }

    public static function getCustomerGroupLabel($customerGroupId)
    {
        foreach (self::CUSTOMER_GROUPS as $group) {
            if ($group['id'] == $customerGroupId) {
                return $group['label'];
            }
        }

        return null;
    }

    public function setWasEnabled(int $wasEnabled)
    {
        $this->setCustomOption('was_enabled', $wasEnabled);
    }

    public function getWasEnabled()
    {
        return $this->getCustomOption('was_enabled');
    }

    public function shouldInvoiceEmailBeSent(): bool
    {
        return $this->getCustomOption('do_not_send_invoice_reminders') != Constants::C_True_Char;
    }

    public function resolveCountry(): ?string
    {
        if (!empty($this->getCountry())) {
            return $this->getCountry();
        }

        if ($this->getSite() == 'sk') {
            return 'Slovenská republika';
        }

        if ($this->getSite() == 'cz') {
            return 'Česká republika';
        }

        return null;
    }
}
