<?php

namespace App\Http\Controllers;

use App\Authentication\FakeAuthentication;
use Buxus\Util\PageIds;
use Buxus\Util\Url;
use Buxus\WebUser\Facades\WebUserAuthentication;
use Buxus\WebUser\Facades\WebUserFactory;
use Illuminate\Http\Request;

class AuthenticationController extends Controller
{
    public function fakeLogin(Request $request)
    {
        if (\WebUserAuthentication::isAuthenticated() && ($superuser = \WebUserAuthentication::getUser())->getCustomOption('is_sales_representant')) {
            (new FakeAuthentication())->setSuperuser($superuser);

            $user = WebUserFactory::getById($request->fake_login_id);
            WebUserAuthentication::fakeLoginAsAdmin($user, $superuser);
        }
        return redirect(Url::page(PageIds::getHomepage()));
    }

    public function validateUserLogin(Request $request)
    {
        return response()->json([
            'user_exists' => WebUserAuthentication::userExists($request->user_login),
            'can_user_login' => WebUserAuthentication::canUserLogin($request->user_login),
        ]);

    }
}
