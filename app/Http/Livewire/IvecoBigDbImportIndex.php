<?php

namespace App\Http\Livewire;

use App\Imports;
use Buxus\Ciselniky\Facades\Ciselniky;
use Buxus\Livewire\Component;
use Buxus\Util\PageIds;

class IvecoBigDbImportIndex extends Component
{
    public $imports;
    public $producers;
    public $importsCom;

    public function __construct()
    {
        $import = Imports::getImports(Imports::IVECO_ORIGINAL_BIG_DB_ID, Imports::IVECO_ORIGINAL_BIG_DB);

        $importArr = [[
            'name' => 'IVECO originál',
            'latest' => $import['created_at'],
            'allDone' => $import['status'],
            'items_processed' => $import['items_processed'],
            'updates_processed' => $import['updates_processed'],
            'creates_processed' => $import['creates_processed'],
            'deletes_processed' => $import['deletes_processed'],
            'errors' => $import['errors'],
        ]];

        $importCom = Imports::getImports(Imports::IVECO_ORIGINAL_BIG_DB_ID, Imports::IVECO_ORIGINAL_BIG_DB_COM);

        $importComArr = [[
            'name' => 'IVECO originál',
            'latest' => $importCom['created_at'],
            'allDone' => $importCom['status'],
            'items_processed' => $importCom['items_processed'],
            'updates_processed' => $importCom['updates_processed'],
            'creates_processed' => $importCom['creates_processed'],
            'deletes_processed' => $importCom['deletes_processed'],
            'errors' => $importCom['errors'],
        ]];

        $this->imports = $importArr;
        $this->importsCom = $importComArr;
    }


    public function render()
    {
        return view('livewire.iveco-big-db-import-index');
    }
}
