<?php

namespace App\Http\Livewire;

use AuthenticationLog;
use Buxus\Livewire\Component;
use Cache;
use Livewire\WithPagination;

class ShowLoginStatistics extends Component
{
    use WithPagination;

    protected $paginationTheme = 'bootstrap-4';

    public $userId;

    public function render()
    {
        $logs = $this->getLogs();

        return view('livewire.show-login-statistics', [
            'logs' => $logs
        ]);
    }

    protected function getLogs()
    {
        $from = request('login_from');
        $to = request('login_to');

        return Cache::remember('login-statistics-' . $from . '-' . $to, 15 * 60, function () {
            return \DB::table('tblWebUserLog as tWUL')
                ->select('tWUL.*', 'tWU.company_name')
                ->where('action', AuthenticationLog::ACTION_LOGIN)
                ->when(request('login_from'), function ($q) {
                    $q->where('action_date', '>=', request('login_from'));
                })->when(request('login_to'), function ($q) {
                    $q->where('action_date', '<=', request('login_to'));
                })->when($this->userId, function ($q) {
                    $q->where('tWUL.user_id', $this->userId);
                })->leftJoin('tblWebUsers as tWU', function ($q) {
                    $q->on('tWUL.user_id', 'tWU.user_id');
                })
                ->orderBy('tWUL.action_date', 'DESC')
                ->paginate(24, ['*'], 'loginLogPage', request('loginLogPage'));
        });
    }
}
