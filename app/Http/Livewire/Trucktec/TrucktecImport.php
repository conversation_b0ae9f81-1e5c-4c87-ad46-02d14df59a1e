<?php

namespace App\Http\Livewire\Trucktec;

use App\Imports;
use App\Imports\Components\AbstractImportWithCrossfileComponent;
use App\Imports\Processors\Trucktec\TrucktecCrossFile;
use App\Imports\Processors\Trucktec\TrucktecImportProcessor;
use Buxus\Util\PageIds;

class TrucktecImport extends AbstractImportWithCrossfileComponent
{
    protected int $supplier;
    protected int $importType = Imports::TRUCKTEC;
    protected string $fileLocation = 'imports/trucktec/trucktec.xlsx';
    protected string $filePreviousLocation = 'imports/trucktec/trucktec-previous.xlsx';
    protected ?string $configKey = 'trucktec';
    protected string $processorClass = TrucktecImportProcessor::class;
    protected string $crossfileProcessorClass = TrucktecCrossFile::class;

    public const CONFIG_KEY = 'trucktec';

    protected bool $shouldQueueCrossfile = true;

    public function __construct($id = null)
    {
        ini_set('memory_limit', '2048M');
        $this->supplier = PageIds::getTrucktecSupplier();
        parent::__construct($id);
    }
}
