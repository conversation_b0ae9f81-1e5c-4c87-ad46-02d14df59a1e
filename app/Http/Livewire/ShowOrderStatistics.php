<?php

namespace App\Http\Livewire;

use AuthenticationLog;
use Buxus\Livewire\Component;
use Livewire\WithPagination;

class ShowOrderStatistics extends Component
{
    use WithPagination;

    protected $paginationTheme = 'bootstrap-4';

    public $userId;

    public function render()
    {
        //

        $orders = [];
        return view('livewire.show-order-statistics', [
            'orders' => $orders
        ]);
    }
}
