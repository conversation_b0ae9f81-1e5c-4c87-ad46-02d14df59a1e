<?php

namespace App\Http\Livewire;

use App\Imports;
use App\Imports\Processors\IvecoBigDb\IvecoBigDbXlsxProcessor;
use App\IvecoBigDb;
use Buxus\Livewire\Component;
use Illuminate\Support\Facades\Storage;
use Livewire\WithFileUploads;

class IvecoBigDbImport extends Component
{
    use WithFileUploads;

    public $availabilityOptions;

    protected $supplier;
    public $availability;
    public $file;

    protected $ivecoBigDb;

    protected $rules = [
        'file' => 'required|max:25576',
        'availability' => 'required',
    ];

    public function __construct()
    {
        $this->availabilityOptions = \Buxus\Property\PropertyManager::getInstance()
            ->getPropertyByTag('availability')
            ->getAttribute('options');

        if (!empty($this->availabilityOptions)) {
            $this->availability = array_key_first($this->availabilityOptions);
        }
    }

    public function create($site = 'sk')
    {
        set_time_limit(500);

        $this->validate();

        $this->ivecoBigDb = new IvecoBigDb($site);

        if (Storage::disk('local')->exists($this->ivecoBigDb->getImportPath())) {
            if (Storage::disk('local')->exists($this->ivecoBigDb->getImportPath())) {
                Storage::disk('local')->delete($this->ivecoBigDb->getImportPathPrevious());
            }
            Storage::move($this->ivecoBigDb->getImportPath(), $this->ivecoBigDb->getImportPathPrevious());
        }

        $importPathInfo = pathinfo($this->ivecoBigDb->getImportPath());

        $import = Imports::create([
            'path' => $this->file->storeAs($importPathInfo['dirname'], $importPathInfo['basename']),
            'producer_ciselnik_id' => Imports::IVECO_ORIGINAL_BIG_DB_ID,
            'type' => $this->ivecoBigDb->getType(),
            'availability' => $this->availability,
            'status' => Imports::STILL_RUNNING,
        ]);



        (new IvecoBigDbXlsxProcessor($this->availability, $import->id, $site))->import($this->ivecoBigDb->getStoragePath());

        session()->flash('success', 'Import pridaný úspešne.');
    }

    public function render()
    {
        return view('livewire.iveco-big-db-import');
    }
}
