<?php

namespace App\Http\Livewire;

use App\Supplier;
use Buxus\Livewire\Component;

class ProducersIndex extends Component
{
    public $producers;

    public $producer;

    protected $listeners = ['producerEdited' => '$refresh'];

    public function render()
    {
        $this->producers = Supplier::where('producer_ciselnik_id', '!=', 9999998)->where('producer_ciselnik_id', '!=', 9999999)->get()->sortBy('name')->fresh();

        return view('livewire.producers-index');
    }

    public function changeStatus($producer)
    {
        $producer = Supplier::find($producer['id']);
        $producer->price_levels_on = !$producer->price_levels_on;
        $producer->save();

        if ($producer->price_levels_on) {
            session()->flash('success', 'Cenové hladiny boli zapnuté úspešne.');
        } else {
            session()->flash('success', 'Cenové hladiny boli vypnuté úspešne.');
        }

        $this->emit('producerEdited');
    }
}
