<?php

namespace App\Http\Livewire;

use App\ProductSearchStatistics;
use App\Statistics\ExtendedStatistics\ExportEmptyProductSearchStatistics;
use Buxus\Livewire\Component;
use Excel;
use Livewire\WithPagination;

class ExtendedSearchStatisticsForNonexistentProductsShow extends Component
{
    use WithPagination;

    public const NONEXISTENT_DATE_FROM = 'nonexistentDateFrom';
    public const NONEXISTENT_DATE_TO = 'nonexistentDateTo';

    protected $logsQuery;

    public $queryString = ['nonexistentDateFrom', 'nonexistentDateTo'];

    public $nonexistentDateFrom;
    public $nonexistentDateTo;

    public function mount()
    {
        $this->logsQuery = ProductSearchStatistics::whereNull('page_id')
            ->select('*')
            ->selectRaw('count(id) as sum')
            ->when(!empty($this->nonexistentDateFrom), function ($q) {
                $q->whereDate('created_at', '>=', $this->nonexistentDateFrom);
            })->when(!empty($this->nonexistentDateTo), function ($q) {
                $q->whereDate('created_at', '<=', $this->nonexistentDateTo);
            })
            ->groupBy('search_term');
    }

    public function render()
    {
        $this->mount();

        return view('livewire.extended-search-statistics-for-nonexistent-products-show', [
            'logs' => $this->logsQuery->paginate($perPage = 24, $columns = ['*'], $pageName = 'nonexistent-products-page'),
        ]);
    }

    public function export()
    {
        $this->mount();

        $export = new ExportEmptyProductSearchStatistics($this->logsQuery->get());
        return Excel::download($export, 'logs.xlsx');
    }
}
