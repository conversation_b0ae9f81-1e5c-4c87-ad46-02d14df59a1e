<?php

namespace App\Http\Livewire\TrucktecMargin;

use App\Imports\Components\AbstractMarginComponent;
use App\MarginLevel;
use App\Supplier;

class TrucktecMargin extends AbstractMarginComponent
{
    protected $type = MarginLevel::TYPE_TRUCKTEC;

    public function render()
    {
        $this->producer = Supplier::where('name', 'TRUCKTEC-SUPPLIER')->first();

        return parent::render();
    }
}
