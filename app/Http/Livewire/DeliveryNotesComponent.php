<?php

namespace App\Http\Livewire;

use App\Onix\Onix;
use Buxus\Livewire\Component;
use Illuminate\Pagination\Paginator;
use Livewire\WithPagination;

class DeliveryNotesComponent extends Component
{
    use WithPagination;

    protected $deliveryNotes;
    protected $webuser;

    public $delivery_note_search;
    public $product_search;
    public $enclosure_type;

    public $deliveryNotePagination;

    protected $paginationTheme = 'bootstrap';


    public function __construct($id = null)
    {
        $this->webuser = \WebUserAuthentication::getUser();
        parent::__construct($id);
    }

    public function getQueryString()
    {
        return 'deliveryNotePagination';
    }

    public function render()
    {
        $this->deliveryNotes = $this->getDeliveryNotes();

        return view('livewire.delivery-notes-component', [
            'deliveryNotes' => $this->deliveryNotes,
        ]);
    }

    protected function getDeliveryNotes()
    {
        $perPage = 24;
        $currentPage = $this->page ?? 1;

        $fulltextSearch = app('buxus:fulltext-search:manager')->getSearch('eshop_documents');
        $result = $fulltextSearch->search($this->delivery_note_search, [
            'onix_partner_id' => $this->webuser->getCustomOption('onix_partner_id'),
            'enclosure_type_id' => Onix::DELIVERY_NOTE_TYPE_ID,
            'product_search' => $this->product_search,
            'vs_search' => $this->delivery_note_search,
            'page' => $currentPage,
            'per_page' => $perPage,
        ]);

        $items = collect([]);
        $itemIds = [];
        foreach ($result as $item) {
            $itemIds[] = $item->doc_record_id;
        }
        $dbItems = \DB::table('onix_enclosures')
            ->whereIn('doc_record_id', $itemIds)
            ->get()
            ->keyBy('doc_record_id');
        foreach ($result as $item) {
            $items->push($dbItems[$item->doc_record_id]);
        }

        return new \Illuminate\Pagination\LengthAwarePaginator(
            $items,
            $result->getTotalCount(),
            $perPage,
            $currentPage,
        );
    }

    public function updated()
    {
        $this->resetPage();
    }
}
