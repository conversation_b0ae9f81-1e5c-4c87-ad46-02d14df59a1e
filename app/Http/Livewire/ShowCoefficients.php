<?php

namespace App\Http\Livewire;

use App\Coefficient;
use App\MarginLevel;
use Buxus\Livewire\Component;

class ShowCoefficients extends Component
{
    public $user;
    public $coefficients;

    protected $listeners = [
        'coefficientEdited' => 'rerender',
    ];

    public function rerender(){
        $this->mount();
        $this->render();
    }

    public function mount()
    {
        $this->coefficients = null;
        $coefficients = Coefficient::where('user_id', $this->user)->get();
        foreach ($coefficients as $coefficient) {
            $this->coefficients[] = [
                'id' => $coefficient->id,
                'producer' => \Ciselniky::get('producer')->getValueById($coefficient->producer_ciselnik_id)->getName(),
                'coefficient' => $coefficient->coefficient,
            ];
        }
    }

    public function render()
    {
        return view('livewire.show-coefficients');
    }

    public function update($coefficientId, $form)
    {
        $coefficient = Coefficient::find($coefficientId);

        $coefficient->coefficient = $form['coefficient'];
        $coefficient->save();

        session()->flash('success', 'Cenová hladina bola upravená úspešne.');

        $this->emit('marginLevelEdited');
    }
}
