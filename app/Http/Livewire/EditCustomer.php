<?php

namespace App\Http\Livewire;

use App\Coefficient;
use Buxus\Livewire\Component;

class EditCustomer extends Component
{
    public $user;
    public $producers;
    public $producer;
    public $coefficient;

    protected $rules = [
        'producer' => 'required',
        'coefficient' => 'required',
    ];

    public function mount()
    {
        $producers = \Ciselniky::get('producer')->getAllValues();
        foreach ($producers as $producer) {
            $this->producers[$producer->getId()] = $producer->getName();
        }

        if(is_array($this->producers)) {
            $this->producer = array_key_first($this->producers);
        }
    }

    public function render()
    {
        return view('livewire.edit-customer');
    }

    public function create()
    {
        $this->validate();

        Coefficient::create([
            'user_id' => $this->user,
            'producer_ciselnik_id' => $this->producer,
            'coefficient' => $this->coefficient
        ]);

        $this->emit('coefficientEdited');

        session()->flash('success', 'Cenová hladina pridaná úspešne.');
    }
}
