<?php

namespace App\Http\Livewire;

use App\Imports;
use App\Imports\Processors\IvecoPurchasePrices\IvecoPurchasePricesProcessor;
use Buxus\Livewire\Component;
use Illuminate\Support\Facades\Storage;
use Livewire\WithFileUploads;

class IvecoPurchasePricesImport extends Component
{
    use WithFileUploads;

    public $availabilityOptions;

    protected $supplier;
    public $availability;
    public $file;

    protected $rules = [
        'file' => 'required',
        'availability' => 'required',
    ];

    public function __construct()
    {
        $this->availabilityOptions = \Buxus\Property\PropertyManager::getInstance()
            ->getPropertyByTag('availability')
            ->getAttribute('options');

        if (!empty($this->availabilityOptions)) {
            $this->availability = array_key_first($this->availabilityOptions);
        }
    }

    public function create()
    {
        set_time_limit(500);

        $this->validate();

        if (Storage::disk('local')->exists('imports/iveco-original-prices-import/iveco-original-prices.xlsx')) {
            if (Storage::disk('local')->exists('imports/iveco-original-prices-import/iveco-original-prices.xlsx')) {
                Storage::disk('local')->delete('imports/iveco-original-prices-import/iveco-original-prices-previous.xlsx');
            }
            Storage::move('imports/iveco-original-prices-import/iveco-original-prices.xlsx', 'imports/iveco-original-prices-import/iveco-original-prices-previous.xlsx');
        }

        $import = Imports::create([
            'path' => $this->file->storeAs(Imports::getPath(Imports::IVECO_ORIGINAL_PURCHASE_PRICES), 'iveco-original-prices.xlsx'),
            'producer_ciselnik_id' => Imports::IVECO_ORIGINAL_PURCHASE_PRICES_ID,
            'type' => Imports::IVECO_ORIGINAL_PURCHASE_PRICES,
            'availability' => $this->availability,
            'status' => Imports::STILL_RUNNING,
        ]);

        (new IvecoPurchasePricesProcessor($this->availability, $import->id))->import(storage_path('app/imports/iveco-original-prices-import/iveco-original-prices.xlsx'));

        session()->flash('success', 'Import pridaný úspešne.');
    }

    public function render()
    {
        return view('livewire.iveco-purchase-prices-import');
    }
}
