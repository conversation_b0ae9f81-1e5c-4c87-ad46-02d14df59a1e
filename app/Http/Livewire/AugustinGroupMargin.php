<?php

namespace App\Http\Livewire;

use App\MarginLevel;
use App\Supplier;
use Buxus\Livewire\Component;

class AugustinGroupMargin extends Component
{
    public $price_from;
    public $margin;
    public $margin_eu;
    public $producer;

    protected $rules = [
        'price_from' => 'required|numeric|min:0',
        'margin' => 'required|numeric|min:0',
        'margin_eu' => 'required|numeric|min:0',
    ];

    public function render()
    {
        $this->producer = Supplier::where('name', 'Augustin Group')->first();

        return view('livewire.augustin-group-margin');
    }

    public function create()
    {
        $this->validate();

        MarginLevel::create([
            'price_from' => $this->price_from,
            'margin' => $this->margin,
            'margin_eu' => $this->margin_eu,
            'producer_id' => $this->producer->id,
            'type' => MarginLevel::TYPE_AUGUSTIN_GROUP
        ]);

        session()->flash('success', 'Cenová hladina pridaná úspešne.');

        $this->emit('marginLevelEdited');
    }

    public function changeStatus()
    {
        $this->producer->price_levels_on = !$this->producer->price_levels_on;
        $this->producer->save();

        if ($this->producer->price_levels_on) {
            session()->flash('success', 'Cenové hladiny boli zapnuté úspešne.');
        } else {
            session()->flash('success', 'Cenové hladiny boli vypnuté úspešne.');
        }
    }
}
