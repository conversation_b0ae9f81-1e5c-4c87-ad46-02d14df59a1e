<?php

namespace App\Http\Livewire;

use App\Invoice\CreditNote;
use App\Invoice\Invoice;
use App\Onix\Onix;
use Buxus\Livewire\Component;
use Livewire\WithPagination;

class CreditNotesComponent extends Component
{
    use WithPagination;

    public const PAID = 'PAID';
    public const PARTLY_PAID = 'PARTLY_PAID';
    public const UNPAID = 'UNPAID';
    public const WAITING = 'WAITING';

    protected $creditNotes;
    protected $webuser;

    public $credit_note_search;
    public $product_search;
    public $enclosure_type;

    public $creditNotePagination;

    protected $paginationTheme = 'bootstrap';


    public function __construct($id = null)
    {
        $this->webuser = \WebUserAuthentication::getUser();
        parent::__construct($id);
    }

    public function getQueryString()
    {
        return 'creditNotePagination';
    }

    public function render()
    {
        $this->creditNotes = $this->getCreditNotes();

        return view('livewire.credit-notes-component', [
            'creditNotes' => $this->creditNotes,
        ]);
    }

    protected function getCreditNotes()
    {
        $perPage = 24;
        $currentPage = $this->page ?? 1;

        $fulltextSearch = app('buxus:fulltext-search:manager')->getSearch('eshop_documents');
        $result = $fulltextSearch->search($this->credit_note_search, [
            'onix_partner_id' => $this->webuser->getCustomOption('onix_partner_id'),
            'enclosure_type_id' => Onix::CREDIT_NOTE_TYPE_ID,
            'product_search' => $this->product_search,
            'vs_search' => $this->credit_note_search,
            'page' => $currentPage,
            'per_page' => $perPage,
        ]);

        $items = collect([]);
        $itemIds = [];
        foreach ($result as $item) {
            $itemIds[] = $item->doc_record_id;
        }
        $dbItems = \DB::table('onix_enclosures')
            ->whereIn('doc_record_id', $itemIds)
            ->get()
            ->keyBy('doc_record_id');
        foreach ($result as $item) {
            $items->push($dbItems[$item->doc_record_id]);
        }

        return new \Illuminate\Pagination\LengthAwarePaginator(
            $items,
            $result->getTotalCount(),
            $perPage,
            $currentPage,
        );
    }

    public static function renderCreditNoteState($creditNote)
    {
        $state = self::getCreditNoteState($creditNote);

        if ($state == self::WAITING) {
            return view('credit-note.states.waiting');
        }

        if ($state == self::PAID) {
            return view('credit-note.states.paid');
        }

        if ($state == self::UNPAID) {
            return view('credit-note.states.unpaid');
        }

        return view('credit-note.states.partly-paid');
    }

    public static function getCreditNoteState($creditNote): string
    {
        $creditNote = new CreditNote($creditNote->enclosure_record_id);

        if ($creditNote->hasOngoingTransaction()) {
            return self::WAITING;
        }

        if ($creditNote->creditNote->payment_remaining == 0) {
            return self::PAID;
        }

        if ($creditNote->creditNote->payment_status == 0) {
            return self::UNPAID;
        }

        return self::PARTLY_PAID;
    }

    public function updated()
    {
        $this->resetPage();
    }

    public function offsetInvoiceWithCreditNote($creditNoteId, $invoiceId)
    {
        $creditNote = new CreditNote($creditNoteId);
        $invoice = new Invoice($invoiceId);

        $invoice->offsetCreditNote($creditNote);

        session()->flash('success', \Trans::str('invoice', 'Proces spracováva účtovné oddelenie. O započítaní dobropisu budete informovaný e-mailom.'));
    }
}
