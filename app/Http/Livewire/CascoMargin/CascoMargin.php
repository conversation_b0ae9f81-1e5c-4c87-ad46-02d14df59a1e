<?php

namespace App\Http\Livewire\CascoMargin;

use App\Imports\Components\AbstractMarginComponent;
use App\MarginLevel;
use App\Supplier;
use Buxus\Livewire\Component;

class CascoMargin extends AbstractMarginComponent
{
    protected $type = MarginLevel::TYPE_CASCO;

    public function render()
    {
        $this->producer = Supplier::where('name', 'CASCO-SUPPLIER')->first();

        return parent::render();
    }
}
