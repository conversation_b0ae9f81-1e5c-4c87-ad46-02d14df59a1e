<?php

namespace App\Http\Livewire;

use App\Exports\ExtendedSalesStatisticsExport;
use App\WebUser\WebUser;
use Buxus\Core\Constants;
use Buxus\Livewire\Component;
use Buxus\WebUser\Contracts\WebUser as WebUserContract;
use Carbon\Carbon;
use Excel;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Collection;
use Livewire\WithPagination;
use WebUserFactory;

class ExtendedSalesStatistics extends Component
{
    use WithPagination;

    public $month;
    public $dateFrom;
    public $dateTo;
    public const MONTH_OPTION = 'month';
    public const DATE_FROM = 'dateFrom';
    public const DATE_TO = 'dateTo';

    protected $queryString = [
        self::MONTH_OPTION,
        self::DATE_FROM,
        self::DATE_TO,
    ];

    protected $listeners = [
        'updatedMonth' => 'resolveMonth',
        'updatedDateFrom' => 'resolveDateFrom',
        'updatedDateTo' => 'resolveDateTo',
    ];

    public function render()
    {
        $data = $this->getData();
        $months = $this->getMonths();

        return view('livewire.extended-sales-statistics', [
            'data' => $this->paginate($data, 36),
            'count' => $data->count(),
            'months' => $months,
        ]);
    }

    protected function getMonths()
    {
        $months = [];

        for ($i = 2022; $i <= Carbon::now()->year; $i++) {
            $maxMonths = Carbon::now()->year == $i ? Carbon::now()->month : 12;
            for ($j = 1; $j <= $maxMonths; $j++) {
                $months[] = str_pad($j, 2, "0", STR_PAD_LEFT) . '/' . $i;
            }
        }

        return $months;
    }

    protected function getData()
    {
        $data = collect([]);
        foreach ($this->getSalesWebusers() as $webuser) {
            foreach ($this->getOrdersForUser($webuser) as $order) {
                try {
                    $orderedFor = \WebUserFactory::getById($order['order']->getUserId());
                } catch (\Exception $e) {
                    $orderedFor = null;
                }

                $data->push([
                    'webuser_name' => $order['webuser']->getFirstname() . ' ' . $order['webuser']->getSurname(),
                    'order_company' => $order['order']->getInvoiceCompanyName(),
                    'order_id' => $order['order']->getOrderId(),
                    'order_vs' => $order['order']->getVariableSymbol(),
                    'order_price' => $order['order']->getTotalPriceEurWithoutVat(),
                    'order_datetime' => $order['order']->getOrderDatetime(),
                    'ordered_for_category' => $orderedFor instanceof WebUser ? WebUser::getCustomerGroupLabel($orderedFor->getCustomerGroup()) : '',
                ]);
            }
        }

        $data = $data->sortByDesc('order_price');

        return $data;
    }


    protected function getOrdersForUser(WebUserContract $webuser): Collection
    {
        $email = $webuser->getEmail();

        $orderIds = \DB::table('tblShopOrderOptions')
            ->join('tblShopOrders', 'tblShopOrders.order_id', '=', 'tblShopOrderOptions.order_id')
            ->select('tblShopOrderOptions.order_id')
            ->distinct()
            ->where('order_tag', WebUser::SUPERUSER_EMAIL)
            ->where('order_value', $email)
            ->when(!empty($this->month), function ($q) {
                $q
                    ->where('order_datetime', '>=', Carbon::createFromFormat('m/Y', $this->month)->startOfMonth())
                    ->where('order_datetime', '<=', Carbon::createFromFormat('m/Y', $this->month)->endOfMonth());
            })
            ->when(!empty($this->dateFrom), function ($q) {
                $q
                    ->where('order_datetime', '>=', Carbon::createFromFormat('Y-m-d', $this->dateFrom)->startOfDay());
            })
            ->when(!empty($this->dateTo), function ($q) {
                $q
                    ->where('order_datetime', '<=', Carbon::createFromFormat('Y-m-d', $this->dateTo)->endOfDay());
            })
            ->get()
            ->pluck('order_id');

        $orders = collect([]);
        foreach ($orderIds as $orderId) {
            $orders->push([
                'webuser' => $webuser,
                'order' => \OrderFactory::getById($orderId),
            ]);
        }

        return $orders;
    }

    protected function getSalesWebusers(): Collection
    {
        $webuserIds = \DB::table('tblWebUserOptions')
            ->select('user_id as webuser_id')
            ->distinct()
            ->where('user_option_tag', WebUser::SALES_REPRESENTANT)
            ->where('user_option_value', Constants::C_True_Char)
            ->get()
            ->pluck('webuser_id');

        $webusers = collect([]);

        foreach ($webuserIds as $webuserId) {
            $webuser = WebUserFactory::getById($webuserId);
            $fullname = $webuser->getFirstName() . ' ' . $webuser->getSurname();

            if (empty(trim($fullname)) || str_contains($fullname, 'test') || str_contains($fullname, 'Test')) continue;

            $webusers->push($webuser);
        }

        return $webusers;
    }

    public function export()
    {
        $data = $this->getData();

        $export = new ExtendedSalesStatisticsExport($data);
        return Excel::download($export, 'extended_sales_stastics.xlsx');
    }

    public function updatedMonth($month)
    {
        $this->emit('updatedMonth', $month);
    }

    public function resolveMonth($month)
    {
        $this->dateFrom = null;
        $this->dateTo = null;
        $this->month = $month;
        $this->page = 1;
    }

    public function updatedDateFrom($dateFrom)
    {
        $this->emit('updatedDateFrom', $dateFrom);
    }

    public function resolveDateFrom($dateFrom)
    {
        $this->month = null;
        $this->dateFrom = $dateFrom;
        $this->page = 1;
    }

    public function updatedDateTo($dateTo)
    {
        $this->emit('updatedDateTo', $dateTo);
    }

    public function resolveDateTo($dateTo)
    {
        $this->month = null;
        $this->dateTo = $dateTo;
        $this->page = 1;
    }

    protected function paginate($items, $perPage = 15, $page = null, $options = []): LengthAwarePaginator
    {
        $page = $page ?: (Paginator::resolveCurrentPage() ?: 1);
        $items = $items instanceof Collection ? $items : Collection::make($items);
        return new LengthAwarePaginator($items->forPage($page, $perPage), $items->count(), $perPage, $page, $options);
    }
}
