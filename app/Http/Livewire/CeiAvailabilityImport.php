<?php

namespace App\Http\Livewire;

use App\Imports;
use App\Imports\Processors\Cei\CeiAvailabilityProcessor;
use Buxus\Livewire\Component;
use Livewire\WithFileUploads;
use App\Imports\Helper\GeneralImportHelper;

class CeiAvailabilityImport extends Component
{
    use WithFileUploads;

    /** @var GeneralImportHelper $helper */
    protected $helper;
    public $file;
    public $submit;

    protected $rules = [
        'file' => 'required',
    ];

    public const CONFIG_KEY = 'cei_availability';

    public function __construct($id = null)
    {
        $this->helper = new GeneralImportHelper();
        $this->helper->setConfigKey(self::CONFIG_KEY);
    }

    public function render()
    {
        return view('livewire.cei-availability-import');
    }

    public function create($delayed = false)
    {
        ini_set('max_execution_time', 300);
        ini_set('memory_limit', '-1');
        $this->validate();

        if ($this->file) {
            $availability = config('imports.' . $this->helper->getConfigKey() . '.default_availability');
            $supplierPageId = config('imports.' . $this->helper->getConfigKey() . '.supplier_page_id');

            $path = $this->helper->save($this->file);

            $import = Imports::create([
                'path' => $path,
                'producer_ciselnik_id' => $supplierPageId,
                'type' => Imports::CEI_AVAILABILITY,
                'availability' => $availability,
                'status' => $delayed ? Imports::DELAYED : Imports::STILL_RUNNING,
            ]);

            $processor = new CeiAvailabilityProcessor($import->id, $delayed);
            $processor->import($path, 'local', \Maatwebsite\Excel\Excel::CSV);
        }

        session()->flash('success', 'Import pridaný úspešne.');
    }

    public function createDelayed()
    {
        $this->create(true);
    }
}
