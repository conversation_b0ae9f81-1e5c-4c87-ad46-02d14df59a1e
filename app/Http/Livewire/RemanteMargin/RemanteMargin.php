<?php

namespace App\Http\Livewire\RemanteMargin;

use App\Imports\Components\AbstractMarginComponent;
use App\MarginLevel;
use App\Supplier;
use Buxus\Livewire\Component;

class RemanteMargin extends AbstractMarginComponent
{
    protected $type = MarginLevel::TYPE_REMANTE;

    public function render()
    {
        $this->producer = Supplier::where('name', 'REMANTE-SUPPLIER')->first();

        return parent::render();
    }
}
