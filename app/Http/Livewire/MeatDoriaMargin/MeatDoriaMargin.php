<?php

namespace App\Http\Livewire\MeatDoriaMargin;

use App\Imports\Components\AbstractMarginComponent;
use App\MarginLevel;
use App\Supplier;

class MeatDoriaMargin extends AbstractMarginComponent
{
    protected $type = MarginLevel::TYPE_MEAT_DORIA;

    public function render()
    {
        $this->producer = Supplier::where('name', 'MEAT-DORIA-SUPPLIER')->first();

        return parent::render();
    }
}
