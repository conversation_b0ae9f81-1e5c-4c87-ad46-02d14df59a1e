<?php

namespace App\Onix;

use App\Eshop\Order\RinopartsOrderItem;
use App\Eshop\OrderState;
use App\Models\WebUserNotification;
use Buxus\Util\PageIds;
use Buxus\Util\PageTypeID;
use Buxus\Util\PropertyID;
use Buxus\Util\PropertyTag;

class Onix extends \App\OnixLib\Onix
{
    static function getTestPartnerId()
    {
        return '2125';
    }

    /**
     * @param \Buxus\Eshop\Order\OrderInterface $buxus_order
     * @param $new_buxus_state
     * @param $onix_order
     */
    protected function updateBuxusOrderState($buxus_order, $new_buxus_state, $onix_order)
    {
        if($buxus_order->getOrderState() !== $new_buxus_state) {
            WebUserNotification::addOnixNotification(
                $onix_order->Ns_Number_Partner,
                config('notifications.types.order'),
                $buxus_order->getOrderId()
            );
        }
        parent::updateBuxusOrderState($buxus_order, $new_buxus_state, $onix_order);
    }

    /**
     * @param \Buxus\Eshop\Order\OrderInterface $buxus_order
     * @param $onix_order
     */
    protected function updateBuxusOrderCustomData($buxus_order, $onix_order)
    {
        $order_custom_columns = [];
        foreach ($onix_order->CustomColumns as $custom_column) {
            if (in_array($custom_column->Name, [
                'Z_RINO_OB001_Cakajuci_DL_BA',
                'Z_RINO_OB001_Cakajuci_DL_ZA',
                'Z_RINO_OB001_Vybavena'
            ])) {
                $order_custom_columns[$custom_column->Name] = $custom_column->Value;
            }
        }


        if (isset($order_custom_columns['Z_RINO_OB001_Cakajuci_DL_BA']) && $order_custom_columns['Z_RINO_OB001_Cakajuci_DL_BA'] === -1) {
            $buxus_order->setData('onix_cakajuci_dl_ba', 1);
        } else {
            $buxus_order->setData('onix_cakajuci_dl_ba', 0);
        }

        if (isset($order_custom_columns['Z_RINO_OB001_Cakajuci_DL_ZA']) && $order_custom_columns['Z_RINO_OB001_Cakajuci_DL_ZA'] === -1) {
            $buxus_order->setData('onix_cakajuci_dl_za', 1);
        } else {
            $buxus_order->setData('onix_cakajuci_dl_za', 0);
        }
    }


    function onixStateToBuxusStateChange($onix_state)
    {

        if ($onix_state === null || $onix_state === '' || $onix_state === 'Zaevidovaná'
            || $onix_state == 'MODO' || $onix_state == 'ZAHRANICIE' || $onix_state == 'SPOLU') {
            return OrderState::STATE_PRIJATA;
        }
        if ($onix_state === 'Stornovaná') {
            return OrderState::STATE_STORNOVANA;
        }
        if ($onix_state === 'Expedovaný DL') {
            return OrderState::STATE_EXPEDOVANA;
        }
        if ($onix_state === 'Dodaná') {
            return OrderState::STATE_VYBAVENA;
        }
        if ($onix_state === 'Čaká na DL') {
            return OrderState::STATE_CAKA_NA_DL;
        }
        if ($onix_state === 'Čiastočne fakturovaná') {
            return OrderState::STATE_CIASTOCNE_FAKTUROVANA;
        }
        if ($onix_state === 'Čiastočne pokrytá') {
            return OrderState::STATE_CIASTOCNE_POKRYTA;
        }

        return null;
    }

    protected function getLabelOfTransportOnixItem()
    {
        return 'DOPRAVA';
    }

    protected function getFullOnixItemName($onix_item, $buxusOrderSite)
    {
        $item_name = $this->getOrderItemName($buxusOrderSite, $onix_item->CustomColumns, $onix_item->Stock_Items_Name);

        $customColumns = $this->getCustomColumnsFromOnixOrderItem($onix_item);
        if (isset($customColumns['code']) && strlen($customColumns['code'])) {
            $item_name = $customColumns['code'] . ' ' . $item_name;
        } else {
            $product_page = \PageFactory::builder()
                ->wherePageType(PageTypeID::ESHOP_PRODUCT_ID())
                ->wherePropertyValue(PropertyID::ONIX_NS_NUMBER_ID(), '=', $onix_item->Stock_Items_Ns_Number)
                ->first();
            if ($product_page && strlen($product_page->getValue(PropertyTag::ONIX_MAIN_CODE_TAG()))) {
                $item_name = $product_page->getValue(PropertyTAG::ONIX_MAIN_CODE_TAG()) . ' ' . $item_name;
            }
        }
        return $item_name;
    }

    private function getOrderItemName($site, $customColumns, $default)
    {
        $itemName = null;
        $itemNameEn = null;
        $itemNameCz = null;

        foreach ($customColumns as $customColumn) {
            if ($customColumn->Name == 'Z_RINO_00001_Nazov_1214770319') {
                $itemNameEn = $customColumn->Value;
            }
            if ($customColumn->Name == 'Z_RINO_00001_Nazov_-2131259568') {
                $itemNameCz = $customColumn->Value;
            }
        }

        if ($site == 'en') {
            $itemName = $itemNameEn;
        } else if ($site == 'cz') {
            $itemName = $itemNameCz;
        }

        return !empty($itemName) ? $itemName : $default;
    }

    /** @var RinopartsOrderItem $buxus_item */
    protected function getOnixNsNumber($buxus_item)
    {
        return $buxus_item->getOnixNsNumber();
    }

    protected function newItemObject()
    {
        return new RinopartsOrderItem();
    }

    protected function getCustomColumnsFromOnixOrderItem($onix_item)
    {
        $customColumns = [];
        foreach ($onix_item->CustomColumns as $custom_column) {
            $customColumns[$custom_column->Name] = $custom_column->Value;
            if (substr($custom_column->Name, 0, 17) == 'Z_RINO_00001_Kod_') {
                $customColumns['code'] = $custom_column->Value;
            }
            if ($custom_column->Name === 'Z_RINO_OB002_Status_polozky') {
                $customColumns['state'] = $custom_column->Value;
            }
        }
        return $customColumns;
    }

    protected function getUndefinedProductPageId()
    {
        return PageIds::getUndefinedProduct();
    }

}
