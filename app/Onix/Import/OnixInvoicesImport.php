<?php

namespace App\Onix\Import;

use App\Delivery\DeliveryManager;
use App\Eshop\Product\ProductExternalResources;
use App\Invoice\CreditNote;
use App\Models\WebUserNotification;
use App\Onix\Onix;
use App\OnixLib\Jobs\OnixInvoicesImportJob;
use App\OnixLib\Loggers\OnixInvoicesImportLogger;
use Buxus\Error\ErrorReporter;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class OnixInvoicesImport
{
    const ENCLOSURE_TYPE_INVOICE = 1;
    const ENCLOSURE_TYPE_CREDIT_NOTE = 2;
    const ENCLOSURE_TYPE_DELIVERY_NOTE = 3;

    /** @var OnixInvoicesImportLogger */
    protected $logger;


    /**
     * OnixInvoicesImport constructor.
     * @param OnixInvoicesImportLogger $logger
     */
    public function __construct(OnixInvoicesImportLogger $logger)
    {
        $this->logger = $logger;
    }


    public function run()
    {
        try {
            $this->logger->info('Starting import');
            $job = new OnixInvoicesImportJob($this->logger);

            if (Onix::isSync()) {
                $job->handle();
            } else {
                dispatch($job);
            }
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
            ErrorReporter::reportSilent($e);
            echo $e->getMessage();
        }
    }

    private function removeEnclosure($enclosure_record_id)
    {
        if (empty($enclosure_record_id)) {
            return;
        }

        $enclosure = DB::table('onix_enclosures')->where('enclosure_record_id', '=', $enclosure_record_id)->first();

        if (Storage::disk('local')->exists($enclosure->path)) {
            Storage::disk('local')->delete($enclosure->path);
        }

        DB::table('onix_enclosures')->where('enclosure_record_id', '=', $enclosure_record_id)->delete();

    }

    private function removeDocument($doc_record_id, $document_type_id)
    {
        if (empty($doc_record_id) || empty($document_type_id)) {
            return;
        }

        $query = DB::table('onix_enclosures')
            ->where('doc_record_id', '=', $doc_record_id)
            ->where('enclosure_type_id', '=', $document_type_id);
        $documents = $query->get();
        foreach ($documents as $document) {
            $this->removeEnclosure($document->enclosure_record_id);
        }
        $query->delete();

//        app('buxus:fulltext-search:manager')
//            ->getIndex('eshop_documents')
//            ->remove($doc_record_id);
    }

    public function processImport()
    {
//        for ($d = 1; $d <= 28; $d++) {
//            $this->importInvoices(null, sprintf('2022-02-%02d', $d));
//        }
//        for ($d = 1; $d <= 31; $d++) {
//            $this->importInvoices(null, sprintf('2022-03-%02d', $d));
//        }
//        for ($d = 1; $d <= 30; $d++) {
//            $this->importInvoices(null, sprintf('2022-04-%02d', $d));
//        }
//        for ($d = 1; $d <= 22; $d++) {
//            $this->importInvoices(null, sprintf('2022-05-%02d', $d));
//        }


        $this->importInvoices(null, Carbon::now()->subDays(1)->toDateString());
        $this->importInvoices();

        $this->importCreditNotes(null, Carbon::now()->subDays(1)->toDateString());
        $this->importCreditNotes();

        $this->importDeliveryNotes(null, Carbon::now()->subDays(1)->toDateString());
        $this->importDeliveryNotes();
        $this->logger->info('DONE.');
    }

    function importInvoices($partner_id = null, $date = 'today')
    {
        $this->logger->info('Import invoices for date: ' . $date);
        $onix = new Onix([
            'logger' => $this->logger
        ]);
        $invoices = $onix->getInvoices($partner_id, $date);
        $this->importDocuments($onix, $invoices, self::ENCLOSURE_TYPE_INVOICE);
    }

    function importDeliveryNotes($partner_id = null, $date = 'today')
    {
        $this->logger->info('Import delivery notes for date: ' . $date);
        $onix = new Onix([
            'logger' => $this->logger
        ]);
        $deliveryNotes = $onix->getDeliveryNotes($partner_id, $date);
        $this->importDocuments($onix, $deliveryNotes, self::ENCLOSURE_TYPE_DELIVERY_NOTE);
    }

    function importCreditNotes($partner_id = null, $date = 'today')
    {
        $this->logger->info('Import credit notes for date: ' . $date);
        $onix = new Onix([
            'logger' => $this->logger
        ]);
        $creditNotes = $onix->getCreditNotes($partner_id, $date);
        $this->importDocuments($onix, $creditNotes, self::ENCLOSURE_TYPE_CREDIT_NOTE);
    }


    private function importDocuments($onix, $documents, $enclosureTypeId)
    {
        foreach ((array)$documents as $document) {
            try {
                $invoice_data = [
                    'doc_record_id' => $document->IdRecord,
                    'inserted' => date('Y-m-d H:i:s'),
                    'onix_date_changed' => date('Y-m-d H:i:s', strtotime($document->Date_Changed)),
                    'onix_date_document' => date('Y-m-d H:i:s', strtotime($document->Date_Document)),
                    'partner_id' => $document->Ns_Number_Partner,
                    'enclosure_type_id' => $enclosureTypeId,
                    'path' => '',
                    'code' => $document->Ns_Code,
                    'vs' => $document->Variable_Symbol,
                    'sum' => $document->Sum,
                    'curr' => $document->Curr,
                    'commission' => $document->Commission,
                    'due_date' => date('Y-m-d H:i:s', strtotime($document->Date_Due)),
                    'payment_status' => $document->Payment_Status,
                    'payment_remaining' => $document->Payment_Remaining,
                    'sum_vat' => $document->Sum_Vat,
                    'external_number' => $document->External_Number,
                ];

                try {
                    foreach ($document->CustomColumns as $column) {
                        if ($column->Name == 'Z_RINO_DL002_CisloZasielky_TXT') {
                            $invoice_data['carrier_tracking_number'] = $column->Value ?? null;
                        }

                        if ($column->Name == 'Z_RINO_DL002_Prepravca_zasielky') {
                            $invoice_data['carrier'] = $column->Value ?? null;
                        }
                    }
                } catch (\Throwable $e) {
                    ErrorReporter::reportSilent($e);
                }

                $trackingAvailable = !empty($invoice_data['carrier_tracking_number']) && !empty($invoice_data['carrier']) && $enclosureTypeId == self::ENCLOSURE_TYPE_DELIVERY_NOTE;

                if ((!$trackingAvailable) && $enclosureTypeId == self::ENCLOSURE_TYPE_DELIVERY_NOTE) {
                    $invoice_data = $this->processAlreadySavedTracking($invoice_data);
                }

                $trackingAvailable = !empty($invoice_data['carrier_tracking_number']) && !empty($invoice_data['carrier']) && $enclosureTypeId == self::ENCLOSURE_TYPE_DELIVERY_NOTE;

                if (!$trackingAvailable && $enclosureTypeId == self::ENCLOSURE_TYPE_DELIVERY_NOTE) {
                    $invoice_data = $this->processExternalTracking($invoice_data);
                }

                if (strtotime($invoice_data['onix_date_document']) < strtotime('2022-02-01')) {
                    continue;
                }

                $enclosureData = null;

                foreach ($document->Enclosures as $enclosure) {
                    if (strpos($enclosure->Name, 'Faktúra') !== false ||
                        strpos($enclosure->Name, 'Dobropis') !== false ||
                        strpos($enclosure->Name, 'Dodací') !== false
                    ) {
                        if ($enclosureData === null || $enclosureData->IdRecord < $enclosure->IdRecord) {
                            $enclosureData = $enclosure;
                        }
                    } else {
                        // echo $enclosure->Name . "\n";
                    }
                }

                if (!empty($enclosureData)) {
                    $invoice_data['enclosure_record_id'] = $enclosureData->IdRecord;
                    $invoice_data['extension'] = $enclosureData->Extension;
                    $invoice_data['doc_name'] = $enclosureData->Name;
                }

                $trackingAvailable = !empty($invoice_data['carrier_tracking_number']) && !empty($invoice_data['carrier']) && $enclosureTypeId == self::ENCLOSURE_TYPE_DELIVERY_NOTE;

                if (empty($invoice_data['enclosure_record_id']) && !$trackingAvailable) {
                    continue;
                }

                if (!empty($invoice_data['enclosure_record_id'])) {
                    $content = $onix->getEnclosureContent($enclosureData->IdRecord);

                    if (empty($content)) {
                        continue;
                    }

                    $path = 'onix-enclosures-' . $invoice_data['enclosure_type_id'] . '/' . $invoice_data['partner_id'] . '/' . $invoice_data['enclosure_record_id'] . '.' . $invoice_data['extension'];
                    $invoice_data['path'] = $path;

                    $this->logger->info("Content length (" . $enclosureData->IdRecord . "): " . strlen($content) . " path:" . $path);
                }

                if ($enclosureTypeId == self::ENCLOSURE_TYPE_CREDIT_NOTE) {
                    $this->validateCreditNoteTransactionState($document->IdRecord, $document->Payment_Remaining);
                }

                $enclosureExists = DB::table('onix_enclosures')
                    ->where('doc_record_id', $invoice_data['doc_record_id'])
                    ->where('enclosure_type_id', $invoice_data['enclosure_type_id'])
                    ->get()->count();

                $this->removeDocument($invoice_data['doc_record_id'], $invoice_data['enclosure_type_id']);

                if (!empty($path) && !empty($content)) {
                    Storage::disk('local')->put($path, $content);
                }

                DB::table('onix_enclosures')->insert($invoice_data);

                if(!$enclosureExists) {
                    WebUserNotification::addOnixNotification(
                        $invoice_data['partner_id'], $invoice_data['enclosure_type_id'],
                        $invoice_data['doc_record_id']);
                }

                app('buxus:fulltext-search:manager')
                    ->getIndex('eshop_documents')
                    ->indexEshopDocument($invoice_data['doc_record_id']);

            } catch (\Throwable $e) {
                $this->logger->error($e->getMessage());
                ErrorReporter::reportSilent($e);
            }
        }
    }

    protected function processExternalTracking($invoice_data)
    {
        try {
            $vs = $invoice_data['vs'];
            $data = DeliveryManager::runTrackings($vs);

            if (empty($data)) {
                return $invoice_data;
            }

            foreach ($data as $carrier => $trackingNumbers) {
                $invoice_data['carrier_tracking_number'] = implode(',', $trackingNumbers);
                $invoice_data['carrier'] = $carrier;
            }
        } catch (\Throwable $e) {
            ErrorReporter::reportSilent($e);
        }

        return $invoice_data;
    }

    protected function processAlreadySavedTracking($invoice_data)
    {
        $vs = $invoice_data['vs'];

        $record = DB::table('onix_enclosures')
            ->where('vs', '=', $vs)
            ->where('enclosure_type_id', '=', self::ENCLOSURE_TYPE_DELIVERY_NOTE)
            ->whereNotNull('carrier_tracking_number')
            ->first();

        if (empty($record)) {
            return $invoice_data;
        }

        if (empty($record->carrier_tracking_number) || empty($record->carrier)) {
            return $invoice_data;
        }

        if (!in_array($record->carrier, $this->getExternallyManagedCarriers())) {
            return $invoice_data;
        }

        $invoice_data['carrier_tracking_number'] = $record->carrier_tracking_number;
        $invoice_data['carrier'] = $record->carrier;

        return $invoice_data;
    }

    protected function getExternallyManagedCarriers(): array
    {
        return [
            'SPS',
            'ExpressOne',
        ];
    }

    protected function validateCreditNoteTransactionState($recordId, $paymentRemaining)
    {
        try {
            $creditNote = new CreditNote($recordId);

            if (!$creditNote->hasOngoingTransaction()) {
                return;
            }

            if ($paymentRemaining > 0) {
                return;
            }

            $creditNote->finishOngoingTransactions();
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
            ErrorReporter::reportSilent($e);
        }
    }
}
