<?php
namespace App\Onix\Import;

use App\Eshop\Product\ProductExternalResources;
use App\Onix\Onix;
use App\OnixLib\Jobs\OnixPartnersImportJob;
use App\OnixLib\Loggers\OnixPartnersImportLogger;

class OnixPartnersImport
{
    /** @var OnixPartnersImportLogger */
    protected $logger;



    protected $csvFile = null;

    /**
     * OnixPartnersImpor constructor.
     * @param OnixPartnersImportLogger $logger
     */
    public function __construct(OnixPartnersImportLogger $logger, $csvFile = null)
    {
        $this->logger = $logger;
        $this->csvFile = $csvFile;
    }


    public function run()
    {
        try {
            $this->logger->info('Starting import');
            $job = new OnixPartnersImportJob($this->logger, $this->csvFile);

            if (Onix::isSync()) {
                $job->handle();
            } else {
                dispatch($job);
            }
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
            echo $e->getMessage();
        }
    }


    public function processImport()
    {

    }


    public function importPartnersFromOnix()
    {
        return;

        $onix = new Onix();
        $onix->updatePartners();
        $items = $onix->getPartners();


        foreach ($items as $item) {
            if($item->Ns_Number != Onix::getTestPartnerId()) {
                continue;
            }

            // Partner_Type
            // 1 = fyzicka osoba
            // -2 = obcan (nezdan osoba)
            // 0 = ostatne
            // 2 = pravnicka osoba
            // .....


            // najdem pouzivatela ktory ma nastavene onix_partner_id = Ns_Number
            $user_ids = \BuxusDB::get()->fetchCol("SELECT user_id FROM tblWebUserOptions WHERE user_option_tag = 'onix_partner_id' AND user_option_value = ?", [$item->Ns_Number]);
            if(is_array($user_ids)) {
                if(count($user_ids) === 0) {
//                    // new user
//                    $user = \WebUserFactory::create();
//                    $user->setUsername('onix_user_' . $item->Ns_Number);
//                    $this->updateUserProps($user, $item, true);
//                    $user->save();
                }
                else {
                    foreach ($user_ids as $user_id) {
                        // update user
                        $user = \WebUserFactory::getById($user_id);
                        $this->updateUserProps($user, $item);
                        $user->save();
                    }
                }
            }
        }

        $this->logger->info('DONE.');
    }

    /**
     * @param \Buxus\WebUser\Contracts\WebUser $user
     */
    private function updateUserProps($user, $item, $create = false)
    {
        if($create) {
            // properties update only when I'm creating new user
            // ...
        }
//        $user->setNickName($item->Name);
//        $user->setCustomOption('onix_partner_id', $item->Ns_Number);
//        $user->setCustomOption('customer_type', 'company');
//        $user->setCustomOption('ico', $item->Reg);
//        $user->setCustomOption('dic', $item->Tax);
//        $user->setCustomOption('drc', $item->Vat_No);

        ///$item->Addresses
    }


}
