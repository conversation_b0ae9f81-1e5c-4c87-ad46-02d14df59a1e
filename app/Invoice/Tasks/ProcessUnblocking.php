<?php

namespace App\Invoice\Tasks;

use App\Invoice\Facades\InvoiceFactory;
use Buxus\Core\Constants;
use Buxus\Logger\Logger;

class ProcessUnblocking
{
    protected $logger;

    public function __construct()
    {
        $this->logger = new Logger('invoice/process-unblocking');
    }

    public function handle()
    {
        $this->logger->info('Starting ProcessUnblocking task');
        $this->unblockInvoices();
        $this->unblockOrdering();
        $this->logger->info('ProcessUnblocking task completed');
    }

    public function unblockInvoices()
    {
        $userIds = \DB::table('tblWebUserOptions')
            ->where('user_option_tag', 'block_invoice_automatic')
            ->where('user_option_value', Constants::C_True_Char)
            ->get()->pluck('user_id');

        $this->logger->info('Processing invoice unblocking', ['blocked_users_count' => count($userIds)]);

        $users = [];

        foreach ($userIds as $userId) {
            $users[] = \WebUserFactory::getById($userId);
        }

        $unblockedCount = 0;
        foreach ($users as $user) {
            $this->logger->info('Processing user ' . $user->getUserId());
            $onixPartnerId = $user->getOnixPartnerId();

            $invoices = InvoiceFactory::getUnpaidInMoreThan(7, $onixPartnerId);

            if (empty($invoices)) {
                $user->setCustomOption('block_invoice_automatic', Constants::C_False_Char);
                $user->setCustomOption('block_invoice_automatic_note', '');
                $user->save();
                $unblockedCount++;

                $this->logger->info('User invoice unblocked', [
                    'user_id' => $user->getUserId(),
                    'onix_partner_id' => $onixPartnerId
                ]);
            }
        }

        $this->logger->info('Invoice unblocking completed', ['unblocked_users' => $unblockedCount]);
    }

    public function unblockOrdering()
    {
        $userIds = \DB::table('tblWebUserOptions')
            ->where('user_option_tag', 'invoice_after_due_date')
            ->where('user_option_value', Constants::C_True_Char)
            ->get()->pluck('user_id');

        $this->logger->info('Processing ordering unblocking', ['blocked_users_count' => count($userIds)]);

        $users = [];

        foreach ($userIds as $userId) {
            $users[] = \WebUserFactory::getById($userId);
        }

        $unblockedCount = 0;
        foreach ($users as $user) {
            $this->logger->info('Processing user ' . $user->getUserId());
            $onixPartnerId = $user->getOnixPartnerId();

            $invoices = InvoiceFactory::getUnpaidInMoreThan(10, $onixPartnerId);

            if (empty($invoices)) {
                $user->setCustomOption('invoice_after_due_date', Constants::C_False_Char);
                $user->save();
                $unblockedCount++;

                $this->logger->info('User ordering unblocked', [
                    'user_id' => $user->getUserId(),
                    'onix_partner_id' => $onixPartnerId
                ]);
            }
        }

        $this->logger->info('Ordering unblocking completed', ['unblocked_users' => $unblockedCount]);
    }
}
