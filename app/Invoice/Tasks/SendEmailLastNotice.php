<?php

namespace App\Invoice\Tasks;

use App\Invoice\Facades\InvoiceFactory;
use Buxus\Core\Constants;
use Buxus\Logger\Logger;
use Buxus\Util\PageIds;
use BuxusSite;
use Carbon\Carbon;

class SendEmailLastNotice
{
    protected $logger;

    public function __construct()
    {
        $this->logger = new Logger('invoice/send-email-last-notice');
    }

    public function handle()
    {
        $invoices = InvoiceFactory::getUnpaidInDays(7);
        $mailPageId = PageIds::getInvoice_5DaysAfterDueDateEmail();

        $this->logger->info('Starting SendEmailLastNotice task', ['invoices_count' => count($invoices)]);

        $emailsSentCount = 0;
        foreach ($invoices as $invoice) {
            $this->logger->info('Processing invoice ' . $invoice->vs);
            $emails = [];
            $users = $invoice->getUsers();

            foreach ($users as $user) {
                if ($user->shouldInvoiceEmailBeSent()) {
                    $emails[$user->getSite()] = $user->getInvoiceEmailAddress();
                }
            }

            $emails = array_unique($emails);

            if (!$invoice->wasEmailSent($mailPageId)) {
                foreach ($emails as $site => $address) {
                    if (!empty($address)) {
                        BuxusSite::executeInSiteContext($site, function () use ($address, $invoice, $mailPageId) {
                            $email = \Email::get($mailPageId);
                            $email->setRecipientsAddresses([$address]);
                            $email->addAttachment($invoice->getPathForEmail(), $invoice->getFilenameForEmail());
                            $email->send();

                            $invoice->logEmail($mailPageId);
                        });
                    }
                }

                $emailsSentCount++;
                $this->logger->info('Last notice email sent', [
                    'invoice_vs' => $invoice->vs,
                    'recipients_count' => count($emails)
                ]);
            }
        }

        $invoices = InvoiceFactory::getUnpaidInMoreThan(7);
        $this->logger->info('Processing automatic blocking for overdue invoices', ['overdue_invoices_count' => count($invoices)]);

        $blockedUsersCount = 0;
        foreach ($invoices as $invoice) {
            $users = $invoice->getUsers();

            foreach ($users as $user) {
                $user->setCustomOption('block_invoice_automatic', Constants::C_True_Char);
                $user->setCustomOption('block_invoice_automatic_note', 'Automaticky zablokované systémom BUXUS dňa ' . Carbon::now()->toDateString());
                $user->save();
                $blockedUsersCount++;

                $this->logger->info('User automatically blocked', [
                    'user_id' => $user->getUserId(),
                    'invoice_vs' => $invoice->vs
                ]);
            }
        }

        $this->logger->info('SendEmailLastNotice task completed', [
            'emails_sent' => $emailsSentCount,
            'users_blocked' => $blockedUsersCount
        ]);
    }
}
