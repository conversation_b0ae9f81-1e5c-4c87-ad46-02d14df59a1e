<?php

namespace App\Models;

use App\Invoice\CreditNote;
use App\Invoice\Invoice;
use Buxus\Util\PageIds;
use BuxusSite;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EnclosureTransaction extends Model
{
    use HasFactory;

    public const STATUS_REQUESTED = 0;
    public const STATUS_DONE = 1;
    public const STATUS_DENIED = 2;

    protected $fillable = [
        'id',
        'invoice_enclosure_record_id',
        'credit_note_enclosure_record_id',
        'invoice_vs',
        'credit_note_vs',
        'transaction_sum',
        'verification_hash',
        'status',
    ];

    public static function verifyTransactionConfirmation(): ?EnclosureTransaction
    {
        $request = request();
        $hash = $request->get('hash');
        $transactionId = $request->get('transactionId');

        /** @var EnclosureTransaction $transaction */
        $transaction = self::find($transactionId);

        if (!$transaction) {
            return null;
        }

        if ($transaction->verification_hash !== $hash) {
            return null;
        }

        $transaction->status = self::STATUS_DONE;
        $transaction->save();


        $invoice = new Invoice($transaction->invoice_enclosure_record_id);
        $creditNote = new CreditNote($transaction->credit_note_enclosure_record_id);

        $invoice->processValuesOffsetting($creditNote);

        $transaction->sendConfirmationMail();

        return $transaction;
    }

    public static function verifyTransactionDenial(): ?EnclosureTransaction
    {
        $request = request();
        $hash = $request->get('hash');
        $transactionId = $request->get('transactionId');

        /** @var EnclosureTransaction $transaction */
        $transaction = self::find($transactionId);

        if (!$transaction) {
            return null;
        }

        if ($transaction->verification_hash !== $hash) {
            return null;
        }

        $transaction->markCreditNotePaid();
        $transaction->status = self::STATUS_DENIED;
        $transaction->save();

        return $transaction;
    }


    protected function sendConfirmationMail()
    {
        $invoice = new Invoice($this->invoice_enclosure_record_id);
        $emails = [];
        $mailPageId = PageIds::getCreditNoteOffsetConfirmationUserMail();
        $users = $invoice->getUsers();

        foreach ($users as $user) {
            if ($user->shouldInvoiceEmailBeSent()) {
                $emails[$user->getSite()] = $user->getInvoiceEmailAddress();
            }
        }

        $emails = array_unique($emails);

        if (!$invoice->wasEmailSent($mailPageId)) {
            $creditNote = new CreditNote($this->credit_note_enclosure_record_id);

            foreach ($emails as $site => $address) {
                if (!empty($address)) {
                    BuxusSite::executeInSiteContext($site, function () use ($address, $invoice, $mailPageId, $creditNote) {
                        $email = \Email::get($mailPageId);
                        $email->setRecipientsAddresses([
                            $address
                        ]);
                        $email->setDataTag('CREDIT_NOTE_VS', $creditNote->creditNote->vs);
                        $email->setDataTag('INVOICE_VS', $invoice->vs);
                        $email->setDataTag('TRANSACTION_SUM', \PriceViewer::formatRawPrice($this->transaction_sum, $invoice->currency));
                        $email->send();

                        $invoice->logEmail($mailPageId);
                    });
                }
            }
        }
    }

    public function markCreditNotePaid()
    {
        $creditNote = new CreditNote($this->credit_note_enclosure_record_id);

        \DB::table('onix_enclosures')
            ->where('enclosure_record_id', $creditNote->creditNote->enclosure_record_id)
            ->update([
                'payment_remaining' => 0,
                'payment_status' => $creditNote->creditNote->payment_remaining,
            ]);
    }
}
