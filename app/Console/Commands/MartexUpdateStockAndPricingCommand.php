<?php

namespace App\Console\Commands;

use App\Imports\Processors\Martex\MartexStockAndPricing;
use Buxus\Error\ErrorReporter;
use Illuminate\Console\Command;
use Maatwebsite\Excel\Facades\Excel;
use Storage;
use Throwable;
use ZipArchive;

class MartexUpdateStockAndPricingCommand extends Command
{
    protected $signature = 'martex:update-stock-and-pricing';
    protected $description = 'Updates stock and pricing for Martex supplier';

    public function handle()
    {
        ini_set('memory_limit', '4096M');

        $path = config('imports.martex.update_path');

        $filePath = $this->prepareImportFile($path);

        if (Storage::disk('local')->exists($path)) {
            $file = Storage::disk('local')->get($path);
        }

        if (!empty($file)) {
            $processor = new MartexStockAndPricing();
            Excel::import($processor, storage_path('app/' . $path), null, \Maatwebsite\Excel\Excel::TSV);
        }

        if (Storage::disk('local')->exists(dirname($path) . '/OFERTA.ZIP')) {
            if (Storage::disk('local')->exists(dirname($path) . '_latest/OFERTA.ZIP')) {
                Storage::disk('local')->delete(dirname($path) . '_latest/OFERTA.ZIP');
            }

            Storage::disk('local')->move(dirname($path) . '/OFERTA.ZIP', dirname($path) . '_latest/OFERTA.ZIP');
        }

        if (empty($filePath)) {
            return Command::FAILURE;
        }

        if (Storage::disk('local')->exists(dirname($path) . '/OFERTA.txt')) {
            if (Storage::disk('local')->exists(dirname($path) . '_latest/OFERTA.txt')) {
                Storage::disk('local')->delete(dirname($path) . '_latest/OFERTA.txt');
            }

            Storage::disk('local')->move(dirname($path) . '/OFERTA.txt', dirname($path) . '_latest/OFERTA.txt');
        }

        return Command::SUCCESS;
    }

    protected function prepareImportFile($path)
    {
        $zip = new ZipArchive();
        $dirPath = storage_path('app/' . dirname($path));
        $failed = false;

        try {
            if ($zip->open($dirPath . '/OFERTA.ZIP')) {
                $zip->extractTo($dirPath);
                $zip->close();
            }
        } catch (Throwable $e) {
            ErrorReporter::reportSilent($e);
            $failed = true;
        }

        if (!$failed) {
            return $dirPath . '/OFERTA.txt';
        }

        try {
            exec('unzip ' . $dirPath . '/OFERTA.ZIP -d ' . $dirPath);
        } catch (Throwable $e) {
            ErrorReporter::reportSilent($e);
            $failed = true;
        }

        if ($failed) {
            return null;
        }

        return $dirPath . '/OFERTA.txt';
    }
}
