<?php

namespace App\Console\Commands;

use App\Imports\Processors\SpecialTurbo\SpecialTurboStockAndPricing;
use Illuminate\Console\Command;
use Storage;

class SpecialTurboUpdateStockAndPricingCommand extends Command
{
    protected $signature = 'special-turbo:update-stock-and-pricing';
    protected $description = 'Updates stock and pricing for Special Turbo supplier';

    public function handle()
    {
        $path = config('imports.special_turbo.update_path');

        if (Storage::disk('local')->exists($path)) {
            $file = Storage::disk('local')->get($path);
        }

        if (!empty($file)) {
            $processor = new SpecialTurboStockAndPricing();
            $processor->import(storage_path('app/' . $path));
        }
    }
}
