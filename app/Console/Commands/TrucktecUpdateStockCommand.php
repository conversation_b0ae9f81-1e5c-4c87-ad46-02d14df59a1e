<?php

namespace App\Console\Commands;

use App\Imports\Processors\Trucktec\TrucktecStock;
use Illuminate\Console\Command;
use Maatwebsite\Excel\Excel;
use Storage;

class TrucktecUpdateStockCommand extends Command
{
    protected $signature = 'trucktec:update-stock';
    protected $description = 'Updates stock and pricing for Trucktec supplier';

    public function handle()
    {
        $path = config('imports.trucktec_availability.update_path');

        if (Storage::disk('local')->exists($path)) {
            $file = Storage::disk('local')->get($path);
        }

        if (!empty($file)) {
            $processor = new TrucktecStock();
            $processor->import(storage_path('app/' . $path));
        }
    }
}
