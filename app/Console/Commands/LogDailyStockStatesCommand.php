<?php

namespace App\Console\Commands;

use Buxus\Util\PropertyID;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class LogDailyStockStatesCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'stock:log-daily-states';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Log stock states, once a day.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        DB::insert("INSERT INTO stock_state_log (buxus_product_id, stock_value, created_at)
            SELECT P.page_id,
                   cast(PV_STOCK.property_value as SIGNED) + COALESCE(cast(PV_STOCK_SECONDARY.property_value as SIGNED), 0),
                   NOW()
            FROM tblPages P
                INNER JOIN tblPagePropertyValues PV_ONIX ON P.page_id = PV_ONIX.page_id
                INNER JOIN tblPagePropertyValues PV_STOCK ON P.page_id = PV_STOCK.page_id
                LEFT JOIN tblPagePropertyValues PV_STOCK_SECONDARY ON P.page_id = PV_STOCK_SECONDARY.page_id
                    AND PV_STOCK_SECONDARY.property_id = ?
            WHERE
            P.page_state_id = 1
              AND PV_ONIX.property_id = ?
              AND PV_ONIX.property_value IS NOT NULL
              AND PV_ONIX.property_value <> ''
            AND PV_STOCK.property_id = ?
            AND PV_STOCK.property_value IS NOT NULL
            AND PV_STOCK.property_value <> ''", [
                PropertyID::ONIX_SECONDARY_STOCK_BALANCE_ID(),
                PropertyID::ONIX_ID_RECORD_ID(),
                PropertyID::ONIX_STOCK_BALANCE_ID(),
        ]);


        return 0;
    }
}
