<?php

namespace App\Console\Commands;

use App\Mail\TooManySearchNotification;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Mail;

class NotifyTooManySearchesCommand extends Command
{
    protected $signature = 'search:notify-too-many-searches';
    protected $description = 'Notifies about too many searches of one user';

    public function handle()
    {
        $searches = $this->getSearches();

        $searches = array_filter($searches, function ($item) {
            return !$item->isEmpty();
        });

        if (empty($searches)) {
            return;
        }

        Mail::to(['<EMAIL>'])
            ->send(new TooManySearchNotification($searches));
    }

    protected function getSearches()
    {
        $searchesOver40 = \DB::table('tblWebUserSearchLog')
            ->join('tblWebUsers', 'tblWebUserSearchLog.webuser_id', '=', 'tblWebUsers.user_id')
            ->whereDate('search_time', Carbon::createFromDate('2023', 1, 17)->toDateString())
            ->selectRaw('tblWebUsers.company_name, tblWebUserSearchLog.webuser_id, COUNT(DISTINCT search_term) as searches')
            ->groupBy('webuser_id')
            ->having('searches', '>', 40)
            ->having('searches', '<=', 80)
            ->get();

        $searchesOver80 = \DB::table('tblWebUserSearchLog')
            ->join('tblWebUsers', 'tblWebUserSearchLog.webuser_id', '=', 'tblWebUsers.user_id')
            ->whereDate('search_time', Carbon::createFromDate('2023', 1, 17)->toDateString())
            ->selectRaw('tblWebUsers.company_name, tblWebUserSearchLog.webuser_id, COUNT(DISTINCT search_term) as searches')
            ->groupBy('webuser_id')
            ->having('searches', '>', 80)
            ->having('searches', '<=', 120)
            ->get();

        $searchesOver120 = \DB::table('tblWebUserSearchLog')
            ->join('tblWebUsers', 'tblWebUserSearchLog.webuser_id', '=', 'tblWebUsers.user_id')
            ->whereDate('search_time', Carbon::createFromDate('2023', 1, 17)->toDateString())
            ->selectRaw('tblWebUsers.company_name, tblWebUserSearchLog.webuser_id, COUNT(DISTINCT search_term) as searches')
            ->groupBy('webuser_id')
            ->having('searches', '>', 120)
            ->get();

        return [
            '40' => $searchesOver40,
            '80' => $searchesOver80,
            '120' => $searchesOver120,
        ];
    }
}
