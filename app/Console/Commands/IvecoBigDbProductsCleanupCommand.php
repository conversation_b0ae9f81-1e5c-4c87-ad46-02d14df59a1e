<?php

namespace App\Console\Commands;

use <PERSON>uxus\Page\PageInterface;
use Buxus\Util\PropertyID;
use Buxus\Util\PropertyTag;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;

class IvecoBigDbProductsCleanupCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'rinoparts:iveco-products-cleanup';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'One time command to cleanup Iveco products';

    protected $countOnix = 0;
    protected $countAugustin = 0;
    protected $countIveco = 0;


    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $results = \DB::table('tblPagePropertyValues')
            ->selectRaw('count(*) as count, tblPagePropertyValues.property_value')
            ->where('tblPagePropertyValues.property_id', PropertyID::IVECO_BIG_DB_IMPORT_CODE_ID())
            ->groupBy('tblPagePropertyValues.property_value')
            ->having('count', '>', 1)
            ->get();

        foreach ($results as $i => $result) {
            if (empty($result->property_value)) {
                continue;
            }

            $pages = \PageFactory::builder()
                ->wherePropertyValue(PropertyID::IVECO_BIG_DB_IMPORT_CODE_ID(), $result->property_value)
                ->orderBy('creation_date')
                ->get();

            $count = 0;
            $pageIds = [];
            foreach ($pages as $page) {
                if (!empty($page->getValue(PropertyTag::ONIX_MAIN_CODE_TAG()))) {
                    $count++;
                    $pageIds[] = $page->getPageId();
                    if ($count > 1) {
                        unset($results[$i]);
                    }
                }
            }
        }

        foreach ($results as $result) {
            if (empty($result->property_value)) {
                continue;
            }

            $pages = \PageFactory::builder()
                ->wherePropertyValue(PropertyID::IVECO_BIG_DB_IMPORT_CODE_ID(), $result->property_value)
                ->orderBy('creation_date')
                ->get();

            $this->processResults($pages);
        }

        dd($this->countOnix, $this->countAugustin, $this->countIveco);
    }

    protected function processResults($pages)
    {
        if ($pages->count() < 2) {
            return;
        }


        $pages = $pages->reject(function (PageInterface $page, int $key) {
            return empty($page->getValue(PropertyTag::ONIX_MAIN_CODE_TAG())) && empty($page->getValue(PropertyTag::ESHOP_EUR_IVECO_PRICE_WITHOUT_VAT_TAG()));
        });

        if ($pages->count() < 2) {
            if ($pages->first() instanceof PageInterface) {
                $pages->first()->delete();
            }
        }

        $pages = $pages->slice(1);

        foreach ($pages as $page) {
            if ($page instanceof PageInterface) {
                $page->delete();
            }
        }
    }
}
