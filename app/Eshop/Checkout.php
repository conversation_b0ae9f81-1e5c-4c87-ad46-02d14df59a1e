<?php

namespace App\Eshop;

use App\Checkout\Process\DeliveryDataProcess;
use App\Checkout\Process\Form\DeliveryDataForm;
use App\Checkout\Process\Form\SummaryForm;
use Buxus\Eshop\EshopPages;
use Buxus\Util\PageIds;
use Eshop\ShoppingCart\Checkout\Process;

class Checkout extends \Eshop\ShoppingCart\Checkout\Checkout
{
    protected function initProcesses()
    {
        $this->addProcess(new Process\CartProcess(PageIds::getPageId(EshopPages::KOSIK1_OBSAH_KOSIKA), $this, Process\Form\CartForm::class), true);
        $this->addProcess(new Process\TransportPaymentProcess(PageIds::getPageId(EshopPages::KOSIK2_DODAVKA_A_PLATBA), $this, Process\Form\TransportPaymentForm::class));
        $this->addProcess(new DeliveryDataProcess(PageIds::getPageId(EshopPages::KOSIK3_DODACIE_UDAJE), $this, DeliveryDataForm::class));
        $this->addProcess(new Process\SummaryProcess(PageIds::getPageId(EshopPages::KOSIK4_SUHRN_OBJEDNAVKY), $this, SummaryForm::class));
    }

    public function getVisibleInvoiceCountry(): string
    {
        $invoiceCountry = $this->getInvoiceCountry();

        if ($invoiceCountry == 'sk') {
            return 'Slovensko';
        }

        if ($invoiceCountry == 'cz') {
            return 'Česká republika';
        }

        return $invoiceCountry;
    }
}
