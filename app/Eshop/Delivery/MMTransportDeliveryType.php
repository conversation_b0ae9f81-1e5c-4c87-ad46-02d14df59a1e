<?php

namespace App\Eshop\Delivery;

use Buxus\Eshop\Contracts\ShoppingCart;

class MMTransportDeliveryType extends RinopartsGenericDeliveryType
{
    public const TAG = 'mmtransport_carrier';

    public function isEnabled(ShoppingCart $cart)
    {
        if (\WebUserAuthentication::isAuthenticated()) {
            $user = \WebUserAuthentication::getUser();

            if ($user->canUseMMTransport()) {
                return parent::isEnabled($cart);
            }
        }

        return false;
    }
}
