<?php

namespace App\Eshop\Price;

use App\Eshop\Price\Margin\BaseMargin;

class MarginStrategy
{
    protected $item;
    protected $data;

    public function __construct($item)
    {
        $this->item = $item;
        $this->data = $this->run();
    }

    public function getData(): PriceDataWrapper
    {
        $price = $this->data['price'];
        $trace = $this->data['trace'];

        return new PriceDataWrapper($price, $trace);
    }

    public function run()
    {
        $margins = config('buxus_eshop.margins');
        $item = $this->item;

        foreach ($margins as $margin) {
            /** @var BaseMargin $margin */
            $margin = new $margin($item);
            if ($margin->canUse() && $margin->shouldUse()) {
                return [
                    'price' => $margin->getPrice(),
                    'trace' => $margin->getTrace(),
                ];
            }
        }

        return null;
    }
}
