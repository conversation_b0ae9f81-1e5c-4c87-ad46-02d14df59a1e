<?php

namespace App\Eshop\Price\Event;

use Carbon\Carbon;

class ActionPriceChangedEventHandler
{
    public function handle(ActionPriceChangedEvent $event)
    {
        \DB::table('tblActionPriceChanges')
            ->insertOrIgnore([
                'backtrace' => serialize($event->getBacktrace()),
                'page_id' => $event->getPageId(),
                'action_price_before' => $event->getActionPriceBefore(),
                'action_price_after' => $event->getActionPriceAfter(),
                'changed_at' => Carbon::now()->toDateTimeString(),
            ]);
    }
}
