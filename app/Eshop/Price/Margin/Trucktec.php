<?php

namespace App\Eshop\Price\Margin;

use App\Supplier;
use Buxus\Util\PropertyTag;

class Trucktec extends BaseMargin
{
    public function canUse()
    {
        return $this->getPriceType() == PropertyTag::TRUCKTEC_PRICE_WITHOUT_VAT_TAG();
    }

    public function getProducerCiselnikId()
    {
        return Supplier::where('name', 'TRUCKTEC-SUPPLIER')->first()->producer_ciselnik_id;
    }
}
