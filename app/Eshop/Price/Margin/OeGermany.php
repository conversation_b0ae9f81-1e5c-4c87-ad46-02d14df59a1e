<?php

namespace App\Eshop\Price\Margin;

use App\Supplier;
use Buxus\Util\PropertyTag;

class OeGermany extends BaseMargin
{
    public function canUse()
    {
        return $this->getPriceType() == PropertyTag::OE_GERMANY_PRICE_WITHOUT_VAT_TAG();
    }

    public function getProducerCiselnikId()
    {
        return Supplier::where('name', 'OE-GERMANY-SUPPLIER')->first()->producer_ciselnik_id;
    }
}
