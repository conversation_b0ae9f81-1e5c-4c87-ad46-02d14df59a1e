<?php

namespace App\Eshop\Price\Margin;

use App\Supplier;
use Buxus\Util\PropertyTag;

class SpecialTurbo extends BaseMargin
{
    public function canUse()
    {
        return $this->getPriceType() == PropertyTag::SPECIAL_TURBO_PRICE_WITHOUT_VAT_TAG();
    }

    public function getProducerCiselnikId()
    {
        return Supplier::where('name', 'SPECIAL-TURBO-SUPPLIER')->first()->producer_ciselnik_id;
    }
}
