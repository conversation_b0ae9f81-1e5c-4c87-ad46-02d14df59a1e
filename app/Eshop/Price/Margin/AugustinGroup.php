<?php

namespace App\Eshop\Price\Margin;

use App\Eshop\Price\ItemPriceDecorator\ExternalPrices;
use App\Supplier;
use Buxus\Eshop\Item\AbstractShopItem;
use Buxus\Util\PageIds;
use Buxus\Util\PropertyTag;

class AugustinGroup extends BaseMargin
{
    public function canUse()
    {
        return $this->getPriceType() == PropertyTag::AUGUSTIN_GROUP_PRICE_WITHOUT_VAT_TAG();
    }

    public function getProducerCiselnikId()
    {
        return Supplier::where('name', 'Augustin Group')->first()->producer_ciselnik_id;
    }
}
