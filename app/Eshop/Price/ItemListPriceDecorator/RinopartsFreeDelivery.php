<?php

namespace App\Eshop\Price\ItemListPriceDecorator;

use Buxus\Eshop\Contracts\ShoppingCart;
use Buxus\Eshop\FreeDelivery\FreeDeliveryManager;
use Buxus\Eshop\FreeDelivery\ItemPriceDecorator\FreeDeliveryTransportItemDecorator;
use Buxus\Eshop\Oraculum\RatableItemListInterface;
use Buxus\Eshop\Price\Price;
use Buxus\Eshop\Price\PriceType;

class RinopartsFreeDelivery extends \Buxus\Eshop\FreeDelivery\ItemListPriceDecorator\FreeDelivery
{
    /**
     * @param RatableItemListInterface $itemList
     * @throws \Exception
     */
    public function decorate(RatableItemListInterface $itemList)
    {
        if ($itemList instanceof ShoppingCart) {
            $checkout = $itemList->getCheckout();
            $delivery = $checkout->getTransportType();

            /**
             * @var FreeDeliveryManager $freeDeliveryManager
             */
            $freeDeliveryManager = app('buxus:free-delivery:manager');

            $freeLimit = $freeDeliveryManager->getTotalAmountForFreeDelivery();

            $limitTotalPriceTag = PriceType::TOTAL_PRICE_WITHOUT_VAT;
            $totalPriceTag = PriceType::TOTAL_PRICE;
            $totalPriceWithoutVatTag = PriceType::TOTAL_PRICE_WITHOUT_VAT;

            if (class_exists('Buxus\Vouchers\Price\VoucherPriceTypes')
                && $itemList->isPriceDefined(VoucherPriceTypes::PRICE_INCLUDING_VOUCHER_INCLUDING_VAT)
            ) {
                $totalPriceTag = VoucherPriceTypes::PRICE_INCLUDING_VOUCHER_INCLUDING_VAT;
                $totalPriceWithoutVatTag = VoucherPriceTypes::PRICE_INCLUDING_VOUCHER_WITHOUT_VAT;

                if (FreeDeliveryManager::CALCULATE_MODE_WITHOUT_VOUCHER !== config('free-delivery.calculate_mode')) {
                    $limitTotalPriceTag = VoucherPriceTypes::PRICE_INCLUDING_VOUCHER_INCLUDING_VAT;
                }
            }


            if (!is_numeric($freeLimit) || !$delivery || !$itemList->getPriceObject($totalPriceTag)) {
                return;
            }
            if ($itemList->getPriceObject($limitTotalPriceTag)->getValue() >= $freeLimit
                || $freeDeliveryManager->checkCartForExplicitFreeDeliveryItems($itemList)) {
                $priceSumWithVat = $itemList->getPriceObject($totalPriceTag)->getValue();
                $priceSumWithoutVat = $itemList->getPriceObject($totalPriceWithoutVatTag)->getValue();

                $deliveryPriceVat = new Price(PriceType::TOTAL_PRICE_WITH_DELIVERY, $priceSumWithVat);
                $deliveryPriceNoVat = new Price(
                    PriceType::TOTAL_PRICE_WITH_DELIVERY_WITHOUT_VAT,
                    $priceSumWithoutVat
                );
                $itemList->setPriceObject($deliveryPriceVat);
                $itemList->setPriceObject($deliveryPriceNoVat);

                $itemList->setPriceObject(new Price(PriceType::DELIVERY_PRICE, 0));
                $itemList->setPriceObject(new Price(PriceType::DELIVERY_PRICE_WITHOUT_VAT, 0));

                $itemList->setFinalPriceTag(PriceType::TOTAL_PRICE_WITH_DELIVERY);
                $itemList->setFinalPriceTagWithoutVat(PriceType::TOTAL_PRICE_WITH_DELIVERY_WITHOUT_VAT);

                $delivery->setPriceObject(new Price(PriceType::ITEM_PRICE, 0));
                $delivery->setPriceObject(new Price(PriceType::ITEM_PRICE_WITHOUT_VAT, 0));
                $delivery->setPriceObject(new Price(PriceType::ITEM_VAT, 0));
                $delivery->setPriceObject(new Price(PriceType::VAT, 0));
                $delivery->setPriceObject(new Price(PriceType::TOTAL_PRICE, 0));
                $delivery->setPriceObject(new Price(PriceType::TOTAL_PRICE_WITHOUT_VAT, 0));
            } else {
                $oldDeliveryPriceDefined = $delivery->isPriceDefined(FreeDeliveryTransportItemDecorator::PRICE_BEFORE_FREE_DELIVERY_TAG);

                if ($oldDeliveryPriceDefined && $delivery->getFinalPriceValue() == 0.0) {

                    $itemList->setPriceObject(new Price(PriceType::DELIVERY_PRICE,
                        $delivery->getPriceObject(FreeDeliveryTransportItemDecorator::PRICE_BEFORE_FREE_DELIVERY_TAG)->getValue()));
                    $itemList->setPriceObject(new Price(PriceType::DELIVERY_PRICE_WITHOUT_VAT,
                        $delivery->getPriceObject(FreeDeliveryTransportItemDecorator::PRICE_WITHOUT_VAT_BEFORE_FREE_DELIVERY_TAG)->getValue()));

                    $delivery->setFinalPriceTag(FreeDeliveryTransportItemDecorator::PRICE_BEFORE_FREE_DELIVERY_TAG);
                    $delivery->setFinalPriceTagWithoutVat(FreeDeliveryTransportItemDecorator::PRICE_WITHOUT_VAT_BEFORE_FREE_DELIVERY_TAG);

                    $priceSumWithVat = $itemList->getFinalPriceValue();
                    $priceSumWithoutVat = $itemList->getFinalPriceWithoutVatValue();

                    $deliveryPriceVat = new Price(
                        PriceType::TOTAL_PRICE_WITH_DELIVERY,
                        $priceSumWithVat
                        + $delivery->getPriceObject(FreeDeliveryTransportItemDecorator::PRICE_BEFORE_FREE_DELIVERY_TAG)->getValue()
                    );

                    $deliveryPriceNoVat = new Price(
                        PriceType::TOTAL_PRICE_WITH_DELIVERY_WITHOUT_VAT,
                        $priceSumWithoutVat
                        + $delivery->getPriceObject(FreeDeliveryTransportItemDecorator::PRICE_WITHOUT_VAT_BEFORE_FREE_DELIVERY_TAG)->getValue()
                    );

                    $itemList->setPriceObject($deliveryPriceVat);
                    $itemList->setPriceObject($deliveryPriceNoVat);

                    $itemList->setFinalPriceTag(PriceType::TOTAL_PRICE_WITH_DELIVERY);
                    $itemList->setFinalPriceTagWithoutVat(PriceType::TOTAL_PRICE_WITH_DELIVERY_WITHOUT_VAT);
                }
            }
        }
    }
}
