<?php

namespace App\Eshop\Price\ExternalPrices;

use App\Eshop\Price\ItemPriceDecorator\ExternalPrices;
use Buxus\Eshop\Item\AbstractShopItem;
use Buxus\Util\PropertyTag;

class IvecoOriginal extends BaseExternalPrice
{
    protected $item;
    protected $propertyTag;

    public function __construct(AbstractShopItem $item)
    {
        $this->propertyTag = PropertyTag::ESHOP_EUR_IVECO_PRICE_WITHOUT_VAT_TAG();
        parent::__construct($item);
    }

    public function canUse()
    {
        if (\WebUserAuthentication::isAuthenticated()) {
            if ($this->item->isOnixProduct()) {
                return true;
            }

            $user = \WebUserAuthentication::getUser();
            return $user->canUseIvecoSmallDb();
        }

        return false;
    }
}
