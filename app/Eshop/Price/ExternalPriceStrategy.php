<?php

namespace App\Eshop\Price;

use App\Eshop\Price\ExternalPrices\BaseExternalPrice;

class ExternalPriceStrategy
{
    protected $item;
    protected $data;

    public function __construct($item)
    {
        $this->item = $item;
        $this->data = $this->run();
    }

    public function getData(): PriceDataWrapper
    {
        $price = $this->data['price'];
        $trace = $this->data['trace'];

        return new PriceDataWrapper($price, $trace);
    }

    public function run()
    {
        $externalPrices = config('buxus_eshop.external_prices');
        $item = $this->item;


        foreach ($externalPrices as $externalPrice) {
            /** @var BaseExternalPrice $externalPrice */
            $externalPrice = new $externalPrice($item);
            if ($externalPrice->canUse() && $externalPrice->shouldUse()) {
                if (!empty($externalPrice->getPrice())) {
                    return [
                        'price' => $externalPrice->getPrice(),
                        'trace' => $externalPrice->getTrace(),
                    ];
                }
            }
        }

        return null;
    }
}
