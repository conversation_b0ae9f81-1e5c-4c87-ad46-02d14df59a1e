<?php

namespace App\Eshop\Cart;

use App\PriceOffers\PriceOffer;
use Buxus\Core\Constants;
use Buxus\Eshop\Oraculum\RatableItemInterface;
use Buxus\Eshop\Order\OrderItem;
use Buxus\Eshop\Price\PriceType;
use Mpdf\Config\ConfigVariables;
use Mpdf\Config\FontVariables;
use Mpdf\HTMLParserMode;
use Trans;

class DownloadManager
{
    public const PDF = 'pdf';
    public const HTML = 'html';

    /**
     * @var RatableItemInterface[]
     */
    protected $items;
    protected $total;
    protected $showOeNumbers;

    /**
     * @param $items RatableItemInterface[]
     */
    public function __construct(array $items)
    {
        $this->items = $items;
        $total = function ($items) {
            $total = 0;
            foreach ($items as $item) {
                $total += ($item->getPriceObject(PriceType::ITEM_PRICE_WITHOUT_VAT)->getValue() * $item->getAmount());
            }
            return $total;
        };
        $this->total = $total($items);

        $this->showOeNumbers = request(PriceOffer::WITH_OE_NUMBERS) === Constants::C_True_Char && count(array_filter($this->items, function ($item) {
                return ($item instanceof OrderItem);
            })) <= 0;
    }

    public function renderPDF()
    {
        $defaultConfig = (new ConfigVariables())->getDefaults();
        $fontDirs = $defaultConfig['fontDir'];

        $defaultFontConfig = (new FontVariables())->getDefaults();
        $fontData = $defaultFontConfig['fontdata'];


        $mpdf = new \Mpdf\Mpdf([
            'tempDir' => storage_path(),
            'fontDir' => array_merge($fontDirs, [
                public_path('buxus/assets/fonts/'),
            ]),
            'fontdata' => $fontData + [
                    'baijamjuree' => [
                        'R' => 'BaiJamjuree-Regular.ttf',
                        'B' => 'BaiJamjuree-Bold.ttf',
                        'useOTL' => 0xFF,
                        'useKashida' => 75,
                    ],
                ],
        ]);

        $mpdf->curlAllowUnsafeSslRequests = true;
        $mpdf->showImageErrors = true;

        $stylesheet = file_get_contents(public_path('buxus/assets/css/pdf.css'));
        $mpdf->SetTitle(\Trans::str('cart', 'Cenová ponuka'));
        $mpdf->writeHTML($stylesheet, HTMLParserMode::HEADER_CSS);

        $items = $this->items;
        $total = $this->total;
        $showOeNumbers = $this->showOeNumbers;

        $view = view('price-offers.pdf.template', compact('items', 'total', 'showOeNumbers'))->render();

        $mpdf->writeHTML(
            $view, HTMLParserMode::HTML_BODY
        );

        $mpdf->setHTMLFooter(view('pdf.partials.footer'));

        return $mpdf->Output('', 'S');
    }

    protected function renderHTML()
    {
        return view('price-offers.html.template', [
                'items' => $this->items,
                'total' => $this->total,
                'showOeNumbers' => $this->showOeNumbers,
            ]
        );
    }

    public function download($format = DownloadManager::PDF)
    {
        if ($format == DownloadManager::PDF) {
            $contents = $this->renderPDF();
            $filename = Trans::strParamed('cart', 'cenova-ponuka%s', ['.pdf']);
            $headers = [
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            ];
        }

        if ($format == DownloadManager::HTML) {
            $contents = $this->renderHTML();
            $filename = Trans::strParamed('cart', 'cenova-ponuka%s', ['.html']);
            $headers = [
                'Content-Type' => 'application/html',
                'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            ];
        }

        return response()->make($contents, 200, $headers);
    }

    public function showInBrowser($format = DownloadManager::PDF)
    {
        if ($format == self::PDF) {
            header('Content-Type:application/pdf');

            echo $this->renderPDF();
            exit;
        }

        header('Content-Type:application/html');
        return $this->renderHTML();
    }

}
