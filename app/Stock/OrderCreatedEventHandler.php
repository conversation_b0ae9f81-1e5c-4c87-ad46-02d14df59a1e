<?php

namespace App\Stock;

use App\DeliveryAddress;
use App\OnixLib\Libs\Reservations;
use App\Warehouse\Warehouse;
use Buxus\Error\ErrorReporter;
use Buxus\Eshop\Event\OrderCreatedEvent;
use Buxus\Util\PropertyTag;

class OrderCreatedEventHandler
{
    public function handle(OrderCreatedEvent $event)
    {
        try {
            $this->createDeliveryAddress($event);
            $items = $event->getOrder()->getItems();

            $balance = [];
            $reservations = [];

            foreach ($items as $item) {
                $pageId = $item->getPageId();
                $page = \PageFactory::get($pageId);

                if ($page) {
                    foreach ($item->getWarehouseReservationInfo() as $warehouse => $amount) {
                        Reservations::reserveProductForWarehouse($pageId, $event->getOrder()->getOrderId(), $amount, $warehouse);

                        $balance[$warehouse] = (float)$page->getValue(Warehouse::getStockPropertyForWarehouse($warehouse));
                        $reservations[$warehouse] = (float)$page->getValue(Warehouse::getReservationsPropertyForWarehouse($warehouse));

                        if (!is_null($balance[$warehouse])) {
                            $balance[$warehouse] -= $amount;
                            $page->setValue(Warehouse::getStockPropertyForWarehouse($warehouse), $balance[$warehouse] >= 0 ? $balance[$warehouse] : null);
                        }
                    }

                    if ((array_sum($balance) - array_sum($reservations)) <= 0) {
                        $page->setValue(PropertyTag::ESHOP_EUR_ACTION_PRICE_WITHOUT_VAT_TAG(), null);
                        $page->setValue(PropertyTag::ESHOP_EUR_ACTION_PRICE_WITHOUT_VAT_EN_TAG(), null);
                    }
                    $page->save();
                }
            }
        } catch (\Exception $e) {
            ErrorReporter::reportSilent($e);
        }
    }

    public function createDeliveryAddress(OrderCreatedEvent $event)
    {
        $order = $event->getOrder();

        DeliveryAddress::firstOrCreate([
            'webuser_id' => $order->getUserId(),
            'fullname' => $order->getDeliveryName(),
            'company_name' => $order->getDeliveryCompanyName(),
            'delivery_phone' => $order->getDeliveryPhone(),
            'street' => $order->getDeliveryStreet(),
            'city' => $order->getDeliveryCity(),
            'zip' => $order->getDeliveryZip(),
            'country' => $order->getDeliveryCountry(),
        ]);
    }
}
