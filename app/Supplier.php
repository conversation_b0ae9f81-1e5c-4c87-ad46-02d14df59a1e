<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Supplier extends Model
{
    use HasFactory;

    protected $table = 'producers';

    protected $fillable = [
        'name',
        'producer_ciselnik_id',
        'price_levels_on',
    ];

    protected $casts = [
        'price_levels_on' => 'boolean'
    ];

    public function marginLevels()
    {
        return $this->hasMany(MarginLevel::class, 'producer_id');
    }

    public function getCiselnikId()
    {
        return $this->producer_ciselnik_id;
    }
}
