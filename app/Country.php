<?php

namespace App;

class Country
{
    public static function getCountryName($country)
    {
        $mapping = config('buxus_authentication.country_codes_to_name_mapping');
        $uppercaseCountry = strtoupper($country);

        if (in_array($uppercaseCountry, array_keys($mapping))) {
            return $mapping[$uppercaseCountry];
        }

        return $country;
    }

    public static function getDeliveryCountryName($deliveryCountry)
    {
        if (strtoupper(trim($deliveryCountry)) == 'SK') {
            $deliveryCountry = 'Slovensko';
        }

        if (strtoupper(trim($deliveryCountry)) == 'CZ') {
            $deliveryCountry = 'Česko';
        }

        return $deliveryCountry;
    }
}
