<?php

namespace App;

use Buxus\Util\PageIds;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Imports extends Model
{
    use HasFactory;

    protected $fillable = [
        'path',
        'type',
        'status',
        'availability',
        'items_processed',
        'producer_ciselnik_id',
        'updates_processed',
        'deletes_processed',
        'creates_processed',
        'errors'
    ];

    const ALTERNATIVE_PRICES_IMPORT = 1;
    const IVECO_ORIGINAL_PURCHASE_PRICES = 2;
    const IVECO_ORIGINAL_BIG_DB = 3;
    const IVECO_ORIGINAL_BIG_DB_COM = 4;
    const AUGUSTIN_GROUP = 5;
    const MEC_DIESEL = 6;
    const NRF = 7;
    const FEBI_BILSTEIN = 8;
    const MOTORSERVICE = 9;
    const MOTORSERVICE_AVAILABILITY = 10;
    const CEI_AVAILABILITY = 11;

    const IVECO_ORIGINAL_STOCK_PRICES = 12;
    const EMINIA = 13;
    const CASCO = 14;
    const SPECIAL_TURBO = 15;
    const MARTEX = 16;
    const SABO = 17;
    const SABO_AVAILABILITY = 18;
    const REMANTE = 19;
    const OE_GERMANY = 20;
    const MEAT_DORIA = 21;
    const WEIGHT = 22;
    const ABAKUS = 23;
    const TRUCKTEC = 24;
    const AUGUSTIN_GROUP_BULKY_PARTS_ID = 9999991;
    const IVECO_ORIGINAL_STOCK_PRICES_ID = 9999997;
    const IVECO_ORIGINAL_PURCHASE_PRICES_ID = 9999998;
    const IVECO_ORIGINAL_BIG_DB_ID = 9999999;


    const PATHS = [
        self::ALTERNATIVE_PRICES_IMPORT => 'alternative-prices-import',
        self::IVECO_ORIGINAL_PURCHASE_PRICES => 'iveco-original-prices-import',
        self::IVECO_ORIGINAL_BIG_DB => 'iveco-original-big-db',
        self::AUGUSTIN_GROUP => 'augustin-group',
        self::NRF => 'nrf',
    ];

    const DONE = 0;
    const STILL_RUNNING = 1;
    const ERROR = 2;
    const DELAYED = 3;

    public static function getPath(int $type): ?string
    {
        if (array_key_exists($type, self::PATHS)) {
            return 'imports/' . self::PATHS[$type];
        }

        return null;
    }

    public static function getImports($producerCiselnikId, $type)
    {
        $imports = self::where('producer_ciselnik_id', $producerCiselnikId)->where('type', $type)->get();

        $imports = $imports->sortByDesc('created_at');

        $first = $imports->first();

        return $first;
    }

    public function getAlternativePricesImports(): array
    {
        $suppliers = \Ciselniky::get('product_catalog.supplier')->getAllValues();

        foreach ($suppliers as $supplier) {
            $imports[$supplier->getName()] = self::where('producer_ciselnik_id', $supplier->getId())
                ->where('type', self::ALTERNATIVE_PRICES_IMPORT)
                ->latest('created_at')
                ->first();
        }

        return $imports;
    }

    public function getAugustinGroupImports(): array
    {
        return $this->getImportsForSupplierAndType(
            config('imports.augustin_group.supplier_page_id'),
            self::AUGUSTIN_GROUP
        );
    }

    public function getMecDieselImports(): array
    {
        $supplier = \Ciselniky::get('product_catalog.supplier')->getValueById(config('imports.mec_diesel.supplier_page_id'));

        $imports[$supplier->getName()] = self::where('producer_ciselnik_id', $supplier->getId())
            ->where('type', self::MEC_DIESEL)
            ->latest('created_at')
            ->first();

        return $imports;
    }

    public function getNRFImports(): array
    {
        return $this->getImportsForSupplierAndType(
            config('imports.nrf.supplier_page_id'),
            self::NRF
        );
    }

    public function getFebiBilsteinImports(): array
    {
        return $this->getImportsForSupplierAndType(
            config('imports.febi_bilstein.supplier_page_id'),
            self::FEBI_BILSTEIN
        );
    }

    public function getMotorserviceImports(): array
    {
        return $this->getImportsForSupplierAndType(
            config('imports.motorservice.supplier_page_id'),
            self::MOTORSERVICE
        );
    }

    public function getMotorserviceAvailabilityImports(): array
    {
        return $this->getImportsForSupplierAndType(
            config('imports.motorservice.supplier_page_id'),
            self::MOTORSERVICE_AVAILABILITY
        );
    }

    public function getEminiaImports(): array
    {
        return $this->getImportsForSupplierAndType(
            config('imports.eminia.supplier_page_id'),
            self::EMINIA
        );
    }

    public function getCascoImports(): array
    {
        return $this->getImportsForSupplierAndType(
            PageIds::getCascoSupplier(),
            self::CASCO
        );
    }

    public function getSpecialTurboImports(): array
    {
        return $this->getImportsForSupplierAndType(
            PageIds::getSpecialTurboSupplier(),
            self::SPECIAL_TURBO
        );
    }

    public function getMartexImports(): array
    {
        return $this->getImportsForSupplierAndType(
            PageIds::getMartexSupplier(),
            self::MARTEX
        );
    }

    public function getSaboImports(): array
    {
        return $this->getImportsForSupplierAndType(
            PageIds::getSaboSupplier(),
            self::SABO
        );
    }

    public function getSaboAvailabilityImports(): array
    {
        return $this->getImportsForSupplierAndType(
            config('imports.sabo_availability.supplier_page_id'),
            self::SABO_AVAILABILITY
        );
    }

    public function getRemanteImports(): array
    {
        return $this->getImportsForSupplierAndType(
            PageIds::getRemanteSupplier(),
            self::REMANTE
        );
    }

    public function getOeGermanyImports(): array
    {
        return $this->getImportsForSupplierAndType(
            PageIds::getOeGermanySupplier(),
            self::OE_GERMANY
        );
    }

    public function getMeatDoriaImports(): array
    {
        return $this->getImportsForSupplierAndType(
            PageIds::getMeatDoriaSupplier(),
            self::MEAT_DORIA
        );
    }
    public function getCeiAvailabilityImports(): array
    {
        return $this->getImportsForSupplierAndType(
            config('imports.cei_availability.supplier_page_id'),
            self::CEI_AVAILABILITY
        );
    }

    public function getAbakusImports(): array
    {
        return $this->getImportsForSupplierAndType(
            config('imports.abakus.supplier_page_id'),
            self::ABAKUS
        );
    }

    public function getTrucktecImports(): array
    {
        return $this->getImportsForSupplierAndType(
            config('imports.trucktec.supplier_page_id'),
            self::TRUCKTEC
        );
    }

    public function getWeightImports(): array
    {
        $importedSuppliers = self::where('type', self::WEIGHT)
            ->orderBy('created_at')
            ->get();

        foreach ($importedSuppliers as $importedSupplier) {
            $supplier = \Ciselniky::get('product_catalog.supplier')->getValueById($importedSupplier->producer_ciselnik_id);
            $imports[$supplier->getName()] = $importedSupplier;
        }

        return $imports ?? [];
    }

    public function getIvecoStockPricesImports(): array
    {
        $imports['IVECO originál'] = self::where('type', self::IVECO_ORIGINAL_STOCK_PRICES)
            ->latest('created_at')
            ->first();

        return $imports;
    }

    protected function getImportsForSupplierAndType($supplierPageId, $type): array
    {
        $supplier = \Ciselniky::get('product_catalog.supplier')->getValueById($supplierPageId);

        $imports[$supplier->getName()] = self::where('producer_ciselnik_id', $supplier->getId())
            ->where('type', $type)
            ->latest('created_at')
            ->first();

        return $imports;
    }
}
