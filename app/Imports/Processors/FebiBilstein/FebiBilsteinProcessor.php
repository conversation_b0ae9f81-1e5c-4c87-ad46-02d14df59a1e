<?php

namespace App\Imports\Processors\FebiBilstein;

use App\Http\Livewire\FebiBilsteinImport;
use App\Imports;
use App\Imports\Helper\GeneralImportHelper;
use App\Imports\Jobs\FebiBilstein\FebiBilsteinImportJob;
use App\Imports\Jobs\FebiBilstein\RemovePriceJob;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Excel;

class FebiBilsteinProcessor implements ToCollection, WithHeadingRow
{
    use Importable;

    protected $availability;
    protected $import_id;
    protected $categories;

    protected $path;
    protected $pathPrevious;

    protected $supplier;

    /**
     * @var GeneralImportHelper
     */
    protected $helper;

    public function __construct(int $availability, int $import_id)
    {
        $this->availability = $availability;
        $this->import_id = $import_id;

        $this->helper = new GeneralImportHelper();
        $this->helper->setConfigKey(FebiBilsteinImport::CONFIG_KEY);

        $this->path = $this->helper->getStoragePath();
        $this->pathPrevious = $this->helper->getStoragePathPrevious();

        ini_set('memory_limit', '-1');
    }

    public function collection($collection)
    {
        $collection = $collection->filter(function ($item) {
            return !empty($item['febi_cislo']);
        });

        $this->processPrevious($collection);

        $import = Imports::find($this->import_id);
        $import->items_processed = $collection->count();
        $import->save();

        $last = $collection->pop();

        foreach ($collection as $item) {
            FebiBilsteinImportJob::dispatch($item, $this->import_id);
        }

        FebiBilsteinImportJob::dispatch($last, $this->import_id, true);
    }

    protected function processPrevious($collection)
    {
        Excel::toCollection(new FebiBilsteinPrevious(), $this->pathPrevious)
            ->first()
            ->pluck('febi_cislo')
            ->filter()
            ->diff($collection->pluck('febi_cislo'))
            ->each(fn($diffRow) => RemovePriceJob::dispatch($diffRow, $this->import_id));
    }

    public function headingRow(): int
    {
        return 1;
    }
}
