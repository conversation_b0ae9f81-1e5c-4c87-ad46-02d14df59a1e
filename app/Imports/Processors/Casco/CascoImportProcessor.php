<?php

namespace App\Imports\Processors\Casco;

use App\Http\Livewire\Casco\CascoImport;
use App\Imports\AbstractImportProcessor;
use App\Imports\Jobs\Casco\CascoImportJob;
use App\Imports\Jobs\Casco\RemoveStockJob;
use Buxus\Util\PropertyID;
use DB;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class CascoImportProcessor extends AbstractImportProcessor implements WithHeadingRow
{
    protected string $importJobClass = CascoImportJob::class;
    protected bool $shouldProcessPrevious = false;

    protected string $onQueue = 'buxus_iveco_big_db_import';
    protected $codeColumnKey = 'casco_kod';
    protected $priceColumnKey = 'cena';
    protected $titleColumnKey = 'nazov';

    protected $additionalProperties = [
        'kat1',
        'core_value',
        'vyrobca'
    ];

    public function __construct(int $availability = null, int $import_id = null, bool $dispatchAtMidnight = false, $additionalProperties = [])
    {
        parent::__construct($availability, $import_id, $dispatchAtMidnight, $additionalProperties);

        $this->helper->setConfigKey(CascoImport::CONFIG_KEY);
    }

    public function headingRow(): int
    {
        return 1;
    }

    /**
     * @param Collection $collection
     */
    protected function processPrevious($collection)
    {
        $currentSupplierCodes = DB::table('tblPagePropertyValues')
            ->where('property_id', PropertyID::CASCO_SUPPLIER_CODE_ID())
            ->where('property_value', '<>', '')
            ->get()
            ->pluck('property_value');

        $currentSupplierCodes
            ->diff($collection)
            ->each(function ($supplierCode) {
                dispatch(new RemoveStockJob($supplierCode, $this->import_id));
            });
    }
}
