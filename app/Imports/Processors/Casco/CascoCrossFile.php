<?php

namespace App\Imports\Processors\Casco;

use App\Imports\Jobs\Casco\CascoReferenceJob;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class CascoCrossFile implements ToCollection, WithHeadingRow, WithChunkReading, ShouldQueue
{
    use Importable;

    protected $timeout = 3600;
    protected bool $delayed;

    public function __construct($delayed = false)
    {
        $this->delayed = $delayed;
    }

    public function collection(Collection $collection)
    {
        $oeCodes = [];

        foreach ($collection as $item) {
            $oeCodes[$item['artno']][] = trim((string)$item['refno']);
            $oeCodes[$item['artno']] = array_filter($oeCodes[$item['artno']]);
        }

        $oeCodes = array_filter($oeCodes);

        foreach ($oeCodes as $key => $values) {
            if ($this->delayed) {
                CascoReferenceJob::dispatchAtMidnight($key, $values);
            } else {
                CascoReferenceJob::dispatch($key, $values);
            }
        }
    }

    public function headingRow()
    {
        return 1;
    }

    public function chunkSize(): int
    {
        return 100000;
    }
}
