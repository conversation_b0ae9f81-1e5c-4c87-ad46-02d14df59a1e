<?php

namespace App\Imports\Processors\IvecoBigDb;

use App\Imports;
use App\Imports\Jobs\IvecoBigDb\IvecoProductBigDbImportJob;
use App\Imports\Jobs\RemovePriceJob;
use App\IvecoBigDb;
use Buxus\Util\PageIds;
use Buxus\Util\PageTypeID;
use Buxus\Util\PropertyTag;
use BuxusSite;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\ToArray;
use Maatwebsite\Excel\Concerns\WithStartRow;
use Maatwebsite\Excel\Facades\Excel;

class IvecoBigDbXlsxProcessor implements ToArray, WithStartRow
{
    use Importable;

    protected $availability;
    protected $import_id;
    protected $categories;
    protected $site;

    protected $ivecoBigDb;

    protected $path;
    protected $pathPrevious;

    public function __construct($availability, $import_id, $site = 'sk')
    {
        $this->availability = $availability;
        $this->import_id = $import_id;
        $this->site = $site;

        $this->ivecoBigDb = $ivecoBigDb = new IvecoBigDb($site);

        $this->path = $ivecoBigDb->getStoragePath();
        $this->pathPrevious = $ivecoBigDb->getStoragePathPrevious();

        BuxusSite::pushSite($site);

        ini_set('memory_limit', '-1');
    }

    public function __destruct()
    {
        BuxusSite::popSite();
    }

    public function array($rows)
    {
        if (file_exists($this->pathPrevious)) {
            $importCodes = array_column($rows, '0');
            $importCodesPrevious = Excel::toArray(new IvecoBigDbPrevious(), $this->pathPrevious);
            $importCodesPrevious = array_column($importCodesPrevious[0], '0');
            $diff = array_diff($importCodesPrevious, $importCodes);

            foreach ($diff as $diffRow) {
                $job = new RemovePriceJob($diffRow, config('imports.iveco_big_db.price_property_tag'), $this->import_id, $this->site);
                dispatch($job)->onQueue('buxus_iveco_big_db_import_'. env('DB_DATABASE'));
            }
        }

        $import = Imports::find($this->import_id);
        $import->items_processed = count($rows);
        $import->save();

        $last = array_pop($rows);

        foreach ($rows as $row) {
            $job = new IvecoProductBigDbImportJob($row[0], $row[1], $row[2], $this->availability, $this->import_id, $this->getCategoryForImport($row[1]), false, $this->site);
            dispatch($job)->onQueue('buxus_iveco_big_db_import_'. env('DB_DATABASE'));
        }

        $job = new IvecoProductBigDbImportJob($last[0], $last[1], $last[2], $this->availability, $this->import_id, $this->getCategoryForImport($last[1]), true, $this->site);
        dispatch($job)->onQueue('buxus_iveco_big_db_import_'. env('DB_DATABASE'));
    }

    public function startRow(): int
    {
        return 2;
    }

    public function getCategoryForImport($name)
    {
        $name = \Buxus\Util\StringUtil::removeDiacritics($name);
        $categoryName = trim(substr($name, 0, 2));
        $categoryId = null;

        if (!is_array($this->categories)) {
            $page = \PageFactory::get(PageIds::getIvecoOriginalBigDb());
            $children = $page->getChildren();
            foreach ($children as $child) {
                $this->categories[$child->getValue(PropertyTag::TITLE_TAG())] = $child->getPageId();
            }
        } else {
            $categoryId = $this->categories[$categoryName];
        }

        if ($categoryId) {
            return $categoryId;
        }

        $page = \PageFactory::create(PageIds::getIvecoOriginalBigDb(), PageTypeID::FOLDER_ID());
        $page->setPageName($categoryName);
        $page->setValue(PropertyTag::TITLE_TAG(), $categoryName);
        $page->save();

        $this->categories[$categoryName] = $page->getPageId();

        return $page->getPageId();
    }
}
