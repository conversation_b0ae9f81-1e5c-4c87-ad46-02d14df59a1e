<?php

namespace App\Imports\Processors\OeGermany;

use App\Imports\Jobs\OeGermany\OeGermanyStockAndPricingJob;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class OeGermanyStockAndPricing implements ToCollection, WithHeadingRow
{
    use Importable;

    public function headingRow()
    {
        return 1;
    }

    public function collection(Collection $collection)
    {
        $collection = $collection->filter(function ($item) {
            return !empty($item['manufacturer_number']);
        });

        foreach ($collection as $item) {
            OeGermanyStockAndPricingJob::dispatch(
                $item['manufacturer_number'],
                $item['quantity_available'],
                $item['price'],
            );
        }

        return Command::SUCCESS;
    }
}
