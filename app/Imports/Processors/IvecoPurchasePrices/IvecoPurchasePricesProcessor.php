<?php

namespace App\Imports\Processors\IvecoPurchasePrices;

use App\Imports;
use App\Imports\Jobs\IvecoPurchasePrices\IvecoProductPurchasePricesImportJob;
use App\Imports\Jobs\RemovePriceJob;
use Buxus\Util\PropertyTag;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\ToArray;
use Maatwebsite\Excel\Facades\Excel;

class IvecoPurchasePricesProcessor implements ToArray
{
    use Importable;

    protected $availability;
    protected $import_id;

    public function __construct($availability, $import_id)
    {
        $this->availability = $availability;
        $this->import_id = $import_id;
    }

    public function array($rows)
    {
        $this->processPrevious($rows);

        $import = Imports::find($this->import_id);
        $import->items_processed = count($rows);
        $import->save();

        $last = array_pop($rows);

        foreach ($rows as $row) {
            $job = new IvecoProductPurchasePricesImportJob($row[0], $row[1], $this->availability, $this->import_id);
            dispatch($job)->onQueue('buxus_iveco_purchase_prices_import_' . env('DB_DATABASE'));
        }

        $job = new IvecoProductPurchasePricesImportJob($last[0], $last[1], $this->availability, $this->import_id, true);
        dispatch($job)->onQueue('buxus_iveco_purchase_prices_import_' . env('DB_DATABASE'));
    }

    protected function processPrevious($rows)
    {
        if (file_exists(storage_path('app/imports/iveco-original-prices-import/iveco-original-prices-previous.xlsx'))) {
            $importCodes = array_column($rows, '0');
            $importCodesPrevious = Excel::toArray(new IvecoPurchasePricesPrevious(), storage_path('app/imports/iveco-original-prices-import/iveco-original-prices-previous.xlsx'));
            $importCodesPrevious = array_column($importCodesPrevious[0], '0');
            $diff = array_diff($importCodesPrevious, $importCodes);

            foreach ($diff as $diffRow) {
                $job = new RemovePriceJob($diffRow, PropertyTag::ESHOP_EUR_IVECO_PRICE_WITHOUT_VAT_TAG(), $this->import_id);
                dispatch($job)->onQueue('buxus_iveco_purchase_prices_import_' . env('DB_DATABASE'));
            }
        }
    }
}
