<?php

namespace App\Imports\Processors\Sabo;

use App\Http\Livewire\Sabo\SaboImport;
use App\Imports\AbstractImportProcessor;
use App\Imports\Jobs\Sabo\SaboImportJob;
use App\Imports\Jobs\Sabo\RemoveStockJob;
use Buxus\Util\PropertyID;
use DB;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class SaboImportProcessor extends AbstractImportProcessor implements WithHeadingRow
{
    protected string $importJobClass = SaboImportJob::class;
    protected bool $shouldProcessPrevious = false;

    protected string $onQueue = 'buxus_iveco_big_db_import';
    protected $codeColumnKey = 'sabo_kod';
    protected $priceColumnKey = 'cena';
    protected $titleColumnKey = 'nazov';

    protected $additionalProperties = [
        'vyrobca',
    ];

    public function __construct(int $availability = null, int $import_id = null, bool $dispatchAtMidnight = false, $additionalProperties = [])
    {
        parent::__construct($availability, $import_id, $dispatchAtMidnight, $additionalProperties);

        $this->helper->setConfigKey(SaboImport::CONFIG_KEY);
    }

    public function headingRow(): int
    {
        return 1;
    }

    public function collection($collection)
    {
        $collection = $collection->sortBy([
            [$this->codeColumnKey, 'asc'],
            [$this->priceColumnKey, 'desc']
        ]);

        return parent::collection($collection);
    }

    /**
     * @param Collection $collection
     */
    protected function processPrevious($collection)
    {
        $currentSupplierCodes = DB::table('tblPagePropertyValues')
            ->where('property_id', PropertyID::SABO_SUPPLIER_CODE_ID())
            ->where('property_value', '<>', '')
            ->get()
            ->pluck('property_value');

        $currentSupplierCodes
            ->diff($collection)
            ->each(function ($supplierCode) {
                dispatch(new RemoveStockJob($supplierCode, $this->import_id));
            });
    }
}
