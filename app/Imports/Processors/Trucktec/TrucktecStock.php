<?php

namespace App\Imports\Processors\Trucktec;

use App\Imports\Jobs\Trucktec\TrucktecStockJob;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class TrucktecStock implements ToCollection, WithHeadingRow
{
    use Importable;

    public function headingRow()
    {
        return 1;
    }

    public function collection(Collection $collection)
    {
        $collection = $collection->filter(function ($item) {
            return !empty($item['1_ktm']);
        });

        foreach ($collection as $item) {
            TrucktecStockJob::dispatch(
                $item['1_ktm'],
                $item['2_main_warehouse'],
            );
        }

        return Command::SUCCESS;
    }
}
