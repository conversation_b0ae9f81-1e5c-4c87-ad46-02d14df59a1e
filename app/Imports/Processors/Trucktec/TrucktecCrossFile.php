<?php

namespace App\Imports\Processors\Trucktec;

use App\Imports\Jobs\Trucktec\TrucktecReferenceJob;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Illuminate\Contracts\Queue\ShouldQueue;

class TrucktecCrossFile implements ToCollection, WithHeadingRow, WithChunkReading, ShouldQueue
{
    use Importable;

    protected $timeout = 3600;
    protected bool $delayed;

    public function __construct($delayed = false)
    {
        $this->delayed = $delayed;
    }

    public function collection(Collection $collection)
    {
        $oeCodes = [];

        foreach ($collection as $item) {
            if (empty($item['trucktec_cislo'])) {
                continue;
            }

            if (empty($item['oem'])) {
                continue;
            }

            $oeCodes[$item['trucktec_cislo']][] = $item['oem'];
        }

        $oeCodes = array_filter($oeCodes);

        foreach ($oeCodes as $key => $values) {
            if ($this->delayed) {
                TrucktecReferenceJob::dispatchAtMidnight($key, $values);
            } else {
                TrucktecReferenceJob::dispatch($key, $values);
            }
        }
    }

    public function headingRow()
    {
        return 1;
    }

    public function chunkSize(): int
    {
        return 1000;
    }
}
