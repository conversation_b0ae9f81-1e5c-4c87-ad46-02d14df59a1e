<?php

namespace App\Imports\Processors\Trucktec;

use App\Http\Livewire\Trucktec\TrucktecImport;
use App\Imports\AbstractImportProcessor;
use App\Imports\Jobs\Trucktec\TrucktecImportJob;
use App\Imports\Jobs\Trucktec\RemoveStockJob;
use Buxus\Util\PropertyID;
use DB;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class TrucktecImportProcessor extends AbstractImportProcessor implements WithHeadingRow
{
    protected string $importJobClass = TrucktecImportJob::class;
    protected bool $shouldProcessPrevious = false;

    protected string $onQueue = 'buxus_iveco_big_db_import';
    protected $codeColumnKey = 'TTC  no.';
    protected $titleColumnKey = 'Description';
    protected $priceColumnKey = 'Price';

    protected $additionalProperties = [
        'Ref No',
        'Item Group',
        'Weight (gr)',
        'HS Code',
        'EAN13',
    ];

    public function __construct(int $availability = null, int $import_id = null, bool $dispatchAtMidnight = false, $additionalProperties = [])
    {
        parent::__construct($availability, $import_id, $dispatchAtMidnight, $additionalProperties);

        $this->helper->setConfigKey(TrucktecImport::CONFIG_KEY);
    }

    public function headingRow(): int
    {
        return 1;
    }

    /**
     * @param Collection $collection
     */
    protected function processPrevious($collection)
    {
        $currentSupplierCodes = DB::table('tblPagePropertyValues')
            ->where('property_id', PropertyID::TRUCKTEC_SUPPLIER_CODE_ID())
            ->where('property_value', '<>', '')
            ->get()
            ->pluck('property_value');

        $currentSupplierCodes
            ->diff($collection)
            ->each(function ($supplierCode) {
                dispatch(new RemoveStockJob($supplierCode, $this->import_id));
            });
    }
}
