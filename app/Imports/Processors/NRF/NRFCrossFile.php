<?php

namespace App\Imports\Processors\NRF;

use App\Imports\Jobs\NRF\NRFReferenceJob;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class NRFCrossFile implements ToCollection, WithHeadingRow
{
    use Importable;

    protected $reference;

    public function collection(Collection $collection)
    {
        $collection = $collection->filter(function ($row) {
            return empty($row->nrf_cislo) || empty($row->cross_cisla);
        });

        $rows = $collection->toArray();

        foreach ($rows as $row) {
            if (isset($row['nrf_cislo']) && isset($row['cross_cisla'])) {
                $this->reference[trim($row['nrf_cislo'])][] = trim($row['cross_cisla']);
            }
        }

        foreach ((array)$this->reference as $key => $values) {
            NRFReferenceJob::dispatch($key, $values);
        }
    }

    public function headingRow()
    {
        return 1;
    }
}
