<?php

namespace App\Imports\Processors\SpecialTurbo;

use App\Http\Livewire\SpecialTurbo\SpecialTurboImport;
use App\Imports\AbstractImportProcessor;
use App\Imports\Jobs\SpecialTurbo\SpecialTurboImportJob;
use App\Imports\Jobs\SpecialTurbo\RemoveStockJob;
use Buxus\Util\PropertyID;
use DB;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class SpecialTurboImportProcessor extends AbstractImportProcessor implements WithHeadingRow
{
    protected string $importJobClass = SpecialTurboImportJob::class;
    protected bool $shouldProcessPrevious = false;

    protected string $onQueue = 'buxus_iveco_big_db_import';
    protected $codeColumnKey = 'kod';
    protected $priceColumnKey = 'cenaks';
    protected $titleColumnKey = 'nazov';

    protected $additionalProperties = [
        'nazov_2',
        'nazov_3',
        'vratna_zaloha',
        'vyrobca',
        'sklad',
    ];

    public function __construct(int $availability = null, int $import_id = null, bool $dispatchAtMidnight = false, $additionalProperties = [])
    {
        parent::__construct($availability, $import_id, $dispatchAtMidnight, $additionalProperties);

        $this->helper->setConfigKey(SpecialTurboImport::CONFIG_KEY);
    }

    public function headingRow(): int
    {
        return 1;
    }

    /**
     * @param Collection $collection
     */
    protected function processPrevious($collection)
    {
        $currentSupplierCodes = DB::table('tblPagePropertyValues')
            ->where('property_id', PropertyID::SPECIAL_TURBO_SUPPLIER_CODE_ID())
            ->where('property_value', '<>', '')
            ->get()
            ->pluck('property_value');

        $currentSupplierCodes
            ->diff($collection)
            ->each(function ($supplierCode) {
                dispatch(new RemoveStockJob($supplierCode, $this->import_id));
            });
    }
}
