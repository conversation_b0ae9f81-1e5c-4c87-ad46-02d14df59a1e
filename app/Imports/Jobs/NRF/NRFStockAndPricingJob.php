<?php

namespace App\Imports\Jobs\NRF;

use App\Imports;
use App\Logger\ImportsLogger;
use Buxus\Error\ErrorReporter;
use Buxus\Page\PageInterface;
use Buxus\Util\PropertyTag;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class NRFStockAndPricingJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $supplierCode;
    protected $stockItaly;
    protected $stockCzech;
    protected $price;

    /**
     * @var \Buxus\Logger\Logger
     */
    protected $logger;

    public function __construct($supplierCode, $stockItaly, $stockCzech, $price)
    {
        $this->supplierCode = $supplierCode;
        $this->stockItaly = $stockItaly;
        $this->stockCzech = $stockCzech;
        $this->price = $price;

        $this->logger = (new ImportsLogger)->getLogger();
    }

    public function handle()
    {
        try {
            $manager = new Imports\Pairing\NRFPairingManager();
            $page = $manager->getPages($this->supplierCode);

            if (!empty($page)) {
                $this->updatePage($page);
            }
        } catch (\Exception $e) {
            ErrorReporter::reportSilent($e);
        }
    }

    protected function updatePage(PageInterface $page)
    {
        if ($this->price > 0) {
            $pageHandler = new Imports\PageHandlers\NRFPageHandler($page);
            $pageHandler->setPropertyValues([
                PropertyTag::MEC_DIESEL_STOCK_BALANCE_ITALY_TAG() => (int)$this->stockItaly,
                PropertyTag::MEC_DIESEL_STOCK_BALANCE_ITALY_TAG() => (int)$this->stockCzech,
                PropertyTag::MEC_DIESEL_PRICE_WITHOUT_VAT_TAG() => (float)$this->price,
                PropertyTag::MEC_DIESEL_LATEST_IMPORT_TAG() => Carbon::now()->toDateTimeString(),
            ]);
            $pageHandler->updatePage();
        }

        $this->logger->info("[MEC_DIESEL] Changing price and stock of page with ID: {$page->getPageId()}, price {$this->price}");
        dd($page->getPageId());
    }
}
