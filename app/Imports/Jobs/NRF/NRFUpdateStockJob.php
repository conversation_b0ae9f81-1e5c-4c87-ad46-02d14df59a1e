<?php

namespace App\Imports\Jobs\NRF;

use App\Imports\Pairing\BasePairingManager;
use App\Imports\Pairing\NRFPairingManager;
use Buxus\Page\PageInterface;
use Buxus\Util\PropertyTag;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class NRFUpdateStockJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected string $supplierCode;
    protected int $stockBalance;

    public function __construct($supplierCode, $stockBalance)
    {
        $this->supplierCode = $supplierCode;
        $this->stockBalance = $stockBalance;

        $this->onQueue('buxus_iveco_big_db_import_' . env('DB_DATABASE'));
    }

    public function handle()
    {
        $pairing = new NRFPairingManager();
        $pairing->setMethod(BasePairingManager::METHOD_GET_FIRST);

        $page = $pairing->getPages($this->supplierCode);

        if ($page instanceof PageInterface) {
            $page->setValue(PropertyTag::NRF_STOCK_BALANCE_TAG(), $this->stockBalance);
            $page->setValue(PropertyTag::NRF_LATEST_IMPORT_TAG(), Carbon::now()->toDateTimeString());
            $page->save(false);
        }
    }
}