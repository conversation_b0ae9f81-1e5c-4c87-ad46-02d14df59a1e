<?php

namespace App\Imports\Jobs\IvecoPurchasePrices;

use App\Imports;
use App\Imports\Pairing\BasePairingManager;
use App\Imports\Pairing\IvecoStockPricesPairingManager;
use App\Logger\ImportsLogger;
use App\Product\Codes\ProductCodesChangedEvent;
use Buxus\Error\ErrorReporter;
use Buxus\Page\PageInterface;
use Buxus\Util\PropertyTag;
use BuxusEvent;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Throwable;

class IvecoProductPurchasePricesImportJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $code;
    protected $price;
    protected $page;
    protected $logger;
    protected $availability;
    protected $import_id;
    protected $last;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($code, $price, $availability, $import_id, $last = false)
    {
        $this->code = $code;
        $this->price = $price;
        $this->availability = $availability;
        $this->import_id = $import_id;
        $this->last = $last;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            $this->logger = (new ImportsLogger)->getLogger();

            $manager = new IvecoStockPricesPairingManager(BasePairingManager::METHOD_GET_ALL);
            $pages = $manager->getPages($this->code);

            if ($pages instanceof PageInterface) {
                $pages = collect([$pages]);
            }

            if ($pages->isNotEmpty()) {
                foreach ($pages as $page) {
                    if (!$page instanceof PageInterface) {
                        continue;
                    }

                    if (!empty($page->getValue(PropertyTag::AUGUSTIN_GROUP_NUMBER_TAG()))) {
                        continue;
                    }

                    $this->logger->info("\n[IVECO Nákupné ceny] Updating product: {$this->code}, page ID: {$page->getPageId()}, from price: {$page->getValue(PropertyTag::ESHOP_EUR_IVECO_PRICE_WITHOUT_VAT_TAG())}, to price: {$this->price}");

                    Imports::find($this->import_id)->increment('updates_processed');

                    $page->setValue(PropertyTag::IVECO_SMALL_DB_LATEST_IMPORT_TAG(), Carbon::now());
                    $page->setValue(PropertyTag::ESHOP_EUR_IVECO_PRICE_WITHOUT_VAT_TAG(), $this->price);
                    $page->setValue(PropertyTag::AVAILABILITY_SMALL_DB_TAG(), $this->availability);
                    $page->save(false);

                    $event = new ProductCodesChangedEvent($page);
                    BuxusEvent::fire($event);
                }
            }

            if ($this->last) {
                $this->markDone();
            }
        } catch (Throwable $e) {
            $this->markError($e);
        }
    }

    protected function markDone()
    {
        $import = Imports::find($this->import_id);
        $import->status = Imports::DONE;
        $import->save();
    }

    protected function markError(Throwable $e)
    {
        ErrorReporter::reportSilent($e);
        $this->logger->error("\n[IVECO Nákupné ceny] Something went wrong while updating product: {$this->code}\n{$e->getMessage()}");
        $import = Imports::find($this->import_id);
        $import->status = Imports::ERROR;
        $import->increment('errors');
        $import->save();
    }
}
