<?php

namespace App\Imports\Jobs\Trucktec;

use App\Imports;
use App\Imports\Pairing\TrucktecPairingManager;
use App\Logger\ImportsLogger;
use Buxus\Error\ErrorReporter;
use Buxus\Util\PropertyTag;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class RemoveStockJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $supplierCode;
    protected $importId;

    /**
     * @var \Buxus\Logger\Logger
     */
    protected $logger;

    public function __construct($supplierCode, $importId)
    {
        $this->supplierCode = $supplierCode;
        $this->importId = $importId;

        $this->onQueue('buxus_iveco_big_db_import_' . env('DB_DATABASE'));
    }

    public function handle()
    {
        $this->logger = (new ImportsLogger)->getLogger();

        try {
            $manager = new TrucktecPairingManager();
            $page = $manager->getPages($this->supplierCode);

            if (!empty($page)) {
                $this->removePage($page);
            }
        } catch (\Exception $e) {
            ErrorReporter::reportSilent($e);
            Imports::find($this->importId)->increment('errors');
        }
    }

    protected function removePage($page)
    {
        $page->setValue(PropertyTag::TRUCKTEC_STOCK_BALANCE_TAG(), 0);
        $page->setValue(PropertyTag::TRUCKTEC_LATEST_IMPORT_TAG(), Carbon::now()->toDateTimeString());
        $page->save();

        $this->logger->info("[" . self::class . "]" . " Removed stock for: " . $this->supplierCode);
    }
}
