<?php

namespace App\Imports\Jobs\Trucktec;

use App\Imports;
use App\Imports\Helper\CategoryHelper;
use App\Imports\Jobs\AbstractImportJob;
use App\Imports\Pairing\TrucktecPairingManager;
use App\Logger\ImportsLogger;
use App\Product\CountryManager;
use Arr;
use Buxus\Core\Constants;
use Buxus\Error\ErrorReporter;
use Buxus\Util\PageIds;
use Buxus\Util\PropertyTag;
use Carbon\Carbon;

class TrucktecImportJob extends AbstractImportJob
{
    protected string $pairingManagerClass = TrucktecPairingManager::class;
    protected ?string $price = null;

    public function __construct($item, $import_id, $last = false, $additionalProperties = [])
    {
        parent::__construct($item, $import_id, $last, $additionalProperties);

        $this->price = $this->item['cena'] ?? null;
        $this->onQueue('buxus_iveco_big_db_import_' . env('DB_DATABASE'));
    }

    protected function getPropertyValueChangesForUpdate(): array
    {
        return $this->getPropertyValueChangesForCreate();
    }

    protected function getPropertyValueChangesForCreate(): array
    {
        $properties = [];

        return array_merge($properties, [
            PropertyTag::TITLE_TAG() => $this->getTitle(),
            PropertyTag::ESHOP_ROLLER_PRODUCER_TAG() => $this->getProducerId(),
            PropertyTag::SUPPLIER_TAG() => PageIds::getTrucktecSupplier(),
            PropertyTag::TRUCKTEC_PRICE_WITHOUT_VAT_TAG() => $this->getPrice(),
            PropertyTag::TRUCKTEC_SUPPLIER_CODE_TAG() => $this->supplierCode,
            PropertyTag::TRUCKTEC_OE_NUMBER_TAG() => $this->getMainOeNumber(),
            PropertyTag::SALE_DENIED_COUNTRIES_TAG() => $this->getNotForSaleIn(),
            PropertyTag::TRUCKTEC_OE_NUMBERS_TAG() => implode(',', $this->getOeNumbers()),
            PropertyTag::TRUCKTEC_LATEST_IMPORT_TAG() => Carbon::now()->toDateTimeString(),
        ]);
    }

    protected function getPrice()
    {
        return $this->price;
    }

    protected function getProducerId()
    {
        $producer = $this->item['vyrobca'] ?? null;

        if (empty($producer)) {
            return null;
        }

        $categoryHelper = new CategoryHelper();
        return $categoryHelper->getProducerIdByName($producer);
    }

    protected function getMainOeNumber()
    {
        $oeNumbers = $this->getOeNumbers();

        if (empty($oeNumbers)) {
            return null;
        }

        return $oeNumbers[0];
    }

    protected function getOeNumbers()
    {
        $oeNumbers = [];

        for ($i = 1; $i <= 4; $i++) {
            $oeNumber = $this->item['nr_oe_' . $i] ?? null;
            if (!empty($oeNumber)) {
                $oeNumbers[] = $oeNumber;
            }
        }

        $crossNo = $this->item['cross_no'] ?? null;
        if (!empty($crossNo)) {
            $oeNumbers[] = $crossNo;
        }

        return array_unique(array_filter($oeNumbers));
    }

    protected function getNotForSaleIn()
    {
        return CountryManager::getNotForSaleInCountriesForSupplier(PageIds::getTrucktecSupplier());
    }

    protected function getAdditionalProperties(): array
    {
        $additionalProperties = [];

        if (!empty($this->item['length_cm'])) {
            $additionalProperties[PropertyTag::LENGTH_CM_TAG()] = $this->item['length_cm'];
        }

        if (!empty($this->item['width_cm'])) {
            $additionalProperties[PropertyTag::WIDTH_CM_TAG()] = $this->item['width_cm'];
        }

        if (!empty($this->item['height_cm'])) {
            $additionalProperties[PropertyTag::HEIGHT_CM_TAG()] = $this->item['height_cm'];
        }

        if (!empty($this->item['weight'])) {
            $additionalProperties[PropertyTag::WEIGHT_TAG()] = $this->item['weight'];
        }

        return $additionalProperties;
    }
}
