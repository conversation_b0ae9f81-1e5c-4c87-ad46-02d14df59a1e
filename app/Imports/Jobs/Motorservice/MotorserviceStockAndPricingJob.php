<?php

namespace App\Imports\Jobs\Motorservice;

use App\Imports\Pairing\MotorservicePairingManager;
use App\Logger\ImportsLogger;
use Buxus\Error\ErrorReporter;
use Buxus\Page\PageInterface;
use Buxus\Util\PropertyTag;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class MotorserviceStockAndPricingJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $supplierCode;
    protected $stock;
    protected $price;

    /**
     * @var \Buxus\Logger\Logger
     */
    protected $logger;

    public function __construct($supplierCode, $stock, $price = null)
    {
        $this->supplierCode = $supplierCode;
        $this->stock = $stock;
        $this->price = $price;

        $this->onQueue('buxus_iveco_big_db_import_' . env('DB_DATABASE'));
    }

    public function handle()
    {
        $this->logger = (new ImportsLogger)->getLogger();

        try {
            $manager = new MotorservicePairingManager();
            $page = $manager->getPages($this->supplierCode);

            if (!empty($page)) {
                $this->updatePage($page);
            }
        } catch (\Exception $e) {
            ErrorReporter::reportSilent($e);
        }
    }

    protected function updatePage(PageInterface $page)
    {
        if ($this->price != null) {
            $page->setValue(PropertyTag::MOTORSERVICE_PRICE_WITHOUT_VAT_TAG(), (float)$this->price);
            $page->setValue(PropertyTag::MOTORSERVICE_LATEST_IMPORT_TAG(), Carbon::now()->toDateTimeString());
        }

        $page->setValue(PropertyTag::MOTORSERVICE_STOCK_BALANCE_TAG(), (int)$this->stock);
        $page->save(false);

        $this->logger->info("[MOTORSERVICE] Changing price and stock of page with ID: {$page->getPageId()}, code {$this->supplierCode}, price {$this->price}, stock {$this->stock}");
    }
}
