<?php

namespace App\Imports\Jobs\Eminia;

use App\Imports;
use App\Imports\Pairing\EminiaPairingManager;
use App\Logger\ImportsLogger;
use Buxus\Core\Constants;
use Buxus\Error\ErrorReporter;
use Buxus\Logger\Logger;
use Buxus\Page\PageInterface;
use Buxus\Util\PropertyTag;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class RemoveStockJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * @var Logger
     */
    protected $logger;
    protected string $code;
    protected ?int $import_id;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($code, $import_id = null)
    {
        $this->onQueue('buxus_iveco_big_db_import_' . env('DB_DATABASE'));

        $this->code = $code;
        $this->import_id = $import_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->logger = (new ImportsLogger)->getLogger();

        try {
            $pairing = new EminiaPairingManager();
            $pairing->setMethod(Imports\Pairing\BasePairingManager::METHOD_GET_FIRST);
            $page = $pairing->getPages($this->code);

            if ($page instanceof PageInterface) {
                $page->setPageStateId(Constants::C_passive_page_state_id);
                $page->setValue(PropertyTag::EMINIA_STOCK_BALANCE_TAG(), null);
                $page->setValue(PropertyTag::EMINIA_LATEST_IMPORT_TAG(), Carbon::now()->toDateTimeString());

                $mutex = new \malkusch\lock\mutex\FlockMutex(fopen(__FILE__, 'rb'));
                $mutex->synchronized(function () use ($page) {
                    $page->save(false);
                });

                $this->logger->info("\n[Eminia] Removing product stock: {$this->code}, page ID: {$page->getPageId()}");

                if (!empty($this->import_id)) {
                    Imports::find($this->import_id)->increment('deletes_processed');
                }
            }
        } catch (\Throwable $e) {
            ErrorReporter::reportSilent($e);
            if (!empty($this->import_id)) {
                Imports::find($this->import_id)->increment('errors');
            }
        }
    }
}
