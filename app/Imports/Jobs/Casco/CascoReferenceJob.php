<?php

namespace App\Imports\Jobs\Casco;

use App\Imports\Jobs\AbstractReferenceJob;
use App\Imports\Pairing\BasePairingManager;
use App\Imports\Pairing\CascoPairingManager;
use App\Logger\ImportsLogger;
use Buxus\Error\ErrorReporter;
use Buxus\Page\PageInterface;
use Buxus\Util\PageIds;
use Buxus\Util\PropertyTag;
use Cache;

class CascoReferenceJob extends AbstractReferenceJob
{
    protected $pairingManagerClass = CascoPairingManager::class;
    protected $oeNumbersPropertyTag = PropertyTag::CASCO_OE_NUMBERS_TAG;

    public function handle()
    {
        $this->logger = (new ImportsLogger)->getLogger();
        $this->logger->info("[" . self::class . "]" . " Referencing: " . $this->supplierCode . " - " . implode(',', $this->oeCodes));

        $pairing = new $this->pairingManagerClass(BasePairingManager::METHOD_GET_ALL);

        try {
            $pages = $pairing->getPages($this->supplierCode);

            foreach ($pages as $page) {
                $this->logger->info("[" . self::class . "]" . " Paired to page: " . $this->supplierCode . " - " . $page->getPageId());

                Cache::lock('page_reference_job_' . $page->getPageId(), 30)->get(function () use ($page) {
                    $oeNumbers = explode(',', $page->getValue($this->oeNumbersPropertyTag));
                    $oeNumbers = array_merge($oeNumbers, $this->oeCodes);
                    $oeNumbers = array_unique($oeNumbers);
                    $oeNumbers = array_filter($oeNumbers, function ($oeNumber) {
                        return !empty($oeNumber);
                    });

                    $page->setValue(PropertyTag::TITLE_TAG(), $this->getProductTitle($page, $oeNumbers[array_key_first($oeNumbers)]));

                    $page->setValue($this->oeNumbersPropertyTag, implode(',', $oeNumbers));
                    $page->setValue(PropertyTag::CASCO_OE_NUMBER_TAG(), $oeNumbers[array_key_first($oeNumbers)]);
                    $page->save();
                });
            }
        } catch (\Throwable $e) {
            ErrorReporter::reportSilent($e);
        }
    }

    protected function getProductTitle(PageInterface $page, $oeCode)
    {
        $title = $page->getValue(PropertyTag::TITLE_TAG());

        if ($page->getValue(PropertyTag::ESHOP_ROLLER_PRODUCER_TAG()) == PageIds::getCascoProducer()) {
            return trim($title);
        }

        if(!empty($page->getValue(PropertyTag::ONIX_CODES_TAG()))){
            return trim($title);
        }

        $supplierCode = $page->getValue(PropertyTag::CASCO_SUPPLIER_CODE_TAG());

        if (!str_contains($title, $supplierCode)) {
            return $title;
        }

        $title = str_replace($supplierCode, '', $title);

        if (str_contains($title, $oeCode)) {
            return trim($title);
        }

        return $oeCode . ' ' . trim($title);
    }
}