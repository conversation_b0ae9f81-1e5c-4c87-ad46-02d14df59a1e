<?php

namespace App\Imports\Helper;

use Buxus\Util\PageIds;
use Buxus\Util\PropertyTag;
use PageTypesConstantsIDs;

class CategoryHelper
{
    public function getAllParentPageIds($categories): ?array
    {
        $parentPageIds = [];

        $parentPageIds[] = $pageId = $this->getCategoryPageId($categories['kat1']);
        if ($categories['kat2']) {
            $parentPageIds[] = $pageId = $this->getCategoryPageId($categories['kat2'], $pageId);
        }
        if ($categories['kat3']) {
            $parentPageIds[] = $this->getCategoryPageId($categories['kat3'], $pageId);
        }

        return $parentPageIds;
    }

    public function getSupposedParentPageId($categories)
    {
        $pageId = $this->getCategoryPageId($categories['kat1']);
        if ($categories['kat2']) {
            $pageId = $this->getCategoryPageId($categories['kat2'], $pageId);
        }
        if ($categories['kat3']) {
            $pageId = $this->getCategoryPageId($categories['kat3'], $pageId);
        }

        return $pageId;
    }

    public function getCategoryPageId($title, $parentPageId = null)
    {
        if (empty($title)) {
            return PageIds::getNezaradene();
        }

        $builder = \PageFactory::builder();

        $title = trim($title);

        $isParentCategory = false;

        if ($parentPageId == null) {
            $parentPageId = PageIds::getEshopCatalog();
            $isParentCategory = true;
        }

        $page = $builder->wherePropertyValue(PropertyTag::TITLE_TAG(), $title)
            ->whereParent($parentPageId)
            ->first();

        if (empty($page)) {
            $mutex = new \malkusch\lock\mutex\FlockMutex(fopen(__FILE__, 'rb'));
            $page = $mutex->synchronized(function () use ($builder, $title, $parentPageId, $isParentCategory) {
                $page = $builder->wherePropertyValue(PropertyTag::TITLE_TAG(), $title)
                    ->whereParent($parentPageId)
                    ->first();
                if (empty($page)) {
                    $page = \PageFactory::create($parentPageId, $isParentCategory
                        ? PageTypesConstantsIDs::ESHOP_CATEGORY_ID()
                        : PageTypesConstantsIDs::ESHOP_SUBCATEGORY_ID());

                    $page->setPageName($title);
                    $page->setValue(PropertyTag::TITLE_TAG(), $title);
                    $page->save();
                }

                return $page;
            });
        }

        return $page->getPageId();
    }
}