<?php

namespace App\Imports\Pairing;

use Buxus\Util\PageIds;

class SupplierPairingStrategyManager
{
    public static function getPairingManagerForSupplierId($supplierId): ?string
    {
        $supplierIdToManagerMapping = self::getPairingManagersMapping();

        return $supplierIdToManagerMapping[$supplierId] ?? null;
    }

    public static function getPairingManagersMapping(): array
    {
        return [
            PageIds::getAbakusSupplier() => AbakusPairingManager::class,
            PageIds::getAugustinGroupSupplier() => AugustinGroupPairingManager::class,
            PageIds::getCascoSupplier() => CascoWeightPairingManager::class,
            PageIds::getCeiSupplier() => CeiPairingManager::class,
            PageIds::getCleanSupplier() => AlternativePricesPairingManager::class,
            PageIds::getCovindSupplier() => AlternativePricesPairingManager::class,
            PageIds::getEminiaSupplier() => EminiaPairingManager::class,
            PageIds::getErreviSupplier() => AlternativePricesPairingManager::class,
            PageIds::getFebiBilsteinSupplier() => FebiBilsteinPairingManager::class,
            PageIds::getLemaSupplier() => AlternativePricesPairingManager::class,
            PageIds::getMartexSupplier() => MartexPairingManager::class,
            PageIds::getMecDieselSupplier() => MecDieselPairingManager::class,
            PageIds::getMotorserviceSupplier() => MotorservicePairingManager::class,
            PageIds::getNrfSupplier() => NRFPairingManager::class,
            PageIds::getOeGermanySupplier() => OeGermanyPairingManager::class,
            PageIds::getRemanteSupplier() => RemantePairingManager::class,
            PageIds::getSaboSupplier() => SaboPairingManager::class,
            PageIds::getSpecialTurboSupplier() => SpecialTurboPairingManager::class,
            PageIds::getTurbaSupplier() => AlternativePricesPairingManager::class,
            PageIds::getVignalSupplier() => AlternativePricesPairingManager::class,
            PageIds::getTrucktecSupplier() => TrucktecPairingManager::class,
        ];
    }

}
