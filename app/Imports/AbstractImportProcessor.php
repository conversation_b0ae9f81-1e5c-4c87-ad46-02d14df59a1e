<?php

namespace App\Imports;

use App\Imports;
use App\Imports\Helper\GeneralImportHelper;
use App\Imports\Processors\DefaultPreviousProcessor;
use App\Jobs\Traits\DispatchableAtMidnight;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

abstract class AbstractImportProcessor implements ToCollection, WithHeadingRow
{
    use Importable, DispatchableAtMidnight;

    protected ?int $availability;
    protected ?int $import_id;

    protected bool $shouldProcessPrevious = true;

    protected string $path;
    protected string $pathPrevious;
    protected string $removePriceJobClass = \App\Imports\Jobs\RemovePriceJob::class;

    protected string $importJobClass;
    protected string $onQueue;

    protected $codeColumnKey = 0;
    protected $priceColumnKey = null;

    protected $quantityColumnKey = null;
    protected $titleColumnKey = null;

    protected bool $dispatchAtMidnight = false;

    protected $additionalProperties = [];

    public function __construct(int $availability = null, int $import_id = null, bool $dispatchAtMidnight = false)
    {
        $this->availability = $availability;
        $this->import_id = $import_id;

        $this->helper = new GeneralImportHelper();

        $this->path = $this->helper->getStoragePath();
        $this->pathPrevious = $this->helper->getStoragePathPrevious();

        $this->dispatchAtMidnight = $dispatchAtMidnight;

        ini_set('memory_limit', '-1');
    }

    public function collection($collection)
    {
        $collection = $collection->filter(function ($item) {
            return isset($item[$this->codeColumnKey]) && !empty($item[$this->codeColumnKey]);
        });

        if ($this->shouldProcessPrevious) {
            $this->processPrevious($collection);
        }

        if ($this->import_id) {
            $import = Imports::find($this->import_id);
            $import->items_processed = $collection->count();
            $import->save();
        }

        $last = $collection->pop();

        foreach ($collection as $item) {
            $this->processJobForItem($item);
        }

        $this->processJobForItem($last, true);
    }

    protected function processJobForItem($item, $last = false)
    {
        if (is_null($item)) {
            return;
        }

        $itemAdditionalProperties = [];

        foreach ($item as $key => $value) {
            if (in_array($key, $this->additionalProperties)) {
                $itemAdditionalProperties[$key] = $value;
            }
        }

        $job = new $this->importJobClass(
            $item[$this->codeColumnKey],
            $this->import_id,
            $itemAdditionalProperties,
            $last
        );

        if ($this->priceColumnKey) {
            $job->setPrice($item[$this->priceColumnKey]);
        }

        if ($this->quantityColumnKey) {
            $job->setStockBalance($item[$this->quantityColumnKey]);
        }

        if ($this->titleColumnKey) {
            $job->setTitle($item[$this->titleColumnKey]);
        }

        if ($this->dispatchAtMidnight) {
            self::dispatchAtMidnight($job)->onQueue($this->onQueue . '_' . env('DB_DATABASE'));
        } else {
            dispatch($job)->onQueue($this->onQueue . '_' . env('DB_DATABASE'));
        }
    }

    protected function processPrevious($collection)
    {
        if (!\Storage::disk('local')->exists($this->pathPrevious)) {
            return;
        }

        \Excel::toCollection(new DefaultPreviousProcessor(), $this->pathPrevious)
            ->first()
            ->pluck($this->codeColumnKey)
            ->filter()
            ->diff($collection->pluck($this->codeColumnKey))
            ->each(fn($diffRow) => call_user_func($this->removePriceJobClass . '::dispatch', $diffRow, $this->import_id));
    }

    public function headingRow(): int
    {
        return 1;
    }
}
