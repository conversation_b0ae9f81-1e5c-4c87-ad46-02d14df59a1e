<?php

namespace App\Mailinglist\Form;

use Authentication\Form\MailinglistForm;
use Buxus\Captcha\Form\Element\CaptchaFormElement;
use Buxus\Captcha\Form\Element\HoneypotFormElement;
use FormBase\Element\CheckboxElement;
use FormBase\Element\HiddenElement;
use FormBase\Element\SubmitElement;

class RinopartsMailinglistForm extends MailinglistForm
{
    public function init()
    {
        $news = new CheckboxElement('news');
        $news->setAttrib('class', 'check');
        $news->setLabel(\Trans::raw(lang('form', 'Chcem odoberať newsletter')));
        $user = \WebUserAuthentication::getUser();
        $news->setValue(($user->getMailing(config('buxus_authentication.user_mailinglist_tag', 'newsletter')) ? 1 : 0));
        $news->setValueClass('checkbox col-sm-9 col-xs-12');
        $this->addElement($news);

        // Fake field for antispam protection
        $honeypotElement = new HoneypotFormElement();
        $this->addElement($honeypotElement);

        $captchaElement = new CaptchaFormElement();
        $this->addElement($captchaElement);

        // Csrf token
        $csrf = new HiddenElement('_token');
        $csrf->setValue(csrf_token());
        $this->addElement($csrf);

        // Submit
        $submit = new SubmitElement($this->getFormTag());
        $submit->setLabel(\Trans::raw(lang('form', 'Uložiť')));
        $submit->setValueClass('col-sm-9 col-xs-12');
        $this->addElement($submit);
    }

}