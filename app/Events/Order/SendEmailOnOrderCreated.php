<?php

namespace App\Events\Order;

use Buxus\Eshop\Event\OrderCreatedEvent;
use Buxus\Util\PageIds;
use Buxus\Util\PropertyTag;
use Buxus\Util\Url;
use Exception;
use WebUserFactory;

class SendEmailOnOrderCreated
{
    /**
     * Handler for sending emails when user that created order has empty ONIX ID.
     * @param OrderCreatedEvent $event
     * @throws Exception
     */
    public function handle(OrderCreatedEvent $event)
    {
        $order = $event->getOrder();
        $userId = $order->getUserId();

        $user = WebUserFactory::getById($userId);

        $mailPageId = PageIds::getEmptyOnixIdEmail();
        $mailPage = \PageFactory::get($mailPageId);

        if (empty($mailPage) || !empty($user->getOnixPartnerId())) {
            return;
        }

        $mail = \Email::get($mailPageId);

        $mail->setDataTag('VARIABLE_SYMBOL', $order->getVariableSymbol());
        $mail->setDataTag('USER_ID', $userId);
        $mail->setDataTag('COMPANY_NAME', $user->getCompanyName());
        $mail->setDataTag('LINK', Url::staticUrl('/buxus/lib/authenticate/uif/web_user_details.php?web_user_id=' . $userId));

        $mail->send();
    }
}
