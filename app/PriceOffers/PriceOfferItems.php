<?php

namespace App\PriceOffers;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PriceOfferItems extends Model
{
    protected $table = 'tblPriceOfferItems';

    protected $fillable = [
        'price_offer_id',
        'page_id',
        'amount'
    ];

    public function priceOffer()
    {
        return $this->belongsTo(PriceOffer::class, 'price_offer_id', 'id');
    }

    public function getProduct()
    {
        $product = \ProductFactory::get($this->page_id);
        $product->setAmount($this->amount);

        return $product;
    }
}
