<?php

namespace App\Authentication\Form;

use Buxus\Captcha\Form\Element\CaptchaFormElement;
use Buxus\Captcha\Form\Element\HoneypotFormElement;
use Buxus\WebUser\WebUser;
use BuxusSite;
use FormBase\BaseForm;
use FormBase\Element\RadioElement;
use FormBase\Element\SelectElement;
use FormBase\Element\SubmitElement;
use FormBase\Element\TextElement;

class EditProfileForm extends \Authentication\Form\EditProfileForm
{
    public function init()
    {
        // BaseEmail
        $email = new TextElement('email');
        $email->setLabel(\Trans::raw(lang('user', 'Email')));
        $email->setAttrib('disabled', 'disabled');
        $email->setAttrib('autocapitalize', 'none');
        $email->setAttrib('autocorrect', 'off');

        $this->addElement($email);

        // Degree
        $degree = new TextElement('title');
        $degree->setLabel(\Trans::raw(lang('user', 'Titul')));
        $degree->setValueClass('col-sm-2 col-xs-9');
        $degree->addLabelClass('col-xs-12');
        $degree->setAttrib('readonly', 'readonly');
        $this->addElement($degree);

        // First name
        $first_name = new TextElement('first_name');
        $first_name->setLabel(\Trans::raw(lang('user', 'Meno')));
        $first_name->setRequired(false);
        $first_name->setValueClass('col-sm-6 col-xs-9');
        $first_name->addLabelClass('col-xs-12');
        $first_name->setAttrib('readonly', 'readonly');
        $this->addElement($first_name);

        // Surname
        $surname = new TextElement('surname');
        $surname->setLabel(\Trans::raw(lang('user', 'Priezvisko')));
        $surname->setRequired(false);
        $surname->setValueClass('col-sm-6 col-xs-9');
        $surname->addLabelClass('col-xs-12');
        $surname->setAttrib('readonly', 'readonly');
        $this->addElement($surname);

        // Company name
        $company_name = new TextElement('company_name');
        $company_name->setLabel(\Trans::raw(lang('user', 'Názov firmy')));
        $company_name->setRequired(false);
        $company_name->setValueClass('col-sm-6 col-xs-9');
        $company_name->addLabelClass('col-xs-12');
        $company_name->setAttrib('readonly', 'readonly');
        $this->addElement($company_name);

        // IČO
        $ico = new TextElement('ico');
        $ico->setLabel(\Trans::raw(lang('user', 'IČO')));
        $ico->setRequired(false);
        $ico->addValidator(new \Zend_Validate_Digits());
        $ico->setValueClass('col-sm-4 col-xs-9');
        $ico->addLabelClass('col-xs-12');
        $ico->setAttrib('readonly', 'readonly');
        $this->addElement($ico);

        // DIČ
        $dic = new TextElement('dic');
        $dic->setLabel(\Trans::raw(lang('user', 'DIČ')));
        $dic->setRequired(false);
        $dic->addValidator(new \Zend_Validate_Digits());
        $dic->setValueClass('col-sm-4 col-xs-9');
        $dic->addLabelClass('col-xs-12');
        $dic->setAttrib('readonly', 'readonly');
        $this->addElement($dic);

        // IČ DPH
        $drc = new TextElement('drc');
        $drc->setLabel(\Trans::raw(lang('user', 'IČ DPH')));
        $drc->setRequired(false);
        $drc->setValueClass('col-sm-4 col-xs-9');
        $drc->addLabelClass('col-xs-12');
        $drc->setAttrib('readonly', 'readonly');
        $this->addElement($drc);

        // Phone
        $phone = new TextElement('phone');
        $phone->setLabel(\Trans::raw(lang('user', 'Telefón')));
        $phone->addValidator(new \Zend_Validate_Regex('/^[\+]?[0-9 ]+$/'));
        $phone->setValueClass('col-sm-4 col-xs-9');
        $phone->addLabelClass('col-xs-12');
        $phone->setAttrib('readonly', 'readonly');
        $this->addElement($phone);

        // Street
        $street = new TextElement('street');
        $street->setLabel(\Trans::raw(lang('user', 'Ulica a číslo')));
        $street->setRequired(false);
        $street->setValueClass('col-sm-4 col-xs-9');
        $street->addLabelClass('col-xs-12');
        $street->setAttrib('readonly', 'readonly');
        $this->addElement($street);

        // City
        $city = new TextElement('city');
        $city->setLabel(\Trans::raw(lang('user', 'Mesto')));
        $city->setRequired(false);
        $city->setValueClass('col-sm-4 col-xs-9');
        $city->addLabelClass('col-xs-12');
        $city->setAttrib('readonly', 'readonly');
        $this->addElement($city);

        // ZIP
        $zip = new TextElement('zip');
        $zip->setLabel(\Trans::raw(lang('user', 'PSČ')));
        $zip->setRequired(false);
        $zip->setValueClass('col-sm-2 col-xs-9');
        $zip->addLabelClass('col-xs-12');
        $zip->setAttrib('readonly', 'readonly');
        $this->addElement($zip);

        if (BuxusSite::site() == 'en') {
            $country = new TextElement('country');
            $country->setLabel(\Trans::raw(lang('user', 'Krajina')));
            $country->setRequired(false);
            $country->setValueClass('col-sm-4 col-xs-9');
            $country->addLabelClass('col-xs-12');
            $country->setAttrib('readonly', 'readonly');
            $this->addElement($country);
        }

        // Fake field for antispam protection
        $honeypotElement = new HoneypotFormElement();
        $this->addElement($honeypotElement);

        $captchaElement = new CaptchaFormElement();
        $this->addElement($captchaElement);

        // Set translation
        $this->setTranslation();

        $person_elements = array(
            'first_name',
            'surname',
            'title'
        );
        foreach ($person_elements as $person_element_name) {
            $person_element = $this->getElement($person_element_name);
            $person_element->addWrapperClass('person-fields');
        }

        // Mark body corporate elements
        $body_corporate_elements = array(
            'company_name',
            'ico',
            'dic',
        );
        foreach ($body_corporate_elements as $body_corporate_element_name) {
            $body_corporate_element = $this->getElement($body_corporate_element_name);
            if (!empty($body_corporate_element)) {
                $body_corporate_element->addWrapperClass('corporate-fields');
            }
        }

        // populate current values
        $user = \WebUserAuthentication::getUser();
        $this->populate($user->getAllData());
    }

    /**
     * Validate the form
     *
     * @param array $data
     * @return boolean
     */
    public function isValid($data)
    {
        $data['e_mail'] = \WebUserAuthentication::getUser()->getEmail();

        // Set required fields
        $person_elements = array(
            'first_name',
            'surname'
        );
        $body_corporate_elements = array(
            'company_name',
            'ico',
            'dic',
        );

        if ((isset($data['customer_type'])) && ($data['customer_type'] == WebUser::USER_TYPE_PERSON)) { // The person is selected
            $person_required = true;
            $body_corporate_required = false;
        } else { // The body corporate is selected
            $person_required = false;
            $body_corporate_required = true;
        }
        foreach ($person_elements as $element_name) {
            $element = $this->getElement($element_name);
            if (!empty($element)) {
                $element->setRequired($person_required);
            }
        }
        foreach ($body_corporate_elements as $element_name) {
            $element = $this->getElement($element_name);
            if (!empty($element)) {
                $element->setRequired($body_corporate_required);
            }
        }

        return BaseForm::isValid($data);
    }
}
