<?php

namespace App\Authentication\Form;

use Buxus\Error\ErrorReporter;
use Buxus\Util\PageIds;
use Buxus\Util\Url;
use BuxusSite;
use Exception;
use FormBase\AbstractForm;
use FormBase\Element\HiddenElement;
use FormBase\Element\SelectElement;
use FormBase\Element\SubmitElement;
use FormBase\Element\TextAreaElement;
use FormBase\Element\TextElement;
use Illuminate\Http\Request;
use Trans;

class RegistrationCountryForm extends AbstractForm
{
    public function init()
    {
        parent::init();

        $countries = array_map(function ($n) {
            return \Trans::str('eshop', $n);
        }, config('buxus_authentication.entry_countries'));

        $country = new SelectElement('country');
        $country->setMultiOptions($countries);
        $country->setValue(BuxusSite::site());
        $country->setLabel(Trans::str('form', 'Krajina'));
        $country->setRequired(true);
        $this->addElement($country);

        $submit = new SubmitElement('submit');
        $submit->setLabel(Trans::str('eshop', 'Vybrať'));
        $this->addElement($submit);

        $isSubmit = new HiddenElement('is_submit');
        $isSubmit->setValue(1);
        $this->addElement($isSubmit);
    }

    public function actionHandler(Request $request)
    {
        if ($request->input('is_submit') === null) {
            return;
        }

        Url::redirectPermanent(Url::page(PageIds::getAuthRegistration(), '', null, $request->input('country')));
    }
}
