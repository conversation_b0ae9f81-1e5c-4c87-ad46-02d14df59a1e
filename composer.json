{"name": "buxus/buxus", "type": "project", "description": "BUXUS 7 CMS", "keywords": ["framework", "BUXUS", "CMS"], "license": "proprietary", "minimum-stability": "dev", "prefer-stable": true, "require": {"php": "^7.4", "buxus-eshop/delivery-oraculum": "^1.1", "buxus-eshop/free-delivery": "^2.1", "buxus-eshop/related-products": "^1.3", "buxus-libs/beanstalkd-monitor": "^1.0", "buxus-libs/google-sitemap": "^2.0", "buxus-libs/persistence": "^1.2", "buxus/bx-blade-components": "1.0.x-dev", "buxus/bx-provisioning": "^1.0", "buxus/bx-widgets": "^1.0", "buxus/core": "^7.4@dev", "buxus/livewire": "1.0.x-dev", "drupol/phpermutations": "^1.4", "fideloper/proxy": "^4.0", "krabica-theme/basic": "^2.5", "krabica/krabica-core": "^7.0.0", "laravel/framework": "^8.0", "laravel/tinker": "^2.6.1", "league/oauth2-client": "^2.7", "maatwebsite/excel": "^3.1", "pda/pheanstalk": "^4.0.0", "spatie/laravel-ray": "^1.35"}, "require-dev": {"beyondcode/laravel-dump-server": "^1.3.0", "buxus/update-log-plugin": "^1.0", "fzaninotto/faker": "^1.4", "mockery/mockery": "^1.0", "nunomaduro/collision": "^5.4", "phpunit/phpunit": "^9.5"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"buxus/buxus-registrator-plugin": true, "buxus/update-log-plugin": true}}, "extra": {"branch-alias": {"dev-master": "7.3.x-dev"}, "laravel": {"dont-discover": ["buxus-libs/fs"]}}, "autoload": {"psr-4": {"App\\": ["app/"]}, "classmap": ["database/seeds", "database/factories"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "repositories": [{"type": "composer", "url": "https://packagist.ui42.sk"}], "scripts": {"post-update-cmd": [], "post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php artisan buxus:post-install"]}}