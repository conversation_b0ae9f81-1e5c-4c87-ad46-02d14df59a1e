<?php

use App\Http\Controllers\AbakusToolController;
use App\Http\Controllers\TrucktecToolController;
use App\Http\Controllers\CascoToolController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\CeiToolController;
use App\Http\Controllers\EminiaToolController;
use App\Http\Controllers\FebiBilsteinToolController;
use App\Http\Controllers\FormController;
use App\Http\Controllers\ImportToolControllers\AugustinGroupToolController;
use App\Http\Controllers\ImportToolControllers\ImportsToolController;
use App\Http\Controllers\ImportToolControllers\InvoiceToolController;
use App\Http\Controllers\ImportToolControllers\MarginToolController;
use App\Http\Controllers\ImportToolControllers\NRFToolController;
use App\Http\Controllers\MartexToolController;
use App\Http\Controllers\MeatDoriaToolController;
use App\Http\Controllers\MecDieselToolController;
use App\Http\Controllers\MsToolController;
use App\Http\Controllers\OeGermanyToolController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\ProductListController;
use App\Http\Controllers\RemanteToolController;
use App\Http\Controllers\SaboToolController;
use App\Http\Controllers\SpecialTurboToolController;
use App\Http\Controllers\StatisticsController;
use App\Http\Controllers\WeightToolController;
use App\User\Permissions\StatisticsBuxusUserPermission;
use Spatie\Honeypot\ProtectAgainstSpam;

Route::options('/csrf', function () {
});

Route::post('/fake-login', [\App\Http\Controllers\AuthenticationController::class, 'fakeLogin'])->name('fakelogin');

Route::get('/download-invoice/{enclosureRecordId}', [\App\Http\Controllers\UserController::class, 'downloadInvoice'])->name('download.invoice');
Route::get('/download-complaint-form/{complaintFormId}', [\App\Http\Controllers\UserController::class, 'downloadComplaintForm'])->name('download.complaint-form');
Route::get('/download-returns-form/{returnsFormId}', [\App\Http\Controllers\UserController::class, 'downloadReturnsForm'])->name('download.returns-form');

Route::post('/validate-user-login', [\App\Http\Controllers\AuthenticationController::class, 'validateUserLogin'])->name('user.validate-login');

Route::post('/delivery-address-change/{deliveryAddress}', [\App\Http\Controllers\UserController::class, 'changeDeliveryAddress'])->name('delivery-address.change');
Route::post('/delivery-address-create', [\App\Http\Controllers\UserController::class, 'createDeliveryAddress'])->name('delivery-address.create');
Route::get('/delivery-address-delete/{deliveryAddress}', [\App\Http\Controllers\UserController::class, 'deleteDeliveryAddress'])->name('delivery-address.delete');
Route::post('/notification-mark-as-read', [\App\Http\Controllers\UserController::class, 'notificationMarkAsRead'])->name('notification.mark-as-read');

Route::group(['prefix' => 'cart'], function () {
    Route::get('/recount', [\App\Http\Controllers\UserController::class, 'cartRecount'])->name('cart.recount');
    Route::get('/remove', [\App\Http\Controllers\CartController::class, 'cartRemove'])->name('cart.remove');
    Route::post('/save', [\App\Http\Controllers\CartController::class, 'cartSave'])->name('cart.save');
    Route::get('/restore/{priceOffer}', [\App\Http\Controllers\CartController::class, 'cartRestore'])->name('cart.restore');
    Route::get('/download/{format}', [\App\Http\Controllers\CartController::class, 'cartDownload'])->name('cart.download');
    Route::get('/show/{format}', [\App\Http\Controllers\CartController::class, 'cartShow'])->name('cart.show');
});
Route::group(['prefix' => 'price-offer'], function () {
    Route::get('remove/{priceOffer}', [\App\Http\Controllers\CartController::class, 'removePriceOffer'])->name('price-offer.remove');
});

Route::get('/login-to-site', [\App\Http\Controllers\UserController::class, 'loginToSite'])->name('site.login');

Route::get('/categories', [CategoryController::class, 'getMainCategories'])->name('categories.get');
Route::get('/subcategories/{categoryId?}', [CategoryController::class, 'getSubcategories'])->name('subcategories.get');

Route::post('/product/create', [ProductController::class, 'create'])->name('product.create');
Route::post('/demand/create', [ProductController::class, 'demand'])->name('demand.create')->middleware(ProtectAgainstSpam::class);

Route::get('/form/file/{fileId}', [FormController::class, 'file'])->name('form.file');

Route::group([
    'prefix' => 'buxus/tool',
    'middleware' => ['buxus.auth', 'buxus_admin']
], static function () {
    Route::group(['prefix' => 'margin-tool'], static function () {
        Route::get('/show', [MarginToolController::class, 'show'])->name('margin-tool.show');
        Route::get('/producer/{producer}', [MarginToolController::class, 'showProducer'])->name('margin-tool-producer.show');
    });
    Route::group(['prefix' => 'invoice-tool'], static function () {
        Route::get('/show', [InvoiceToolController::class, 'show']);
    });
    Route::group(['prefix' => 'margin-tool-small-db'], static function () {
        Route::get('/show', [MarginToolController::class, 'showSmallDb']);
    });
    Route::group(['prefix' => 'margin-tool-big-db'], static function () {
        Route::get('/show', [MarginToolController::class, 'showBigDb']);
    });
    Route::group(['prefix' => 'margin-tool-customers'], static function () {
        Route::get('/show', [MarginToolController::class, 'showCustomers']);
        Route::get('/edit/{customer}', [MarginToolController::class, 'editCustomer'])->name('margin-tool-customer.edit');
    });
    Route::group(['prefix' => 'alternative-producers-prices-import'], static function () {
        Route::get('/show', [ImportsToolController::class, 'showAlternativeProducers'])->name('margin-tool.alternative-prices');
    });
    Route::group(['prefix' => 'iveco-purchase-prices-import'], static function () {
        Route::get('/show', [ImportsToolController::class, 'showIvecoPurchasePrices']);
    });
    Route::group(['prefix' => 'iveco-stock-prices'], static function () {
        Route::get('/prices', [ImportsToolController::class, 'showIvecoStockPrices']);
    });

    Route::group(['prefix' => 'iveco-stock-prices-margin'], static function () {
        Route::get('/margin', [MarginToolController::class, 'showIvecoStockMargin']);
    });

    Route::group(['prefix' => 'iveco-big-db-import'], static function () {
        Route::get('/show', [ImportsToolController::class, 'showIvecoBigDb']);
    });
    Route::group(['prefix' => 'product-list'], static function () {
        Route::get('/show', [ProductListController::class, 'show']);
    });
    Route::group(['prefix' => 'augustin-group-import'], static function () {
        Route::get('/show', [AugustinGroupToolController::class, 'show']);
    });
    Route::group(['prefix' => 'augustin-group-margin'], static function () {
        Route::get('/margin', [AugustinGroupToolController::class, 'margin'])->name('augustin-group.margin');
    });
    Route::group(['prefix' => 'augustin-group-margin-bulky-parts'], static function () {
        Route::get('/margin-bulky-parts', [AugustinGroupToolController::class, 'marginBulkyParts'])->name('augustin-group.margin-bulky-parts');
    });
    Route::group(['prefix' => 'mec-diesel-import'], static function () {
        Route::get('/show', [\App\Http\Controllers\ImportToolControllers\MecDieselToolController::class, 'show']);
    });
    Route::group(['prefix' => 'mec-diesel-margin'], static function () {
        Route::get('/margin', [\App\Http\Controllers\ImportToolControllers\MecDieselToolController::class, 'margin'])->name('mec-diesel.margin');
    });
    Route::group(['prefix' => 'nrf-import'], static function () {
        Route::get('/show', [NRFToolController::class, 'show']);
    });
    Route::group(['prefix' => 'nrf-margin'], static function () {
        Route::get('/margin', [NRFToolController::class, 'margin'])->name('nrf.margin');
    });
    Route::group(['prefix' => 'mec-diesel-import'], static function () {
        Route::get('/show', [MecDieselToolController::class, 'show']);
    });
    Route::group(['prefix' => 'mec-diesel-margin'], static function () {
        Route::get('/margin', [MecDieselToolController::class, 'margin'])->name('mec-diesel.margin');
    });

    Route::group(['prefix' => 'febi-import'], static function () {
        Route::get('/show', [FebiBilsteinToolController::class, 'show']);
    });
    Route::group(['prefix' => 'febi-margin'], static function () {
        Route::get('/margin', [FebiBilsteinToolController::class, 'margin'])->name('febi-bilstein.margin');
    });

    Route::get('/ms-import/show', [MsToolController::class, 'show']);
    Route::get('/ms-margin/margin', [MsToolController::class, 'margin'])->name('motorservice.margin');
    Route::get('/ms-import-availability/availability', [MsToolController::class, 'availability']);
    Route::get('/cei-import-availability/availability', [CeiToolController::class, 'availability']);

    Route::get('/eminia-import/show', [EminiaToolController::class, 'show']);
    Route::get('/eminia-margin/margin', [EminiaToolController::class, 'margin'])->name('eminia.margin');

    Route::get('/casco-import/show', [CascoToolController::class, 'show']);
    Route::get('/casco-margin/margin', [CascoToolController::class, 'margin'])->name('casco.margin');

    Route::get('/special-turbo-import/show', [SpecialTurboToolController::class, 'show']);
    Route::get('/special-turbo-margin/margin', [SpecialTurboToolController::class, 'margin'])->name('special-turbo.margin');

    Route::get('/martex-import/show', [MartexToolController::class, 'show']);
    Route::get('/martex-margin/margin', [MartexToolController::class, 'margin'])->name('martex.margin');


    Route::get('/sabo-import/show', [SaboToolController::class, 'show']);
    Route::get('/sabo-margin/margin', [SaboToolController::class, 'margin'])->name('sabo.margin');
    Route::get('/sabo-availability/availability', [SaboToolController::class, 'availability']);

    Route::get('/remante-import/show', [RemanteToolController::class, 'show']);
    Route::get('/remante-margin/margin', [RemanteToolController::class, 'margin'])->name('remante.margin');

    Route::get('/oe-germany-import/show', [OeGermanyToolController::class, 'show']);
    Route::get('/oe-germany-margin/margin', [OeGermanyToolController::class, 'margin'])->name('oe-germany.margin');

    Route::get('/meat-doria-import/show', [MeatDoriaToolController::class, 'show']);
    Route::get('/meat-doria-margin/margin', [MeatDoriaToolController::class, 'margin'])->name('meat-doria.margin');

    Route::get('/abakus-import/show', [AbakusToolController::class, 'show']);
    Route::get('/abakus-margin/margin', [AbakusToolController::class, 'margin'])->name('abakus.margin');

    Route::get('/trucktec-import/show', [TrucktecToolController::class, 'show']);
    Route::get('/trucktec-margin/margin', [TrucktecToolController::class, 'margin'])->name('trucktec.margin');


    Route::get('/weight-import/show', [WeightToolController::class, 'show']);


    Route::group([
        'prefix' => 'statistics',
        'middleware' => 'buxus_user_permission:' . StatisticsBuxusUserPermission::TAG
    ],
        static function () {
            Route::get('/show', [StatisticsController::class, 'show'])->name('statistics.show');
            Route::get('/show/user/{userId}', [StatisticsController::class, 'showUser'])->name('statistics.show-user');
        });
    Route::group([
        'prefix' => 'extended-statistics',
        'middleware' => 'buxus_user_permission:' . StatisticsBuxusUserPermission::TAG
    ],
        static function () {
            Route::get('/show', [StatisticsController::class, 'showExtended'])->name('extended-statistics.show');
        });

    Route::group([
        'prefix' => 'sales-statistics',
        'middleware' => 'buxus_user_permission:' . StatisticsBuxusUserPermission::TAG
    ],
        static function () {
            Route::get('/show', [StatisticsController::class, 'showSales'])->name('sales-statistics.show');
        });
    Route::group(['prefix' => 'form'], static function () {
        Route::get('/complaints', [FormController::class, 'complaints'])->name('form.complaints');
        Route::get('/returns', [FormController::class, 'returns'])->name('form.returns');
    });

    Route::group([
        'prefix' => 'slow-moving-stock-statistics',
        'middleware' => 'buxus_user_permission:' . StatisticsBuxusUserPermission::TAG
    ],
        static function () {
            Route::get('/show', [StatisticsController::class, 'showSlowMovingStock'])->name('slow-moving-stock-statistics.show');
    });
});
