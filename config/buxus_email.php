<?php
return [
    /*
     * should all emails be logged by default ?
     */
    'log_all_emails' => true,

    /*
     * should images be embedded by default ?
     */
    'embed_images' => false,

    /*
     * *******************************************************************************************
     *
     * default transactional email type settings
     */

    /*
     * type of transport: php, smtp
     */
    'type' => env('MAIL_TRANSPORT_TYPE', 'php'),
    'sender' => '<EMAIL>',
    'sender_name' => 'Rinoparts',
    'do_not_set_sender_envelope' => false,

    'smtp_auth' => env('SMTP_AUTH', 'false'),
    'smtp_hostname' => env('MAIL_HOST', ''),
    'smtp_port' => env('MAIL_PORT', ''),
    'smtp_username' => env('MAIL_USERNAME', ''),
    'smtp_password' => env('MAIL_PASSWORD', ''),
    /*
     * SMTP secure connection: ssl, tls, <none>
     */
    'smtp_secure' => 'tls',

    /*
     * *******************************************************************************************
     *
     * BULK email type settings, used by mailinglist
     */

    /*
     * type of transport for bulk email: php, smtp
     */
    'bulk_type' => env('MAIL_TRANSPORT_TYPE', 'php'),
    'bulk_smtp_auth' => env('SMTP_AUTH', 'false'),
    'bulk_smtp_hostname' => env('MAIL_HOST', ''),
    'bulk_smtp_port' => env('MAIL_PORT', ''),
    'bulk_smtp_username' => env('MAIL_USERNAME', ''),
    'bulk_smtp_password' => env('MAIL_PASSWORD', ''),


    /*
     * *******************************************************************************************
     *
     * ERROR email type settings, used by error reporter
     */

    /*
     * type of transport for bulk email: php, smtp
     */
    'error_email_type' => env('MAIL_TRANSPORT_TYPE', 'php'),
    'error_email_smtp_auth' => env('SMTP_AUTH', 'false'),
    'error_email_smtp_hostname' => env('MAIL_HOST', ''),
    'error_email_smtp_port' => env('MAIL_PORT', ''),
    'error_email_smtp_username' => env('MAIL_USERNAME', ''),
    'error_email_smtp_password' => env('MAIL_PASSWORD', ''),

];
