<?php

return [
    'iveco_big_db' => [
        'import_path' => 'imports/iveco-original-big-db/iveco-big-db.xlsx',
        'import_previous_path' => 'imports/iveco-original-big-db/iveco-big-db-previous.xlsx',

        'price_property_tag' => \Buxus\Util\PropertyTag::ESHOP_EUR_IVECO_BIG_DB_PRICE_WITHOUT_VAT_TAG(),
        'import_code_property_tag' => \Buxus\Util\PropertyTag::IVECO_BIG_DB_IMPORT_CODE_TAG(),
        'availability_property_tag' => \Buxus\Util\PropertyTag::AVAILABILITY_BIG_DB_TAG(),
        'latest_import_property_tag' => \Buxus\Util\PropertyTag::IVECO_BIG_DB_LATEST_IMPORT_TAG(),

        'type' => \App\Imports::IVECO_ORIGINAL_BIG_DB
    ],

    'alternative_prices' => [
        'import_path' => 'imports/alternative-prices/*.xlsx',
        'import_previous_path' => 'imports/alternative-prices/*-previous.xlsx',

        'price_property_tag' => \Buxus\Util\PropertyTag::ALTERNATIVE_PRICE_WITHOUT_VAT_TAG(),
        'import_code_property_tag' => \Buxus\Util\PropertyTag::ALTERNATIVE_PRICES_CODE_TAG(),
        'latest_import_property_tag' => \Buxus\Util\PropertyTag::ALTERNATIVE_PRICES_LATEST_IMPORT_TAG(),

        'default_availability' => 1,
    ],

    'augustin_group' => [
        'import_path' => 'imports/augustin-group/*.xlsx',
        'import_previous_path' => 'imports/augustin-group/*-previous.xlsx',

        'update_path' => 'imports/external/augustin-group.csv',

        'price_property_tag' => \Buxus\Util\PropertyTag::AUGUSTIN_GROUP_PRICE_WITHOUT_VAT_TAG(false),
        'import_code_property_tag' => \Buxus\Util\PropertyTag::AUGUSTIN_GROUP_NUMBER_TAG(false),
        'latest_import_property_tag' => \Buxus\Util\PropertyTag::AUGUSTIN_GROUP_LATEST_IMPORT_TAG(false),

        'default_availability' => 1,

        'supplier_page_id' => \Buxus\Util\PageIds::getAugustinGroupSupplier(false),

        'ftp' => [
            'host' => env('AG_HOST'),
            'username' => env('AG_USERNAME'),
            'password' => env('AG_PASSWORD'),
            'remote_filename' => 'AG_rinoparts.csv'
        ],
    ],

    'mec_diesel' => [
        'import_path' => 'imports/mec-diesel/*.csv',
        'import_previous_path' => 'imports/mec-diesel/*-previous.csv',

        'update_path' => 'imports/external/mec_stock.txt',

        'price_property_tag' => \Buxus\Util\PropertyTag::MEC_DIESEL_PRICE_WITHOUT_VAT_TAG(false),
        'import_code_property_tag' => \Buxus\Util\PropertyTag::MEC_DIESEL_SUPPLIER_CODE_TAG(false),
        'latest_import_property_tag' => \Buxus\Util\PropertyTag::MEC_DIESEL_LATEST_IMPORT_TAG(false),

        'default_availability' => 1,

        'supplier_page_id' => \Buxus\Util\PageIds::getMecDieselSupplier(false),
    ],

    'nrf' => [
        'import_path' => 'imports/nrf/*.xlsx',
        'import_previous_path' => 'imports/nrf/*-previous.xlsx',

        'update_path' => 'imports/external/nrf_stock.txt',

        'price_property_tag' => \Buxus\Util\PropertyTag::NRF_PRICE_WITHOUT_VAT_TAG(false),
        'stock_property_tag' => \Buxus\Util\PropertyTag::NRF_STOCK_BALANCE_TAG(false),
        'import_code_property_tag' => \Buxus\Util\PropertyTag::NRF_SUPPLIER_CODE_TAG(false),
        'latest_import_property_tag' => \Buxus\Util\PropertyTag::NRF_LATEST_IMPORT_TAG(false),

        'default_availability' => 1,

        'supplier_page_id' => \Buxus\Util\PageIds::getNrfSupplier(false),

        'ftp' => [
            'host' => env('NRF_HOST'),
            'username' => env('NRF_USERNAME'),
            'password' => env('NRF_PASSWORD'),
            'remote_filename' => 'NrfStockInfoPOL.txt'
        ],
    ],

    'febi_bilstein' => [
        'import_path' => 'imports/febi_bilstein/*.xlsx',
        'import_previous_path' => 'imports/febi_bilstein/*-previous.xlsx',

        'price_property_tag' => \Buxus\Util\PropertyTag::FEBI_BILSTEIN_PRICE_WITHOUT_VAT_TAG(false),
        'stock_property_tag' => \Buxus\Util\PropertyTag::FEBI_BILSTEIN_STOCK_BALANCE_TAG(false),
        'import_code_property_tag' => \Buxus\Util\PropertyTag::FEBI_BILSTEIN_SUPPLIER_CODE_TAG(false),
        'latest_import_property_tag' => \Buxus\Util\PropertyTag::FEBI_BILSTEIN_LATEST_IMPORT_TAG(false),

        'default_availability' => 1,

        'supplier_page_id' => \Buxus\Util\PageIds::getFebiBilsteinSupplier(false),
    ],

    'motorservice' => [
        'import_path' => 'imports/motorservice/*.xlsx',
        'import_previous_path' => 'imports/motorservice/*-previous.xlsx',

        'price_property_tag' => \Buxus\Util\PropertyTag::MOTORSERVICE_PRICE_WITHOUT_VAT_TAG(false),
        'stock_property_tag' => \Buxus\Util\PropertyTag::MOTORSERVICE_STOCK_BALANCE_TAG(false),
        'import_code_property_tag' => \Buxus\Util\PropertyTag::MOTORSERVICE_SUPPLIER_CODE_TAG(false),
        'latest_import_property_tag' => \Buxus\Util\PropertyTag::MOTORSERVICE_LATEST_IMPORT_TAG(false),

        'default_availability' => 1,

        'supplier_page_id' => \Buxus\Util\PageIds::getMotorServiceSupplier(false),
    ],

    'motorservice_availability' => [
        'import_path' => 'imports/motorservice-availability/*.xlsx',
        'import_previous_path' => 'imports/motorservice-availability/*-previous.xlsx',

        'default_availability' => 1,

        'supplier_page_id' => \Buxus\Util\PageIds::getMotorServiceSupplier(false),
    ],

    'cei_availability' => [
        'import_path' => 'imports/cei-availability/*.xlsx',
        'import_previous_path' => 'imports/cei-availability/*-previous.xlsx',

        'default_availability' => 1,

        'supplier_page_id' => \Buxus\Util\PageIds::getCeiSupplier(false),
    ],

    'eminia' => [
        'import_path' => 'imports/eminia/*.xls',
        'import_previous_path' => 'imports/eminia/*-previous.xls',

        'update_path' => 'imports/eminia/FTP Rinoparts.xls',

        'default_availability' => 1,

        'supplier_page_id' => \Buxus\Util\PageIds::getEminiaSupplier(false),
    ],


    'casco' => [
        'import_path' => 'imports/casco/*.xlsx',
        'import_previous_path' => 'imports/casco/*-previous.xlsx',

        'update_path' => 'imports/casco/CASCO_1457_1.csv',

        'default_availability' => 1,

        'supplier_page_id' => \Buxus\Util\PageIds::getCascoSupplier(false),
    ],

    'special_turbo' => [
        'import_path' => 'imports/special_turbo/*.xlsx',
        'import_previous_path' => 'imports/special_turbo/*-previous.xlsx',

        'update_path' => 'imports/special_turbo_stock/10196.csv',

        'default_availability' => 1,

        'supplier_page_id' => \Buxus\Util\PageIds::getSpecialTurboSupplier(false),
    ],

    'martex' => [
        'import_path' => 'imports/martex/*.xlsx',
        'import_previous_path' => 'imports/martex/*-previous.xlsx',

        'update_path' => 'imports/martex_stock/OFERTA.txt',

        'default_availability' => 1,

        'supplier_page_id' => \Buxus\Util\PageIds::getMartexSupplier(false),
    ],

    'sabo' => [
        'import_path' => 'imports/sabo/*.xlsx',
        'import_previous_path' => 'imports/sabo/*-previous.xlsx',

        'default_availability' => 1,

        'supplier_page_id' => \Buxus\Util\PageIds::getSaboSupplier(false),
    ],

    'sabo_availability' => [
        'import_path' => 'imports/sabo-availability/*.xlsx',
        'import_previous_path' => 'imports/sabo-availability/*-previous.xlsx',

        'default_availability' => 1,

        'supplier_page_id' => \Buxus\Util\PageIds::getSaboSupplier(false),
    ],

    'remante' => [
        'import_path' => 'imports/remante_import/*.xlsx',
        'import_previous_path' => 'imports/remante_import/*-previous.xlsx',

        'default_availability' => 1,

        'supplier_page_id' => \Buxus\Util\PageIds::getRemanteSupplier(false),
    ],

    'remante_availability' => [
        'import_path' => 'imports/remante/*.xlsx',
        'import_previous_path' => 'imports/remante/*-previous.xlsx',

        'update_path' => 'imports/remante/remante.csv',

        'default_availability' => 1,

        'supplier_page_id' => \Buxus\Util\PageIds::getRemanteSupplier(false),
    ],

    'oe_germany' => [
        'import_path' => 'imports/oe_germany_import/*.xlsx',
        'import_previous_path' => 'imports/oe_germany_import/*-previous.xlsx',

        'default_availability' => 1,

        'supplier_page_id' => \Buxus\Util\PageIds::getOeGermanySupplier(false),
    ],

    'oe_germany_availability' => [
        'update_path' => 'imports/oe_germany/stock.csv',

        'default_availability' => 1,

        'supplier_page_id' => \Buxus\Util\PageIds::getOeGermanySupplier(false),

        'ftp' => [
            'host' => env('OE_GERMANY_HOST'),
            'username' => env('OE_GERMANY_USERNAME'),
            'password' => env('OE_GERMANY_PASSWORD'),
            'remote_filename' => 'export.csv'
        ],
    ],

    'meat_doria' => [
        'import_path' => 'imports/meat_doria_import/*.xlsx',
        'import_previous_path' => 'imports/meat_doria_import/*-previous.xlsx',

        'default_availability' => 1,

        'supplier_page_id' => \Buxus\Util\PageIds::getMeatDoriaSupplier(false),
    ],

    'meat_doria_availability' => [
        'update_path' => 'imports/meatdoria_stock/meatdoria.csv',

        'default_availability' => 1,

        'supplier_page_id' => \Buxus\Util\PageIds::getMeatDoriaSupplier(false),
    ],

    'abakus' => [
        'import_path' => 'imports/abakus_import/*.xlsx',
        'import_previous_path' => 'imports/abakus_import/*-previous.xlsx',

        'default_availability' => 1,

        'supplier_page_id' => \Buxus\Util\PageIds::getAbakusSupplier(false),
    ],

    'abakus_availability' => [
        'update_path' => 'imports/abakus_stock/stocklist.xls',

        'default_availability' => 1,

        'supplier_page_id' => \Buxus\Util\PageIds::getAbakusSupplier(false),
    ],

    'trucktec' => [
        'import_path' => 'imports/trucktec_import/*.xlsx',
        'import_previous_path' => 'imports/trucktec_import/*-previous.xlsx',

        'default_availability' => 1,

        'supplier_page_id' => \Buxus\Util\PageIds::getTrucktecSupplier(false),
    ],

    'trucktec_availability' => [
        'update_path' => 'imports/trucktec_stock/stocklist.xls',

        'default_availability' => 1,

        'supplier_page_id' => \Buxus\Util\PageIds::getTrucktecSupplier(false),
    ],

];
