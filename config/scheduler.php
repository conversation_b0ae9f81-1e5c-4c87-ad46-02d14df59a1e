<?php

return [
    'setup_function' => function (\Illuminate\Console\Scheduling\Schedule $scheduler) {
        if (App::environment('live')) {
            $scheduler
                ->command('fulltext:reindex all')
                ->dailyAt('02:00');

            $scheduler
                ->command('onix:import-products')
                ->cron('*/30 * * * *');
            $scheduler
                ->command('onix:export-orders')
                ->cron('*/2 * * * *');
            $scheduler
                ->command('onix:import-orders')
                ->cron('*/4 * * * *');
            $scheduler
                ->command('onix:import-invoices')
                ->cron('0 5,14,17 * * *');
            $scheduler
                ->command('onix:watch-dog')
                ->cron('*/5 8-18 * * *');

            $scheduler
                ->command('invoice:send-notices')
                ->dailyAt('15:00')
                ->when(function () {
                    return \DB::table('tblInvoicesUpdateLog')->whereDate('created_at', \Carbon\Carbon::now()->toDateString())->where('update_tag', \App\Invoice\Invoice::NOTICES_TAG)->count() == 0;
                });

            $scheduler
                ->command('google-sitemap:generate')
                ->dailyAt('02:00');

            $scheduler
                ->command('meat-doria:update-stock')
                ->dailyAt('03:20');

            $scheduler
                ->command('martex:update-stock-and-pricing')
                ->dailyAt('03:30');

            $scheduler
                ->command('special-turbo:update-stock-and-pricing')
                ->dailyAt('03:45');

            $scheduler
                ->command('abakus:update-stock')
                ->dailyAt('04:00');

            $scheduler
                ->command('trucktec:update-stock')
                ->dailyAt('04:10');

            $scheduler
                ->command('nrf:update-stock')
                ->dailyAt('04:15');

            $scheduler
                ->command('augustin-group:export-prices')
                ->dailyAt('04:30');

            $scheduler
                ->command('augustin-group:update-stock-and-pricing')
                ->dailyAt('05:15');

            $scheduler
                ->command('oe-germany:update-stock-and-pricing')
                ->dailyAt('05:30');

            $scheduler
                ->command('casco:update-stock')
                ->dailyAt('05:45');

            $scheduler
                ->command('enclosures:transactions-closure')
                ->dailyAt('05:55');

            $scheduler
                ->command('remante:update-stock-and-pricing')
                ->dailyAt('06:00');

            $scheduler
                ->command('eminia:update-stock-and-pricing')
                ->dailyAt('06:35');

            $scheduler
                ->command('mec-diesel:update-stock-and-pricing')
                ->dailyAt('08:15');

            $scheduler
                ->command('febi-bilstein:update-stock')
                ->dailyAt('17:00');

            $scheduler
                ->command('rinoparts:sps-delivery-tracking')
                ->dailyAt('17:20');

            $scheduler
                ->command('rinoparts:ppl-delivery-tracking')
                ->dailyAt('17:30');

            $scheduler->command('rinoparts:dpd-file-watcher')
                ->everyFifteenMinutes();


            $scheduler
                ->command('stock:log-daily-states')
                ->dailyAt('5:00');
        }

        if (App::environment('test') && config('onix.test_server_enable_cron')) {
            $scheduler
                ->command('onix:import-products')
                ->cron('10 7-20 * * *');
            $scheduler
                ->command('onix:import-products')
                ->cron('40 * * * *');
            $scheduler
                ->command('onix:export-orders')
                ->cron('*/2 * * * *');
            $scheduler
                ->command('onix:import-orders')
                ->cron('*/4 * * * *');
            $scheduler
                ->command('onix:import-invoices')
                ->twiceDaily(5, 14);

        }
    }
];

