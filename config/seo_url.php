<?php

use Buxus\SeoUrlLegacy\SeoUrl;

return [
    'setup_function' => static function (SeoUrl $seoUrl) {
        $seoUrl->setUrlPostix('');
        $seoUrl->setAddingIndex(true);
        $seoUrl->setAppendPageId(false);
        $seoUrl->setIgnoreProtocol(true);


        /*
         * use the setting of default language only if you use different SEO urls for
         * languages on the same domain
         */
//        $seoUrl->setDefaultLanguage(config('translate.default_language'));

        // Add page types with SEO URL
        try {
            $seoUrl->setHomePageId(\Buxus\Util\PageIds::getHomepage());

            // add multiple domain homepages in different trees
//            $seo_url->setDomainHomePageId(getenv('HOST'), \Buxus\Util\PageIds::getHomepage());
//            $seo_url->setDomainHomePageId('my.other.domain.com', 123);

        } catch (Exception $e) {
        }

        // add additional SEOurl for different domains
        // $seo_url->addDomain(getenv('HOST_CZ'), '_cz');
        $seoUrl->addDomain(getenv('HOST_CZ'), '_cz', 'cz');
        $seoUrl->addDomain(getenv('HOST_EN'), '_en', 'en');

        // , or different languages
        // $seo_url->addDomain(getenv('HOST_CZ'), '_cz', 'cz');

        // , or with different languages and transliteration
        // transliteration ids Any-Latin and Latin-ASCII
        // are automatically added to the end of the transliteration id
        // $seo_url->addDomain(getenv('HOST_BG'), '_bg', 'bg', 'Bulgarian-Latin/BGN');

        // enable SEO URL for all page types which have the seo_url_name property
        $seoPageTypes = \Buxus\Cache\Facades\GenericValueCache::get(
            'seo-url-page-types',
            static function () {
                $pageTypes = [];
                try {
                    $seoUrlProperty = \Buxus\Property\PropertyManager::getInstance()
                        ->getPropertyByTag('seo_url_name');
                    if ($seoUrlProperty) {
                        $pageTypes = \Buxus\Property\PropertyManager::getInstance()
                            ->getSupportedPageTypes($seoUrlProperty);
                    }
                } catch (Exception $e) {
                }
                return $pageTypes;
            },
            60
        );

        foreach ($seoPageTypes as $pageTypeId) {
            $seoUrl->addPageTyp($pageTypeId);
        }
    }
];
