<?php

return [
    /**
     * Strategy that should be used for categories that a product falls into
     */
//    'product_categorization_strategy' => 'buxus:product-catalog:product-categorization:strategy:parent-pages',
//    'product_categorization_strategy' => 'buxus:product-catalog:product-categorization:strategy:property-defined',
    'product_categorization_strategy' => 'buxus:product-catalog:product-categorization:strategy:parent-pages-and-property-defined',

    /**
     * Page types, which should be considered as categories, that are represented by `Eshop\Catalog\Product\Categorization\ProductCategoryInterface`
     */
    'product_categories' => [
        \Buxus\Util\PageTypeID::ESHOP_CATEGORY_ID(),
        \Buxus\Util\PageTypeID::ESHOP_SUBCATEGORY_ID(),
    ],

    /**
     * Product brand tag for project
     */
    'product_brand_tag' => \Buxus\Util\PropertyTag::ESHOP_ROLLER_BRAND_TAG(),


    'product_producer_tag' => \Buxus\Util\PropertyTag::ESHOP_ROLLER_PRODUCER_TAG()
];
