<?php

use FullTextSearch\FullTextSearch;
use FullTextSearch\FullTextSearchManager;

return [
    function (FullTextSearchManager $manager) {
        $backend_tag = 'eshop_documents';
        $index_tag = 'eshop_documents';
        $search_tag = 'eshop_documents';

        $search_core = env('SEARCH_CORE');

        $backend = new \App\FulltextSearch\EshopDocumentsSolrBackend(env('SEARCH_HOST'), env('SEARCH_PORT'), $search_core, $backend_tag);
        $backend->setOption(\App\FulltextSearch\EshopDocumentsSolrBackend::OPTION_TIMEOUT, '240');
        $backend->setImmediateCommit(false);
        $manager->addBackend($backend);

        $indexer = new \App\FulltextSearch\EshopDocumentsSearchIndex($index_tag);
        $indexer->addBackend($manager->getBackend($backend_tag));
        $indexer->setFieldTypes([
            'enclosure_record_id' => \App\FulltextSearch\ExtendedSearchIndex::FIELD_TYPE_INTEGER,
            'doc_record_id' => \App\FulltextSearch\ExtendedSearchIndex::FIELD_TYPE_INTEGER,
            'inserted' => \App\FulltextSearch\ExtendedSearchIndex::FIELD_TYPE_DATETIME,
            'onix_date_changed' => \App\FulltextSearch\ExtendedSearchIndex::FIELD_TYPE_DATETIME,
            'onix_date_document' => \App\FulltextSearch\ExtendedSearchIndex::FIELD_TYPE_DATETIME,
            'enclosure_type_id' => \App\FulltextSearch\ExtendedSearchIndex::FIELD_TYPE_INTEGER,
            'sum' => \App\FulltextSearch\ExtendedSearchIndex::FIELD_TYPE_FLOAT,
            'due_date' => \App\FulltextSearch\ExtendedSearchIndex::FIELD_TYPE_DATETIME,
            'payment_remaining' => \App\FulltextSearch\ExtendedSearchIndex::FIELD_TYPE_FLOAT,
            'payment_status' => \App\FulltextSearch\ExtendedSearchIndex::FIELD_TYPE_FLOAT,
            'sum_vat' => \App\FulltextSearch\ExtendedSearchIndex::FIELD_TYPE_FLOAT,
            'partner_id' => \App\FulltextSearch\ExtendedSearchIndex::FIELD_TYPE_INTEGER,


            'item_names' => \App\FulltextSearch\ExtendedSearchIndex::FIELD_TYPE_MULTIVALUED,
            'item_codes' => \App\FulltextSearch\ExtendedSearchIndex::FIELD_TYPE_MULTIVALUED,
            'buxus_order_vs' => \App\FulltextSearch\ExtendedSearchIndex::FIELD_TYPE_MULTIVALUED,
            'buxus_order_ids' => \App\FulltextSearch\ExtendedSearchIndex::FIELD_TYPE_MULTIVALUED,
            'invalid_products' => \App\FulltextSearch\ExtendedSearchIndex::FIELD_TYPE_MULTIVALUED,
            'ignored_external_codes' => \App\FulltextSearch\ExtendedSearchIndex::FIELD_TYPE_MULTIVALUED,
        ]);
        $manager->addIndex($indexer);

        $searchProvider = new \App\FulltextSearch\EshopDocumentsSearchProcessor([
            'backend' => $manager->getBackend($backend_tag),
            'index' => $manager->getIndex($index_tag),
        ]);

        $search = new FullTextSearch($search_tag);
        $search->setProvider($searchProvider);
        $search->setLimit(500);
        $manager->addSearch($search);

    }
];

