<?php

return [

    // set the calculate mode
    // CALCULATE_MODE_FINAL_PRICE - calculate from the checkouts final price
    // CALCULATE_MODE_WITHOUT_VOUCHER - calculate from the price before applying voucher
    'calculate_mode' => \Buxus\Eshop\FreeDelivery\FreeDeliveryManager::CALCULATE_MODE_FINAL_PRICE,

    /*
     * List of free delivery events for cases when you need to defined free delivery since
     * specific date / until specific date / for specific order min value
     */
    'events' => [
       // [
            /*
             * Event start date in `Y-m-d H:i` format
             */
//            'date_since' => '2020-02-11 00:00',

            /*
             * Event end date in `Y-m-d H:i` format
             */
//            'date_until' => '2020-02-17 23:59',

            /*
             * Purchase min value for eligible free delivery
             */
//            'min_value' => 20.00,

            /**
             * Supported sites
             */
//            'sites' => [
//
//            ],
       // ]
    ]
];
