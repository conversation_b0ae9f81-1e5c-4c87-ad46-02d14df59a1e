<div>
    <div>
        @if (session()->has('success'))
            <div class="alert alert-success">
                {{ session('success') }}
            </div>
        @endif
    </div>

    <div class="ibox">
        <div class="ibox-title">
            <h5>Upload nového súboru</h5>
        </div>
        <div class="ibox-content">
            <form wire:submit.prevent="create">
                <div>
                    <label>
                        Súbor s dátami produktov
                        <input type="file" wire:model="file">
                    </label>
                    <div wire:loading wire:target="file" style="color: red;">Nahráva sa</div>
                    @error('file') <span class="alert alert-danger">{{ $message }}</span> @enderror
                </div>

                <div>
                    <label>
                        Súbor s OE číslami
                        <input type="file" wire:model="crossfile">
                    </label>
                    <div wire:loading wire:target="crossfile" style="color: red;">Nahráva sa</div>
                    @error('crossfile') <span class="alert alert-danger">{{ $message }}</span> @enderror
                </div>

                <div class="d-flex">
                    <div class="ms-auto">
                        <x-buxus::forms.button type="button" text="Odložený import" wire:click="createDelayed"
                                               class="btn btn-primary"/>
                        <x-buxus::forms.button wire:model="processing" type="submit" text="Importovať"
                                               class="btn btn-primary"/>
                    </div>

                    <div wire:loading wire:target="processing">
                        Prebieha import
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
