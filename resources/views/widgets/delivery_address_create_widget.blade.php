@php
    $address = $config['address'];
@endphp
<div class="modal fade" id="deliveryAddressModalCreate" tabindex="-1" role="dialog"
     aria-labelledby="deliveryAddressModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title"
                    id="deliveryAddressModalLabel">{{ \Trans::str('user', 'Vytvorenie adresy') }}</h4>
            </div>
            <div class="modal-body">
                <form action="{{ route('delivery-address.create') }}" method="POST" class="delivery-address-change"
                      id="delivery-address-change-{{ $address->id }}">
                    @csrf
                    <label for="fullname">{{ \Trans::str('user', '<PERSON><PERSON> a priez<PERSON>') }}</label>
                    <input type="text" class="form-control" name="fullname" id="fullname">
                    <label for="company_name">{{ \Trans::str('user', 'Firma') }}*</label>
                    <input type="text" class="form-control" name="company_name" id="company_name" required>
                    <label for="delivery_phone">{{ \Trans::str('user', 'Telefón') }}*</label>
                    <input type="text" class="form-control" name="delivery_phone" id="delivery_phone" required>
                    <label for="street">{{ \Trans::str('user', 'Adresa') }}*</label>
                    <input type="text" class="form-control" name="street" id="street" required>
                    <label for="city">{{ \Trans::str('user', 'Mesto') }}*</label>
                    <input type="text" class="form-control" name="city" id="city" required>
                    <label for="zip">{{ \Trans::str('user', 'PSČ') }}*</label>
                    <input type="text" class="form-control" name="zip" id="zip" required>
                    <label for="country">{{ \Trans::str('user', 'Krajina') }}*</label>
                    <select name="country" id="country" class="form-control" required>
                        @foreach((array) $countries as $key => $country)
                            <option value="{{ $key }}">{{ $country }}</option>
                        @endforeach
                    </select>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default"
                        data-dismiss="modal">{{ \Trans::str('common', 'Zavrieť') }}</button>
                <button type="submit" form="delivery-address-change-{{ $address->id }}"
                        class="btn btn-primary">{{ \Trans::str('common', 'Vytvoriť') }}</button>
            </div>
        </div>
    </div>
</div>
