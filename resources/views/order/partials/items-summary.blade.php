<div class="row">
    <div class="col-10">
        <table class="w-100">
            <tr class="heading-row">
                <th>{{ \Trans::str('common', 'ID') }}</th>
                <th>{{ \Trans::str('common', 'Názov') }}</th>
                <th>{{ \Trans::str('common', 'Množstvo') }}</th>
                <th>{{ \Trans::str('common', 'Cena/ks bez DPH') }}</th>
                <th>{{ \Trans::str('common', 'Cena celkom bez DPH') }}</th>
            </tr>
            @foreach($items as $item)
                <tr class="text-center">
                    <td>{{ $item->getPageId() }}</td>
                    <td>{{ $item->getProductName() }}</td>
                    <td>{{ $item->getAmount() }}</td>
                    <td>{{ PriceViewer::formatRawPrice($item->getItemPriceWithoutVAT()) }}</td>
                    <td>{{ PriceViewer::formatRawPrice($item->getTotalPriceWithoutVAT()) }}</td>
                </tr>
            @endforeach
            <tr class="price-row">
                <td></td>
                <td></td>
                <td></td>
                <td class="text-left" align="left"><strong style="color:#000;">{{ \Trans::str('cart','Suma celkom bez DPH') }}</strong></td>
                <td><strong
                        style="color:#000;">{{ PriceViewer::formatRawPrice($order->getTotalPriceWithoutVat()) }}</strong>
                </td>
            </tr>
        </table>
    </div>
</div>
