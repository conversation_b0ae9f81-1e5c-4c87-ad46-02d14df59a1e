<header id="main-header" class="main-header">
    <div class="container">
        <a href="/" class="logo">
            <svg class="icon">
                <use xlink:href="#sprite-logo"></use>
            </svg>
        </a>

        <div class="mobile-header-icon-buttons">
            <button id="search-toggle-mobile" class="btn-icon-only icon-button-mobile">
                <svg class="icon">
                    <use xlink:href="#sprite-lens"></use>
                </svg>
            </button>
        </div>

        <button id="main-nav-toggle-mobile" class="btn-icon menu-button">
            <svg class="icon-open">
                <use xlink:href="#sprite-menu"></use>
            </svg>
            <svg class="icon-close">
                <use xlink:href="#sprite-cancel"></use>
            </svg>
            <span class="content">{{ Trans::str('eshop', 'Menu') }}</span>
        </button>

        <div class="mobile-navigation-wrap">

            @if(\WebUserAuthentication::isAuthenticated())
                <div class="mobile-account">
                    <a href="{!! \Buxus\Util\Url::page(\Buxus\Util\PageIds::getAuthKlientskaZona())  !!}"
                       class="btn btn-icon btn-bordered login-button" type="button">
                        <svg class="icon">
                            <use xlink:href="#sprite-avatar"></use>
                        </svg>
                        {{ Trans::str('eshop', 'Konto') }}
                    </a>

                    <a href="{!! \Buxus\Util\Url::page(Buxus\Util\PageIds::getCart1Contents()) !!}"
                       class="btn btn-icon btn-bordered login-button" type="button">
                        <svg class="icon">
                            <use xlink:href="#sprite-shopping-cart"></use>
                        </svg>
                        {{ Trans::str('eshop', 'Košík') }}
                        @if(\WebUserAuthentication::isAuthenticated() && \ShoppingCart::getItemCount() > 0)
                            <span class="badge shopping-cart-count">
                                    {{ \ShoppingCart::getItemCount() }}
                                </span>
                        @endif
                    </a>
                </div>
            @else
                <div class="mobile-account">
                    <a href="{!! \Buxus\Util\Url::taggedPage(\Authentication\AuthenticationPages::LOGIN)  !!}"
                       class="btn btn-lighter login-button" type="button">
                        {{ Trans::str('authenticate', 'Prihlásenie') }}
                    </a>

                    <a href="{!! \Buxus\Util\Url::taggedPage(\Authentication\AuthenticationPages::REGISTRATION) !!}"
                       class="btn btn-lighter login-button" type="button">
                        {{ Trans::str('authenticate', 'Registrácia') }}
                    </a>
                </div>
            @endif


            @section('categories')
                @widget('HeaderCategoriesMenuWidget')
            @show

            @section('search')
                @widget('HeaderSearchWidget')
            @show

            <div class="double-floor-section">
                <div class="site-settings">
                    @section('language-select')
                        @widget('HeaderLanguageSelectWidget')
                    @show

                    @if(!\WebUserAuthentication::isAuthenticated())
                        @if(config('krabica.modules.authentication'))
                            <a href="{!! \Buxus\Util\Url::taggedPage(\Authentication\AuthenticationPages::LOGIN)  !!}"
                               class="btn btn-lighter login-button" type="button" data-testing="LOGIN">
                                <svg class="icon fill-primary">
                                    <use xlink:href="#sprite-avatar"></use>
                                </svg>
                                <span>{{ Trans::str('authenticate', 'Prihlásiť/registrovať') }}</span>
                            </a>
                        @endif
                    @else
                        <div class="dropdown header-dropdown header-dropdown-right">
                            <button class="btn btn-white dropdown-toggle login-button" type="button"
                                    data-toggle="dropdown" aria-expanded="false">
                                <svg class="icon">
                                    <use xlink:href="#sprite-avatar"></use>
                                </svg>
                                {{ \WebUserAuthentication::getUser()->getHeaderName() }}
                                @if(\App\Models\WebUserNotification::hasNotification())
                                    <span class="badge notification-badge notification-count-all">{{ \App\Models\WebUserNotification::getNotificationsCount() }}</span>
                                @endif
                                <span class="caret"></span>
                            </button>

                            <ul class="dropdown-menu list-of-languages">
                                <li>
                                    <a class="dropdown-item"
                                       href="{!! \Buxus\Util\Url::page(\Buxus\Util\PageIds::getAuthKlientskaZona())  !!}">
                                        <span>{{ Trans::str('eshop', 'Profil') }}</span>
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item"
                                       href="{!! \Buxus\Util\Url::page(\Buxus\Util\PageIds::getAuthMyOrders())  !!}">
                                        <span>{{ Trans::str('eshop', 'Objednávky') }}</span>
                                        @if(\App\Models\WebUserNotification::hasNotification(config('notifications.types.order')))
                                                <span class="badge notification-badge notification-count-order">{{ \App\Models\WebUserNotification::getNotificationsCount(config('notifications.types.order')) }}</span>
                                        @endif
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item"
                                       href="{!! \Buxus\Util\Url::page(\Buxus\Util\PageIds::getInvoices())  !!}">
                                        <span>{{ Trans::str('user', 'Faktúry') }}</span>
                                        @if(\App\Models\WebUserNotification::hasNotification(config('notifications.types.invoice')))
                                            <span class="badge notification-badge notification-count-invoice">{{ \App\Models\WebUserNotification::getNotificationsCount(config('notifications.types.invoice')) }}</span>
                                        @endif
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item"
                                       href="{!! \Buxus\Util\Url::page(\Buxus\Util\PageIds::getCreditnotes())  !!}">
                                        <span>{{ Trans::str('user', 'Dobropisy') }}</span>
                                        @if(\App\Models\WebUserNotification::hasNotification(config('notifications.types.credit_note')))
                                            <span class="badge notification-badge notification-count-credit-note">{{ \App\Models\WebUserNotification::getNotificationsCount(config('notifications.types.credit_note')) }}</span>
                                        @endif
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item"
                                       href="{!! \Buxus\Util\Url::page(\Buxus\Util\PageIds::getDeliveryNotes())  !!}">
                                        <span>{{ Trans::str('user', 'Dodacie listy') }}</span>
                                        @if(\App\Models\WebUserNotification::hasNotification(config('notifications.types.delivery_note')))
                                            <span class="badge notification-badge notification-count-delivery-note">{{ \App\Models\WebUserNotification::getNotificationsCount(config('notifications.types.delivery_note')) }}</span>
                                        @endif
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item"
                                       href="{!! \Buxus\Util\Url::page(\Buxus\Util\PageIds::getReturns())  !!}">
                                        <span>{{ Trans::str('user', 'Vrátenie tovaru') }}</span>
                                        @if(\App\Models\WebUserNotification::hasNotification(config('notifications.types.return')))
                                            <span class="badge notification-badge notification-count-return">{{ \App\Models\WebUserNotification::getNotificationsCount(config('notifications.types.return')) }}</span>
                                        @endif
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item"
                                       href="{!! \Buxus\Util\Url::page(\Buxus\Util\PageIds::getComplaints())  !!}">
                                        <span>{{ Trans::str('user', 'Reklamácie') }}</span>
                                        @if(\App\Models\WebUserNotification::hasNotification(config('notifications.types.complaint')))
                                            <span class="badge notification-badge notification-count-complaint">{{ \App\Models\WebUserNotification::getNotificationsCount(config('notifications.types.complaint')) }}</span>
                                        @endif
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item"
                                       href="{!! \Buxus\Util\Url::page(\Buxus\Util\PageIds::getAuthChangeUserProfile())  !!}">
                                        <span>{{ Trans::str('user', 'Fakturačné údaje') }}</span>
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item"
                                       href="{!! \Buxus\Util\Url::taggedPage(\Authentication\AuthenticationPages::LOGOUT)  !!}">
                                        <span><strong>{{ Trans::str('authenticate', 'Odhlásiť') }}</strong></span>
                                    </a>
                                </li>
                            </ul>
                        </div>

                        <a href="{!! \Buxus\Util\Url::page(Buxus\Util\PageIds::getCart1Contents()) !!}"
                           class="btn btn-white btn-icon shopping-cart">
                            <svg class="icon">
                                <use xlink:href="#sprite-shopping-cart"></use>
                            </svg>
                            {{ Trans::str('eshop', 'Košík') }}
                            @if(\WebUserAuthentication::isAuthenticated())
                                <span class="badge shopping-cart-count @if(\ShoppingCart::getItemCount() <= 0) d-none @endif">
                                    {{ \ShoppingCart::getItemCount() }}
                                </span>
                            @endif
                        </a>
                    @endif


                    <!-- ?php if (config('krabica.modules.authentication')): ? -->
                    <!-- ?= $this->action('links', 'authentication') ? -->
                    <!-- ?php endif; ? -->

                </div>

                @section('top-menu')
                    @widget('HeaderTopMenuWidget')
                @show

                @section('language-select-mobile')
                    @widget('HeaderLanguageSelectMobileWidget')
                @show
            </div>
        </div>
    </div>
</header>

@jsBegin
<script>
    require(['jquery'], function ($) {
        $(function () {
            $(document).on('eshop.command', function (e, command, payload, element) {
                $.ajax({
                    'url': '{{ route('cart.recount') }}',
                    'success': function (data) {
                        $('.badge.shopping-cart-count').removeClass('d-none');
                        $('.badge.shopping-cart-count').html(data);
                    }
                })
            });
        });
    });
</script>
@jsEnd
