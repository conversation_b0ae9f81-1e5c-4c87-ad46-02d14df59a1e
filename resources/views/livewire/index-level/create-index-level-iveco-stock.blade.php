<div>
    <div>
        @if (session()->has('success'))
            <div class="alert alert-success">
                {{ session('success') }}
            </div>
        @endif
    </div>

    <div class="ibox">
        <div class="ibox-content">
            <form wire:submit.prevent="create">
                <x-buxus::forms.number name="index_level_from" wire:model.lazy="index_level_from" step="0.01"
                                       label="Rozdiel oproti cene (%)"/>
                @error('index_level_from') <span class="alert alert-danger">{{ $message }}</span> @enderror
                <x-buxus::forms.number name="additional_price_increase" wire:model.lazy="additional_price_increase"
                                       step="0.01" label="Dodatočn<PERSON> navýšenie (%)"/>
                @error('additional_price_increase') <span class="alert alert-danger">{{ $message }}</span> @enderror
                <x-buxus::forms.number name="additional_price_increase_eu"
                                       wire:model.lazy="additional_price_increase_eu" step="0.01"
                                       label="<PERSON><PERSON>to<PERSON><PERSON><PERSON> nav<PERSON> (%) (EÚ)"/>
                @error('additional_price_increase_eu') <span class="alert alert-danger">{{ $message }}</span> @enderror
                <div class="d-flex">
                    <div class="ms-auto">
                        <x-buxus::forms.button type="button" wire:click="changeStatus"
                                               text="{{ ($indexLevelStatus ? 'Vypnúť' : 'Zapnúť') . ' cenové hladiny' }}"/>
                        <x-buxus::forms.button type="submit" class="btn btn-primary" text="Pridať cenovú hladinu"/>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
