<div>
    <div class="mt-3">
        <div class="d-flex">
            <h4>@str('user', 'Zoznam faktúr')</h4>
            <div style="margin-left: auto; align-items: baseline;">
                <label for="after_due_date">
                    <input type="checkbox" wire:model="after_due_date">
                    <small style="text-decoration: underline">@str('user', 'Zobraziť len faktúry po splatnosti')</small>
                </label>
            </div>
        </div>
        <div class="d-flex my-2">
            <input type="text" wire:model="invoice_search" class="form-control"
                   placeholder="@str('user', 'Vyhľadávanie podľa čísla faktúry')">
            <input type="text" wire:model="product_search" class="form-control"
                   placeholder="@str('user', 'Vyhľadávanie podľa Interného alebo OE čísla')">
        </div>
        @if ($invoices->count() > 0)
            <div class="table-responsive">
                <table class="order-list js-invoice-table table invoice-table">
                    <tr>
                        <th></th>
                        <th>@str('user', 'Číslo faktúry')</th>
                        <th>@str('user', 'Dátum vyhotovenia')</th>
                        <th>@str('user', 'Dátum splatnosti')</th>
                        <th>@str('user', 'Dní po splatnosti')</th>
                        <th>@str('user', 'Suma faktúry')</th>
                        <th>@str('user', 'Zostáva uhradiť')</th>
                        <th>@str('user', 'Stav faktúry')</th>
                    </tr>
                    <tbody>
                    @foreach ($invoices as $invoice)
                        @php
                            $invoice = \App\Invoice\Facades\InvoiceFactory::getInvoiceForId($invoice->enclosure_record_id);
                        @endphp
                        <tr class="<?= \App\Models\WebUserNotification::hasNotification(config('notifications.types.invoice'), $invoice->doc_record_id) ? 'has-notification':'' ?>" data-item-id="<?= $invoice->doc_record_id ?>">
                            <td class="notification-td"><span></span></td>
                            <td class="acc js-invoice-filter"><?= $invoice->vs ?></td>
                            <td>{{ date('d. m. Y', strtotime($invoice->onix_date_document)) }}</td>
                            <td>{{ empty($invoice->due_date) ? '' : date('d. m. Y', strtotime($invoice->due_date)) }}</td>
                            <td>{{ $invoice->getInvoiceAfterDueDateDiff() }}</td>
                            @if (\BuxusSite::site() == 'sk')
                                <td>{{ PriceViewer::formatRawPrice((float)$invoice->sum_vat, $invoice->currency) }}</td>
                            @else
                                <td>{{ PriceViewer::formatRawPrice((float)$invoice->sum, $invoice->currency) }}</td>
                            @endif
                            <td>{{ PriceViewer::formatRawPrice((float)$invoice->payment_remaining, $invoice->currency) }}</td>
                            <td>{{ $invoice->getPaymentStateToShow() }}</td>
                        </tr>
                        <tr>
                            <td colspan="8" class="actions-row actions-row--invoices">
                                <div class="actions">
                                        <a href="{{!empty($invoice->enclosure_record_id)
                            ? route('download.invoice', ['enclosureRecordId' => $invoice->enclosure_record_id])
                            : '#' }}" class="btn btn-primary">@str('user', 'Stiahnuť')</a>
                                </div>
                            </td>
                        </tr>
                    @endforeach
                    </tbody>
                </table>
            </div>
            <div class="d-flex">
                <div class="mx-auto">
                    {{ $invoices->appends(request()->all())->links() }}
                </div>
            </div>
        @else
            <p>@str('user', 'Pre toto konto ešte neexistujú žiadne faktúry.')</p>
        @endif
    </div>
</div>
