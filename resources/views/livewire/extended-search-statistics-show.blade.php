<div>
    <div class="ibox">
        <div class="ibox-title">
            <h5>Vyhľadávanie</h5>
        </div>
        <div class="ibox-content">
            <div class="my-2">
                <div class="d-flex">
                    <form class="d-flex w-100">
                        <input type="date" class="form-control mx-2"
                               value="{{ request(\App\Http\Livewire\ExtendedSearchStatisticsShow::EXISTENT_DATE_FROM) }}"
                               name="{{ \App\Http\Livewire\ExtendedSearchStatisticsShow::EXISTENT_DATE_FROM }}"
                               wire:model="{{ \App\Http\Livewire\ExtendedSearchStatisticsShow::EXISTENT_DATE_FROM }}">
                        <input type="date" class="form-control mx-2"
                               value="{{ request(\App\Http\Livewire\ExtendedSearchStatisticsShow::EXISTENT_DATE_TO) }}"
                               name="{{ \App\Http\Livewire\ExtendedSearchStatisticsShow::EXISTENT_DATE_TO }}"
                               wire:model="{{ \App\Http\Livewire\ExtendedSearchStatisticsShow::EXISTENT_DATE_TO }}">
                        <button class="btn btn-primary mx-2">Filtrovať</button>
                    </form>
                    <button class="btn btn-primary mx-2" wire:click="export">Export</button>
                </div>
            </div>
            <table>
                <th>Produkt</th>
                <th>Výrobca</th>
                <th>Hlavný kód</th>
                <th>Onix číslo</th>
                <th>Podľa kódu</th>
                <th>Počet vyhľadávaní</th>
                <th>Obrázok</th>
                @foreach($logs as $log)
                    <tr>
                        <td>{{ $log->title }}</td>
                        <td>{{ $log->producer }}</td>
                        <td>{{ $log->main_code }}</td>
                        <td>{{ $log->onix_number }}</td>
                        <td>{{ $log->search_term }}</td>
                        <td>{{ $log->sum }}</td>
                        <td>
                            @if($log->image)
                                <p><span class="status-icon check">&check;</span></p>
                            @else
                                <p><span class="status-icon times">&times;</span></p>
                            @endif
                        </td>
                    </tr>
                @endforeach
            </table>
        </div>
        <div class="d-flex">
            <div class="mx-auto">
                {!! $logs->appends(request()->all())->links() !!}
            </div>
        </div>
    </div>
</div>
