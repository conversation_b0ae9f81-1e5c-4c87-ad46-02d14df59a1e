<div>
    <div>
        @if (session()->has('success'))
            <div class="alert alert-success">
                {{ session('success') }}
            </div>
        @endif
    </div>

    <div class="ibox">
        <div class="ibox-content">
            <table style="width: 50%; border-spacing: 0 30px;">
                <tr>
                    <th></th>
                    <th>Cena od (€)</th>
                    <th><PERSON><PERSON><PERSON> (%)</th>
                    <th><PERSON><PERSON><PERSON> (EÚ) (%)</th>
                    <th></th>
                </tr>
                @foreach($marginLevels as $i => $marginLevel)
                    <tr>
                        <td class="pe-1">
                            <x-buxus::forms.button id="pick-delivery-place" class="btn-link" text="X"
                                                   modal="sureToDelete{{ $marginLevel->id }}"/>
                            <x-buxus::components.modal wire:ignore.self
                                                       title="Naozaj ch<PERSON>š zmazať cenovú hladinu od {{ $marginLevel->price_from }} €?"
                                                       id="sureToDelete{{ $marginLevel->id }}">
                                <livewire:margin-level-delete :marginLevel="$marginLevel" :wire:key="time() . $loop->index"/>
                            </x-buxus::components.modal>
                        </td>
                        <form
                            wire:submit.prevent="update({{ $marginLevel }}, Object.fromEntries(new FormData($event.target)))">
                            <td class="pe-5 py-2">
                                <x-buxus::forms.number name="price_from" value="{{ $marginLevel->price_from }}"/>
                            </td>
                            <td class="pe-5 py-2">
                                <x-buxus::forms.number name="margin" value="{{ $marginLevel->margin }}"/>
                            </td>
                            <td class="pe-5 py-2">
                                <x-buxus::forms.number name="margin_eu" value="{{ $marginLevel->margin_eu }}"/>
                            </td>
                            <td class="pe-5 py-2">
                                <x-buxus::forms.button type="submit" class="btn-link" text="Aktualizovať"/>
                            </td>
                        </form>
                    </tr>
                @endforeach
            </table>
        </div>
    </div>
</div>
